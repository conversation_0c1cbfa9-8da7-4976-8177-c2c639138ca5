/**
 * 会话数据同步服务
 * 实现多浏览器标签页之间的会话数据同步
 * 利用Redis的发布订阅功能和浏览器的BroadcastChannel API
 */

import { getRedisAdapter } from './redisHttpAdapter.js'
import { getChannelName } from '../config/redis.js'

/**
 * 会话同步服务类
 */
class SessionSyncService {
  constructor() {
    this.redis = getRedisAdapter()
    this.isEnabled = false
    this.userId = 'default_user' // 默认用户ID
    
    // 浏览器标签页间通信
    this.broadcastChannel = null
    this.setupBroadcastChannel()
    
    // 事件监听器
    this.eventListeners = {
      sessionUpdate: [],
      sessionDelete: [],
      sessionCreate: [],
      syncError: []
    }
    
    // 同步状态
    this.syncStatus = {
      enabled: false,
      lastSyncTime: null,
      pendingUpdates: 0,
      errors: []
    }
    
    // 防抖定时器
    this.debounceTimers = new Map()
  }

  /**
   * 设置浏览器标签页间通信
   */
  setupBroadcastChannel() {
    if (typeof BroadcastChannel !== 'undefined') {
      this.broadcastChannel = new BroadcastChannel('bodorai_session_sync')
      
      this.broadcastChannel.addEventListener('message', (event) => {
        this.handleBroadcastMessage(event.data)
      })
      
      console.log('BroadcastChannel已设置，支持标签页间同步')
    } else {
      console.warn('浏览器不支持BroadcastChannel，无法进行标签页间同步')
    }
  }

  /**
   * 启用同步服务
   */
  async enable() {
    try {
      // 检查Redis连接
      const connected = await this.redis.ping()
      if (!connected) {
        throw new Error('Redis连接不可用')
      }
      
      this.isEnabled = true
      this.syncStatus.enabled = true
      this.syncStatus.lastSyncTime = new Date()
      
      console.log('会话同步服务已启用')
      this.emit('syncEnabled')
      
      return true
    } catch (error) {
      console.error('启用会话同步服务失败:', error)
      this.syncStatus.errors.push({
        timestamp: new Date(),
        error: error.message
      })
      this.emit('syncError', error)
      return false
    }
  }

  /**
   * 禁用同步服务
   */
  disable() {
    this.isEnabled = false
    this.syncStatus.enabled = false
    
    // 清除所有防抖定时器
    this.debounceTimers.forEach(timer => clearTimeout(timer))
    this.debounceTimers.clear()
    
    console.log('会话同步服务已禁用')
    this.emit('syncDisabled')
  }

  /**
   * 发布会话更新事件
   */
  async publishSessionUpdate(moduleKey, sessionId, action, sessionData = null) {
    if (!this.isEnabled) return false

    try {
      const message = {
        type: 'session_update',
        action, // 'create', 'update', 'delete', 'switch'
        moduleKey,
        sessionId,
        sessionData,
        userId: this.userId,
        timestamp: new Date().toISOString(),
        source: 'redis'
      }

      // 发布到Redis
      await this.redis.publish('sessionUpdate', message)
      
      // 广播到其他标签页
      this.broadcastToTabs(message)
      
      this.syncStatus.lastSyncTime = new Date()
      console.log(`已发布会话${action}事件:`, { moduleKey, sessionId, action })
      
      return true
    } catch (error) {
      console.error('发布会话更新事件失败:', error)
      this.syncStatus.errors.push({
        timestamp: new Date(),
        error: error.message
      })
      this.emit('syncError', error)
      return false
    }
  }

  /**
   * 清理消息数据以支持结构化克隆
   * @param {Object} message - 原始消息对象
   * @returns {Object} 清理后的消息对象
   */
  sanitizeMessageForBroadcast(message) {
    const sanitized = { ...message }

    // 如果有 sessionData，需要清理其中的不可序列化属性
    if (sanitized.sessionData) {
      sanitized.sessionData = this.sanitizeSessionData(sanitized.sessionData)
    }

    return sanitized
  }

  /**
   * 清理会话数据（移除不可序列化的属性）
   * @param {Object} sessionData - 原始会话数据
   * @returns {Object} 清理后的会话数据
   */
  sanitizeSessionData(sessionData) {
    if (!sessionData) return sessionData

    // 清理消息数组中的不可序列化属性
    const sanitizedMessages = (sessionData.messages || []).map(message => {
      const sanitizedMessage = { ...message }

      // 转换 Date 对象为 ISO 字符串
      if (sanitizedMessage.timestamp instanceof Date) {
        sanitizedMessage.timestamp = sanitizedMessage.timestamp.toISOString()
      }

      // 清理推理步骤数组
      const sanitizedReasoningSteps = sanitizedMessage.reasoningSteps ?
        sanitizedMessage.reasoningSteps.map(step => ({
          title: step.title,
          description: step.description,
          icon: step.icon,
          status: step.status,
          duration: step.duration,
          type: step.type
        })) : undefined

      // 清理推理数组
      const sanitizedReasoning = sanitizedMessage.reasoning ?
        sanitizedMessage.reasoning.map(item => ({
          title: item.title,
          description: item.description,
          type: item.type,
          icon: item.icon
        })) : undefined

      // 移除可能的函数或其他不可序列化的属性
      // 保留基本的消息属性
      return {
        id: sanitizedMessage.id,
        type: sanitizedMessage.type,
        content: sanitizedMessage.content,
        timestamp: sanitizedMessage.timestamp,
        loading: sanitizedMessage.loading,
        streaming: sanitizedMessage.streaming,
        streamingContent: sanitizedMessage.streamingContent,
        error: sanitizedMessage.error,
        module: sanitizedMessage.module,
        reasoning: sanitizedReasoning,
        reasoningSteps: sanitizedReasoningSteps,
        reasoningActive: sanitizedMessage.reasoningActive,
        currentReasoningStep: sanitizedMessage.currentReasoningStep,
        thinkingContent: sanitizedMessage.thinkingContent
      }
    })

    return {
      id: sessionData.id,
      title: sessionData.title,
      module: sessionData.module,
      messages: sanitizedMessages,
      createdAt: sessionData.createdAt instanceof Date ? sessionData.createdAt.toISOString() : sessionData.createdAt,
      updatedAt: sessionData.updatedAt instanceof Date ? sessionData.updatedAt.toISOString() : sessionData.updatedAt,
      pinned: Boolean(sessionData.pinned)
    }
  }

  /**
   * 广播消息到其他标签页
   */
  broadcastToTabs(message) {
    if (this.broadcastChannel) {
      try {
        // 清理消息数据，确保可以被结构化克隆
        const sanitizedMessage = this.sanitizeMessageForBroadcast({
          ...message,
          source: 'broadcast'
        })

        this.broadcastChannel.postMessage(sanitizedMessage)
      } catch (error) {
        console.error('广播消息到标签页失败:', error)
      }
    }
  }

  /**
   * 处理广播消息
   */
  handleBroadcastMessage(message) {
    if (message.userId !== this.userId) return
    
    console.log('收到标签页广播消息:', message)
    
    // 防抖处理，避免频繁更新
    const debounceKey = `${message.moduleKey}_${message.sessionId}_${message.action}`
    
    if (this.debounceTimers.has(debounceKey)) {
      clearTimeout(this.debounceTimers.get(debounceKey))
    }
    
    const timer = setTimeout(() => {
      this.processSessionUpdate(message)
      this.debounceTimers.delete(debounceKey)
    }, 100) // 100ms防抖
    
    this.debounceTimers.set(debounceKey, timer)
  }

  /**
   * 处理会话更新
   */
  processSessionUpdate(message) {
    const { action, moduleKey, sessionId, sessionData } = message
    
    try {
      switch (action) {
        case 'create':
          this.emit('sessionCreate', { moduleKey, sessionId, sessionData })
          break
        case 'update':
          this.emit('sessionUpdate', { moduleKey, sessionId, sessionData })
          break
        case 'delete':
          this.emit('sessionDelete', { moduleKey, sessionId })
          break
        case 'switch':
          this.emit('sessionSwitch', { moduleKey, sessionId, sessionData })
          break
        default:
          console.warn('未知的会话更新动作:', action)
      }
    } catch (error) {
      console.error('处理会话更新失败:', error)
      this.emit('syncError', error)
    }
  }

  /**
   * 同步会话创建
   */
  async syncSessionCreate(moduleKey, session) {
    return await this.publishSessionUpdate(moduleKey, session.id, 'create', session)
  }

  /**
   * 同步会话更新
   */
  async syncSessionUpdate(moduleKey, session) {
    return await this.publishSessionUpdate(moduleKey, session.id, 'update', session)
  }

  /**
   * 同步会话删除
   */
  async syncSessionDelete(moduleKey, sessionId) {
    return await this.publishSessionUpdate(moduleKey, sessionId, 'delete')
  }

  /**
   * 同步会话切换
   */
  async syncSessionSwitch(moduleKey, session) {
    return await this.publishSessionUpdate(moduleKey, session.id, 'switch', session)
  }

  /**
   * 批量同步会话数据
   */
  async syncBatchUpdate(updates) {
    if (!this.isEnabled) return false

    try {
      this.syncStatus.pendingUpdates += updates.length
      
      const promises = updates.map(update => 
        this.publishSessionUpdate(
          update.moduleKey, 
          update.sessionId, 
          update.action, 
          update.sessionData
        )
      )
      
      await Promise.all(promises)
      
      this.syncStatus.pendingUpdates -= updates.length
      console.log(`批量同步完成，处理了${updates.length}个更新`)
      
      return true
    } catch (error) {
      console.error('批量同步失败:', error)
      this.syncStatus.pendingUpdates = Math.max(0, this.syncStatus.pendingUpdates - updates.length)
      this.emit('syncError', error)
      return false
    }
  }

  /**
   * 获取同步状态
   */
  getSyncStatus() {
    return {
      ...this.syncStatus,
      isEnabled: this.isEnabled,
      broadcastChannelSupported: !!this.broadcastChannel
    }
  }

  /**
   * 清除同步错误
   */
  clearSyncErrors() {
    this.syncStatus.errors = []
  }

  /**
   * 添加事件监听器
   */
  on(event, listener) {
    if (this.eventListeners[event]) {
      this.eventListeners[event].push(listener)
    }
  }

  /**
   * 移除事件监听器
   */
  off(event, listener) {
    if (this.eventListeners[event]) {
      const index = this.eventListeners[event].indexOf(listener)
      if (index > -1) {
        this.eventListeners[event].splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   */
  emit(event, data = null) {
    if (this.eventListeners[event]) {
      this.eventListeners[event].forEach(listener => {
        try {
          listener(data)
        } catch (error) {
          console.error(`同步事件监听器执行失败 (${event}):`, error)
        }
      })
    }
  }

  /**
   * 销毁同步服务
   */
  destroy() {
    this.disable()
    
    if (this.broadcastChannel) {
      this.broadcastChannel.close()
      this.broadcastChannel = null
    }
    
    // 清除所有事件监听器
    Object.keys(this.eventListeners).forEach(event => {
      this.eventListeners[event] = []
    })
    
    console.log('会话同步服务已销毁')
  }
}

// 创建单例实例
let sessionSyncService = null

/**
 * 获取会话同步服务实例
 */
export const getSessionSyncService = () => {
  if (!sessionSyncService) {
    sessionSyncService = new SessionSyncService()
  }
  return sessionSyncService
}

export default SessionSyncService
