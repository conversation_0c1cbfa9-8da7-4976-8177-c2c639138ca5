/**
 * 主题调试控制台工具
 * 在浏览器控制台中使用这些函数来调试主题问题
 */

// 检查当前主题状态
window.debugTheme = function() {
    const root = document.documentElement;
    const dataTheme = root.getAttribute('data-theme');
    const hasLightClass = root.classList.contains('light-theme');
    const hasDarkClass = root.classList.contains('dark-theme');
    
    console.log('🎨 主题状态检查:');
    console.log(`data-theme: ${dataTheme}`);
    console.log(`light-theme class: ${hasLightClass}`);
    console.log(`dark-theme class: ${hasDarkClass}`);
    
    // 检查CSS变量值
    const computedStyle = getComputedStyle(root);
    const textPrimary = computedStyle.getPropertyValue('--text-primary').trim();
    const bgSecondary = computedStyle.getPropertyValue('--bg-secondary').trim();
    
    console.log(`--text-primary: ${textPrimary}`);
    console.log(`--bg-secondary: ${bgSecondary}`);
    
    return {
        dataTheme,
        hasLightClass,
        hasDarkClass,
        textPrimary,
        bgSecondary
    };
};

// 检查AI消息元素的实际样式
window.debugAIMessages = function() {
    console.log('🤖 AI消息元素检查:');

    const markdownRenderers = document.querySelectorAll('.markdown-renderer');
    const messageBubbles = document.querySelectorAll('.message-bubble');

    console.log(`找到 ${markdownRenderers.length} 个 markdown-renderer`);
    console.log(`找到 ${messageBubbles.length} 个 message-bubble`);

    markdownRenderers.forEach((element, index) => {
        const computedStyle = getComputedStyle(element);
        console.log(`markdown-renderer ${index + 1}:`);
        console.log(`  color: ${computedStyle.color}`);
        console.log(`  background-color: ${computedStyle.backgroundColor}`);
    });

    messageBubbles.forEach((element, index) => {
        const computedStyle = getComputedStyle(element);
        console.log(`message-bubble ${index + 1}:`);
        console.log(`  color: ${computedStyle.color}`);
        console.log(`  background-color: ${computedStyle.backgroundColor}`);
        console.log(`  border-color: ${computedStyle.borderColor}`);
    });
};

// 检查帮助与反馈页面元素的实际样式
window.debugHelpFeedback = function() {
    console.log('📚 帮助与反馈页面元素检查:');

    const helpFeedbackContainer = document.querySelector('.help-feedback');
    if (!helpFeedbackContainer) {
        console.log('❌ 未找到帮助与反馈页面容器');
        return;
    }

    const textElements = document.querySelectorAll(`
        .help-feedback .guide-title,
        .help-feedback .guide-description,
        .help-feedback .step-text,
        .help-feedback .question-text,
        .help-feedback .form-title,
        .help-feedback .form-description,
        .help-feedback .form-label,
        .help-feedback .radio-label
    `);

    const bgElements = document.querySelectorAll(`
        .help-feedback .guide-item,
        .help-feedback .feedback-form,
        .help-feedback .faq-question,
        .help-feedback .faq-answer
    `);

    const inputElements = document.querySelectorAll(`
        .help-feedback .form-input,
        .help-feedback .form-textarea
    `);

    console.log(`找到 ${textElements.length} 个文本元素`);
    console.log(`找到 ${bgElements.length} 个背景元素`);
    console.log(`找到 ${inputElements.length} 个输入元素`);

    // 检查文本元素颜色
    textElements.forEach((element, index) => {
        const computedStyle = getComputedStyle(element);
        const className = element.className.split(' ').find(cls => cls.includes('guide-') || cls.includes('form-') || cls.includes('question-') || cls.includes('step-') || cls.includes('radio-'));
        console.log(`文本元素 ${index + 1} (${className}):`);
        console.log(`  color: ${computedStyle.color}`);
    });

    // 检查背景元素
    bgElements.forEach((element, index) => {
        const computedStyle = getComputedStyle(element);
        const className = element.className.split(' ').find(cls => cls.includes('guide-') || cls.includes('feedback-') || cls.includes('faq-'));
        console.log(`背景元素 ${index + 1} (${className}):`);
        console.log(`  background-color: ${computedStyle.backgroundColor}`);
        console.log(`  border-color: ${computedStyle.borderColor}`);
    });

    // 检查输入元素
    inputElements.forEach((element, index) => {
        const computedStyle = getComputedStyle(element);
        console.log(`输入元素 ${index + 1}:`);
        console.log(`  color: ${computedStyle.color}`);
        console.log(`  background-color: ${computedStyle.backgroundColor}`);
        console.log(`  border-color: ${computedStyle.borderColor}`);
    });
};

// 强制修复主题问题
window.fixTheme = function(isDark = null) {
    console.log('🔧 强制修复主题...');
    
    if (isDark === null) {
        // 自动检测当前应该是什么主题
        const root = document.documentElement;
        const dataTheme = root.getAttribute('data-theme');
        const hasDarkClass = root.classList.contains('dark-theme');
        isDark = dataTheme === 'moon' || hasDarkClass;
    }
    
    const root = document.documentElement;
    
    // 清除所有主题设置
    root.removeAttribute('data-theme');
    root.classList.remove('light-theme', 'dark-theme');
    
    // 重新应用主题
    setTimeout(() => {
        if (isDark) {
            root.setAttribute('data-theme', 'moon');
            root.classList.add('dark-theme');
        } else {
            root.setAttribute('data-theme', 'sun');
            root.classList.add('light-theme');
        }
        
        // 强制更新AI消息样式
        forceUpdateAIElements(isDark);
        
        console.log(`✅ 主题已修复为: ${isDark ? 'Moon' : 'Sun'}`);
    }, 100);
};

// 强制更新AI元素样式
function forceUpdateAIElements(isDark) {
    const selectors = [
        '.markdown-renderer',
        '.message-bubble',
        '.ai-content-wrapper',
        '.markdown-renderer p',
        '.markdown-renderer h1',
        '.markdown-renderer h2',
        '.markdown-renderer h3',
        '.markdown-renderer h4',
        '.markdown-renderer h5',
        '.markdown-renderer h6',
        '.markdown-renderer li',
        '.markdown-renderer strong',
        '.markdown-renderer em'
    ];

    selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            if (isDark) {
                element.style.color = '#f9fafb';
                if (selector === '.message-bubble') {
                    element.style.backgroundColor = '#1f2937';
                    element.style.borderColor = '#374151';
                }
            } else {
                element.style.color = '#1f2937';
                if (selector === '.message-bubble') {
                    element.style.backgroundColor = '#f8fafc';
                    element.style.borderColor = '#e2e8f0';
                }
            }
        });
    });

    // 强制更新帮助与反馈页面元素
    const helpFeedbackTextSelectors = [
        '.help-feedback .guide-title',
        '.help-feedback .guide-description',
        '.help-feedback .step-text',
        '.help-feedback .question-text',
        '.help-feedback .form-title',
        '.help-feedback .form-description',
        '.help-feedback .form-label',
        '.help-feedback .radio-label',
        '.help-feedback .faq-answer'
    ];

    helpFeedbackTextSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            if (isDark) {
                element.style.color = '#f9fafb';
            } else {
                element.style.color = '#1f2937';
            }
        });
    });

    // 强制更新帮助与反馈页面背景元素
    const helpFeedbackBgSelectors = [
        '.help-feedback .guide-item',
        '.help-feedback .feedback-form',
        '.help-feedback .faq-question',
        '.help-feedback .faq-answer'
    ];

    helpFeedbackBgSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            if (isDark) {
                element.style.backgroundColor = '#1f2937';
                element.style.borderColor = '#374151';
            } else {
                element.style.backgroundColor = '#f8fafc';
                element.style.borderColor = '#e5e7eb';
            }
        });
    });

    // 强制更新帮助与反馈页面输入元素
    const helpFeedbackInputSelectors = [
        '.help-feedback .form-input',
        '.help-feedback .form-textarea'
    ];

    helpFeedbackInputSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            if (isDark) {
                element.style.backgroundColor = '#374151';
                element.style.color = '#f9fafb';
                element.style.borderColor = '#4b5563';
            } else {
                element.style.backgroundColor = '#ffffff';
                element.style.color = '#1f2937';
                element.style.borderColor = '#d1d5db';
            }
        });
    });
}

// 测试主题切换
window.testThemeToggle = function() {
    console.log('🧪 测试主题切换...');
    
    const root = document.documentElement;
    const currentTheme = root.getAttribute('data-theme');
    const newTheme = currentTheme === 'moon' ? 'sun' : 'moon';
    const isDark = newTheme === 'moon';
    
    console.log(`从 ${currentTheme} 切换到 ${newTheme}`);
    
    // 应用新主题
    root.setAttribute('data-theme', newTheme);
    root.classList.toggle('dark-theme', isDark);
    root.classList.toggle('light-theme', !isDark);
    
    // 强制更新样式
    setTimeout(() => {
        forceUpdateAIElements(isDark);
        console.log(`✅ 主题切换完成`);
        
        // 检查结果
        setTimeout(() => {
            debugAIMessages();
        }, 200);
    }, 100);
};

// 创建测试AI消息
window.createTestAIMessage = function() {
    console.log('📝 创建测试AI消息...');
    
    const testContainer = document.createElement('div');
    testContainer.id = 'test-ai-message';
    testContainer.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        width: 400px;
        max-height: 300px;
        background: var(--bg-secondary);
        border: 2px solid var(--border-primary);
        border-radius: 12px;
        padding: 16px;
        z-index: 9999;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        overflow-y: auto;
    `;
    
    testContainer.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
            <h3 style="color: var(--text-primary); margin: 0;">测试AI消息</h3>
            <button onclick="document.getElementById('test-ai-message').remove()" 
                    style="background: #ef4444; color: white; border: none; border-radius: 4px; padding: 4px 8px; cursor: pointer;">
                关闭
            </button>
        </div>
        <div class="ai-message-container">
            <div class="ai-avatar" style="width: 32px; height: 32px; border-radius: 50%; background: var(--bg-tertiary); display: flex; align-items: center; justify-content: center;">
                🤖
            </div>
            <div class="message-bubble" style="flex: 1; margin-left: 12px;">
                <div class="markdown-renderer">
                    <h3>测试标题</h3>
                    <p>这是一段测试文本，用于验证主题切换后的显示效果。</p>
                    <p><strong>重要：</strong>如果你能清楚看到这段文字，说明主题工作正常。</p>
                    <code style="background: var(--bg-tertiary); padding: 2px 4px; border-radius: 3px;">
                        const test = "代码测试";
                    </code>
                </div>
            </div>
        </div>
    `;
    
    // 移除已存在的测试消息
    const existing = document.getElementById('test-ai-message');
    if (existing) {
        existing.remove();
    }
    
    document.body.appendChild(testContainer);
    console.log('✅ 测试AI消息已创建');
    
    return testContainer;
};

// 运行完整测试
window.runThemeTest = function() {
    console.log('🚀 运行完整主题测试...');
    console.log('='.repeat(50));
    
    // 1. 检查当前状态
    console.log('1️⃣ 检查当前主题状态');
    debugTheme();
    
    // 2. 检查AI消息
    console.log('\n2️⃣ 检查AI消息元素');
    debugAIMessages();
    
    // 3. 创建测试消息
    console.log('\n3️⃣ 创建测试AI消息');
    createTestAIMessage();
    
    // 4. 测试主题切换
    setTimeout(() => {
        console.log('\n4️⃣ 测试主题切换');
        testThemeToggle();
        
        // 5. 再次切换回来
        setTimeout(() => {
            console.log('\n5️⃣ 切换回原主题');
            testThemeToggle();
            
            console.log('\n✅ 完整测试完成！');
            console.log('请检查右上角的测试AI消息是否在主题切换时正确显示。');
            console.log('='.repeat(50));
        }, 2000);
    }, 1000);
};

// 显示帮助信息
window.themeHelp = function() {
    console.log('🎯 主题调试工具帮助:');
    console.log('debugTheme() - 检查当前主题状态');
    console.log('debugAIMessages() - 检查AI消息元素样式');
    console.log('debugHelpFeedback() - 检查帮助与反馈页面元素样式');
    console.log('fixTheme(isDark) - 强制修复主题 (isDark: true/false/null)');
    console.log('testThemeToggle() - 测试主题切换');
    console.log('createTestAIMessage() - 创建测试AI消息');
    console.log('runThemeTest() - 运行完整测试');
    console.log('themeHelp() - 显示此帮助信息');
    console.log('\n💡 示例用法:');
    console.log('runThemeTest() // 运行完整测试');
    console.log('debugHelpFeedback() // 检查帮助与反馈页面');
    console.log('fixTheme(true) // 强制应用深色主题');
    console.log('fixTheme(false) // 强制应用浅色主题');
};

// 自动显示帮助信息
if (typeof window !== 'undefined') {
    console.log('🎨 主题调试工具已加载！');
    console.log('输入 themeHelp() 查看可用命令');
}
