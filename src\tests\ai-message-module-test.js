/**
 * AI消息模块标识测试
 * 验证不同模块的AI消息能够正确获取对应的AI配置
 */

import { getAIConfig } from '../config/ai.js'

/**
 * 测试AI消息模块配置获取
 */
export function testAIMessageModuleConfig() {
  console.log('🧪 开始测试AI消息模块配置获取...')
  
  // 测试数据：模拟不同模块的AI消息
  const testMessages = [
    {
      id: '1',
      type: 'ai',
      content: '知识中心回复',
      module: 'knowledge'
    },
    {
      id: '2', 
      type: 'ai',
      content: '业务域回复',
      module: 'business'
    },
    {
      id: '3',
      type: 'ai', 
      content: '职能域回复',
      module: 'function'
    },
    {
      id: '4',
      type: 'ai',
      content: '无模块标识回复（应该默认为knowledge）'
      // 没有module字段
    }
  ]
  
  // 测试每个消息的配置获取
  testMessages.forEach(message => {
    const config = getAIConfig(message.module || 'knowledge')
    
    console.log(`📋 消息 ${message.id} (${message.module || 'default'}):`);
    console.log(`   - 模块: ${config.module}`)
    console.log(`   - 温度: ${config.temperature}`)
    console.log(`   - 历史轮数: ${config.historyTurns}`)
    console.log(`   - 推理过程: ${config.enableReasoning}`)
    console.log(`   - 自动展开: ${config.reasoningAutoExpand}`)
    
    // 验证配置正确性
    if (message.module) {
      if (config.module !== message.module) {
        console.error(`❌ 配置错误: 期望模块 ${message.module}, 实际 ${config.module}`)
      } else {
        console.log(`✅ 配置正确: 模块 ${config.module}`)
      }
    } else {
      if (config.module !== 'knowledge') {
        console.error(`❌ 默认配置错误: 期望模块 knowledge, 实际 ${config.module}`)
      } else {
        console.log(`✅ 默认配置正确: 模块 ${config.module}`)
      }
    }
    console.log('')
  })
  
  console.log('🧪 AI消息模块配置测试完成')
}

/**
 * 测试不同模块的配置差异
 */
export function testModuleConfigDifferences() {
  console.log('🔍 开始测试模块配置差异...')
  
  const modules = ['knowledge', 'business', 'function']
  const configs = modules.map(module => ({
    module,
    config: getAIConfig(module)
  }))
  
  console.log('📊 模块配置对比:')
  console.log('模块\t\t温度\t历史轮数\t推理过程\t自动展开')
  console.log('----\t\t----\t--------\t--------\t--------')
  
  configs.forEach(({ module, config }) => {
    console.log(`${module}\t\t${config.temperature}\t${config.historyTurns}\t\t${config.enableReasoning}\t\t${config.reasoningAutoExpand}`)
  })
  
  // 验证推理过程配置一致性
  const reasoningConfigs = configs.map(c => c.config.enableReasoning)
  const allSameReasoning = reasoningConfigs.every(val => val === reasoningConfigs[0])
  
  if (allSameReasoning) {
    console.log('✅ 所有模块的推理过程配置一致')
  } else {
    console.log('❌ 模块间推理过程配置不一致')
  }
  
  console.log('🔍 模块配置差异测试完成')
}

/**
 * 运行所有测试
 */
export function runAllTests() {
  console.log('🚀 开始运行AI消息模块测试套件...')
  console.log('')
  
  testAIMessageModuleConfig()
  console.log('')
  testModuleConfigDifferences()
  
  console.log('')
  console.log('🎉 所有测试完成！')
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined' && window.location) {
  // 浏览器环境，可以通过控制台调用
  window.aiMessageModuleTest = {
    testAIMessageModuleConfig,
    testModuleConfigDifferences,
    runAllTests
  }
  
  console.log('🧪 AI消息模块测试已加载，可通过以下方式调用:')
  console.log('   - window.aiMessageModuleTest.runAllTests()')
  console.log('   - window.aiMessageModuleTest.testAIMessageModuleConfig()')
  console.log('   - window.aiMessageModuleTest.testModuleConfigDifferences()')
}
