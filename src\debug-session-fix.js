/**
 * 调试会话修复的工具函数
 * 在浏览器控制台中使用这些函数来测试会话功能
 */

// 检查当前会话状态
window.debugSessionState = () => {
  console.log('=== 当前会话状态 ===')
  console.log('当前模块:', window.currentModule?.value)
  console.log('模块会话列表:', window.moduleSessions?.value)
  console.log('当前会话:', window.moduleCurrentSession?.value)
  console.log('会话已加载:', window.sessionsLoaded?.value)
  console.log('==================')
}

// 清空所有会话数据（用于测试）
window.clearAllSessions = () => {
  if (window.moduleSessions && window.moduleCurrentSession) {
    window.moduleSessions.value = {
      knowledge: [],
      business: [],
      function: []
    }
    window.moduleCurrentSession.value = {
      knowledge: null,
      business: null,
      function: null
    }
    console.log('✅ 已清空所有会话数据')
    
    // 清空存储
    localStorage.clear()
    sessionStorage.clear()
    console.log('✅ 已清空本地存储')
  } else {
    console.error('❌ 无法访问会话数据')
  }
}

// 模拟直接进入模块并发送消息
window.testDirectModuleMessage = async (module = 'business', message = '测试消息') => {
  console.log(`=== 测试直接进入${module}模块并发送消息 ===`)
  
  // 1. 检查初始状态
  console.log('1. 初始状态检查:')
  window.debugSessionState()
  
  // 2. 切换到指定模块
  console.log(`2. 切换到${module}模块`)
  if (window.handleModuleChange) {
    await window.handleModuleChange(module)
    console.log('模块切换完成')
  } else {
    console.error('❌ 无法访问模块切换函数')
    return
  }
  
  // 3. 检查切换后状态
  console.log('3. 切换后状态检查:')
  window.debugSessionState()
  
  // 4. 发送消息
  console.log(`4. 发送消息: "${message}"`)
  if (window.handleSendMessage) {
    try {
      await window.handleSendMessage(message, { test: true })
      console.log('✅ 消息发送成功')
    } catch (error) {
      console.error('❌ 消息发送失败:', error)
    }
  } else {
    console.error('❌ 无法访问消息发送函数')
    return
  }
  
  // 5. 检查最终状态
  console.log('5. 最终状态检查:')
  window.debugSessionState()
  
  // 6. 检查会话是否在列表中
  const currentModuleSessions = window.moduleSessions?.value[module] || []
  console.log(`6. ${module}模块会话列表:`, currentModuleSessions)
  console.log(`会话数量: ${currentModuleSessions.length}`)
  
  if (currentModuleSessions.length > 0) {
    const latestSession = currentModuleSessions[0]
    console.log('最新会话:', latestSession)
    console.log('消息数量:', latestSession.messages?.length || 0)
    console.log('✅ 会话创建成功')
  } else {
    console.error('❌ 会话列表为空')
  }
  
  console.log('=== 测试完成 ===')
}

// 测试刷新后会话持久性
window.testSessionPersistence = () => {
  console.log('=== 测试会话持久性 ===')
  console.log('当前会话状态:')
  window.debugSessionState()
  
  console.log('即将刷新页面，请在刷新后运行 debugSessionState() 检查会话是否保留')
  setTimeout(() => {
    window.location.reload()
  }, 2000)
}

// 检查存储中的会话数据
window.checkStorageData = () => {
  console.log('=== 检查存储数据 ===')
  
  // 检查 localStorage
  const localData = localStorage.getItem('bodorai_sessions')
  if (localData) {
    try {
      const parsed = JSON.parse(localData)
      console.log('localStorage 中的会话数据:', parsed)
    } catch (error) {
      console.error('localStorage 数据解析失败:', error)
    }
  } else {
    console.log('localStorage 中没有会话数据')
  }
  
  // 检查 sessionStorage
  const sessionData = sessionStorage.getItem('bodorai_sessions')
  if (sessionData) {
    try {
      const parsed = JSON.parse(sessionData)
      console.log('sessionStorage 中的会话数据:', parsed)
    } catch (error) {
      console.error('sessionStorage 数据解析失败:', error)
    }
  } else {
    console.log('sessionStorage 中没有会话数据')
  }
  
  console.log('=== 存储检查完成 ===')
}

console.log('🔧 会话调试工具已加载')
console.log('可用函数:')
console.log('- debugSessionState(): 检查当前会话状态')
console.log('- clearAllSessions(): 清空所有会话数据')
console.log('- testDirectModuleMessage(module, message): 测试直接进入模块并发送消息')
console.log('- testSessionPersistence(): 测试会话持久性')
console.log('- checkStorageData(): 检查存储中的会话数据')
console.log('')
console.log('示例用法:')
console.log('testDirectModuleMessage("business", "你好，这是一条测试消息")')
