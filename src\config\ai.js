/**
 * AI对话配置文件
 * 支持OneAPI格式的配置，支持环境变量覆盖
 *
 * 配置说明：
 * - baseURL: API基础地址
 * - apiKey: API密钥
 * - model: 使用的模型名称
 * - maxTokens: 最大token数量
 * - temperature: 温度参数，控制回复的随机性
 * - timeout: 请求超时时间（毫秒）
 * - historyTurns: 携带历史对话的轮数（0=不携带，1=1轮，2=2轮，-1=全部）
 */

// 从环境变量获取配置，提供默认值
const getEnvConfig = () => ({
  baseURL: import.meta.env.VITE_AI_BASE_URL || '/api/v1',
  apiKey: import.meta.env.VITE_AI_API_KEY || 'fastgpt-zifErOcL2pn5t2cDGGnuLd1iVKC5TvdVDvnnCv16wLusOocffWYOkk',
  model: import.meta.env.VITE_AI_MODEL || 'Qwen3-235B-A22B',
  enableMockMode: import.meta.env.VITE_AI_ENABLE_MOCK === 'true' || true,
  historyTurns: parseInt(import.meta.env.VITE_AI_HISTORY_TURNS) || 5, // 默认携带5轮历史对话
  // 推理过程配置
  enableReasoning: import.meta.env.VITE_AI_ENABLE_REASONING !== 'false', // 默认启用推理过程
  reasoningAutoExpand: import.meta.env.VITE_AI_REASONING_AUTO_EXPAND === 'true' // 默认不自动展开
})

// 获取环境配置
const envConfig = getEnvConfig()

// 知识中心AI配置
export const knowledgeAIConfig = {
  // OneAPI兼容的基础配置 - 使用环境变量和Vite代理避免CORS问题
  baseURL: envConfig.baseURL,
  apiKey: "fastgpt-yGVaR5PAaZak4701c6tNEbPnhCFCESFYqijhFDajLtOYeOOYEAGsI9z5IS0",
  // baseURL: 'http://192.168.1.225:3001/v1',
  // apiKey: "sk-UOPAFn2SZBgRRP6156Ea873cE8174587A989998e4aB838Da",
  model: envConfig.model,

  // 对话参数配置
  maxTokens: 20000,
  temperature: 0.7,
  timeout: 40000, // 40秒超时

  // 历史对话配置
  historyTurns: 2, // 携带3轮历史对话（0=不携带，-1=全部）

  // 推理过程配置
  enableReasoning: envConfig.enableReasoning,
  reasoningAutoExpand: envConfig.reasoningAutoExpand,

  // 系统提示词
  systemPrompt: '',

  // 模块标识
  module: 'knowledge',

  // 启用模拟模式（当AI服务不可用时）
  enableMockMode: envConfig.enableMockMode
}

// 业务域AI配置
export const businessAIConfig = {
  // OneAPI兼容的基础配置 - 使用环境变量和Vite代理避免CORS问题
  baseURL: envConfig.baseURL,
  apiKey: "fastgpt-iRqGjnyvRBvrXjzz0v62iLMnqM7Wz4AshIBX04NN2yClHsUXRsejD1Ah5bwV",
  model: envConfig.model,

  // 对话参数配置
  maxTokens: 20000,
  temperature: 0.6,
  timeout: 40000, // 40秒超时

  // 历史对话配置
  historyTurns: 2, // 携带5轮历史对话（业务场景需要更多上下文）

  // 推理过程配置
  enableReasoning: envConfig.enableReasoning,
  reasoningAutoExpand: envConfig.reasoningAutoExpand,

  // 系统提示词
  systemPrompt: '',

  // 模块标识
  module: 'business',

  // 启用模拟模式（当AI服务不可用时）
  enableMockMode: envConfig.enableMockMode
}

// 职能域AI配置
export const functionAIConfig = {
  // OneAPI兼容的基础配置 - 使用环境变量和Vite代理避免CORS问题
  baseURL: envConfig.baseURL,
  apiKey: "fastgpt-raENCa2YY93r4hr9T48MJ5DaaGYqgV5o4WoH6S8f1oVPC6LdBAKSGsojAM5a8XB6",
  model: envConfig.model,

  // 对话参数配置
  maxTokens: 20000,
  temperature: 0.5,
  timeout: 40000, // 40秒超时

  // 历史对话配置
  historyTurns: 2, // 携带2轮历史对话（功能性对话通常不需要太多历史）

  // 推理过程配置
  enableReasoning: envConfig.enableReasoning,
  reasoningAutoExpand: envConfig.reasoningAutoExpand,

  // 系统提示词
  systemPrompt: '',

  // 模块标识
  module: 'function',

  // 启用模拟模式（当AI服务不可用时）
  enableMockMode: envConfig.enableMockMode
}

// 获取指定模块的AI配置
export const getAIConfig = (module) => {
  const configMap = {
    knowledge: knowledgeAIConfig,
    business: businessAIConfig,
    function: functionAIConfig
  }
  
  return configMap[module] || knowledgeAIConfig
}

// 验证配置是否完整
export const validateAIConfig = (config) => {
  const requiredFields = ['baseURL', 'apiKey', 'model']
  
  for (const field of requiredFields) {
    if (!config[field] || config[field] === 'your-api-key-here') {
      console.warn(`AI配置警告: ${field} 未正确配置`)
      return false
    }
  }
  
  return true
}

// 默认导出所有配置
export default {
  knowledge: knowledgeAIConfig,
  business: businessAIConfig,
  function: functionAIConfig,
  getAIConfig,
  validateAIConfig
}
