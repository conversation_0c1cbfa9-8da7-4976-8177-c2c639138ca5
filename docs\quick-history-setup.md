# 历史对话轮数快速配置指南

## 快速开始

### 1. 基本概念

**历史对话轮数** 控制AI在回答时能看到多少轮之前的对话内容：

- `0` = 不看历史，每次都是新对话
- `1` = 看1轮历史（上一个问题和回答）
- `3` = 看3轮历史（推荐设置）
- `-1` = 看全部历史

### 2. 快速配置

#### 方法一：修改配置文件（推荐）

编辑 `src/config/ai.js`：

```javascript
// 知识中心
export const knowledgeAIConfig = {
  // 其他配置...
  historyTurns: 3, // 改成你想要的数字
}

// 业务域
export const businessAIConfig = {
  // 其他配置...
  historyTurns: 5, // 改成你想要的数字
}

// 职能域
export const functionAIConfig = {
  // 其他配置...
  historyTurns: 2, // 改成你想要的数字
}
```

#### 方法二：环境变量配置

编辑 `.env.development` 或 `.env.production`：

```bash
# 全局默认设置
VITE_AI_HISTORY_TURNS=3
```

### 3. 常用配置推荐

#### 🚀 快速问答（节省成本）
```javascript
historyTurns: 0
```
- 适合：FAQ、搜索式问答
- 优点：响应快、成本低
- 缺点：无法连续对话

#### 📚 知识问答（平衡模式）
```javascript
historyTurns: 3
```
- 适合：一般知识咨询
- 优点：有上下文、成本适中
- 缺点：-

#### 💼 业务咨询（深度模式）
```javascript
historyTurns: 5
```
- 适合：复杂业务问题
- 优点：理解完整场景
- 缺点：成本较高

#### 🔧 功能操作（简洁模式）
```javascript
historyTurns: 2
```
- 适合：工具使用指导
- 优点：有基本上下文
- 缺点：-

#### 📊 深度分析（完整模式）
```javascript
historyTurns: -1
```
- 适合：数据分析、长期咨询
- 优点：完整上下文
- 缺点：成本最高

### 4. 实际效果对比

假设有以下对话：
```
用户: 什么是AI？
AI: AI是人工智能...
用户: AI有什么用？
AI: AI可以用于...
用户: 给我举个例子 ← 当前问题
```

#### historyTurns = 0
AI只看到：
```
用户: 给我举个例子
```

#### historyTurns = 1
AI看到：
```
用户: AI有什么用？
AI: AI可以用于...
用户: 给我举个例子
```

#### historyTurns = 2
AI看到：
```
用户: 什么是AI？
AI: AI是人工智能...
用户: AI有什么用？
AI: AI可以用于...
用户: 给我举个例子
```

### 5. 如何选择合适的数值

#### 根据使用场景

| 场景 | 推荐值 | 原因 |
|------|--------|------|
| 快速帮助 | 0-1 | 问题独立，不需要上下文 |
| 知识问答 | 2-3 | 需要一定上下文理解 |
| 业务咨询 | 3-5 | 需要理解完整业务场景 |
| 技术支持 | 2-4 | 需要了解问题背景 |
| 创意写作 | 5-8 | 需要保持创作连贯性 |
| 数据分析 | -1 | 需要完整的分析过程 |

#### 根据成本考虑

| 成本要求 | 推荐值 | 说明 |
|----------|--------|------|
| 最低成本 | 0 | 每次都是独立请求 |
| 低成本 | 1-2 | 基本的上下文支持 |
| 中等成本 | 3-4 | 平衡效果和成本 |
| 高质量 | 5-8 | 更好的理解能力 |
| 不限成本 | -1 | 最佳效果 |

### 6. 测试和调优

#### 测试步骤

1. **设置初始值**：从推荐值开始
2. **进行对话测试**：测试连续对话效果
3. **观察日志**：查看实际发送的消息数量
4. **调整数值**：根据效果调整

#### 查看日志

在浏览器控制台中查看：
```
[knowledge] 历史对话配置: {
  totalMessages: 6,
  historyTurns: 3,
  filteredMessages: 4,
  actualTurns: 2
}
```

#### 调优建议

- **效果不好**：增加 historyTurns
- **成本太高**：减少 historyTurns
- **响应太慢**：减少 historyTurns
- **理解偏差**：检查系统提示词

### 7. 常见问题

#### Q: 设置为0还是会有历史对话？
A: 设置为0时，只会发送当前用户消息，不会包含历史对话。

#### Q: 设置为-1会不会太消耗资源？
A: 是的，-1会发送所有历史消息，建议只在必要时使用。

#### Q: 不同模块可以设置不同的值吗？
A: 可以，每个模块都有独立的配置。

#### Q: 如何知道当前设置是否合适？
A: 观察AI回答的连贯性和相关性，以及成本消耗。

### 8. 故障排除

#### 配置不生效
1. 检查语法是否正确
2. 重启开发服务器
3. 清除浏览器缓存

#### 成本过高
1. 减少 historyTurns 数值
2. 检查是否有异常长的对话
3. 考虑设置 maxTokens 限制

#### 理解效果差
1. 增加 historyTurns 数值
2. 检查系统提示词设置
3. 确认消息过滤逻辑

### 9. 最佳实践

1. **从推荐值开始**：使用场景对应的推荐值
2. **逐步调优**：根据实际效果微调
3. **监控成本**：定期检查token消耗
4. **分环境配置**：开发和生产使用不同配置
5. **记录变更**：记录配置变更和效果

### 10. 一键配置模板

复制以下代码到你的配置文件：

```javascript
// 快速配置模板 - 选择一个使用

// 经济模式（最低成本）
historyTurns: 0,

// 标准模式（推荐）
historyTurns: 3,

// 专业模式（高质量）
historyTurns: 5,

// 专家模式（最佳效果）
historyTurns: -1,
```

选择适合你的模式，复制对应的配置即可！
