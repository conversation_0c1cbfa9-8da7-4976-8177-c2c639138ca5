/**
 * 日志服务器
 * 用于接收前端发送的反馈日志并写入到.log文件中
 */

import express from 'express'
import { promises as fs } from 'fs'
import path from 'path'
import cors from 'cors'
import { fileURLToPath } from 'url'

// ES模块中获取__dirname的方法
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const app = express()
const PORT = process.env.LOG_SERVER_PORT || 4002

// 中间件
app.use(cors())
app.use(express.json({ limit: '10mb' }))

// 日志配置
const LOG_CONFIG = {
  logDir: './logs',
  feedbackLogFile: 'feedback.log',
  maxLogSize: 10 * 1024 * 1024, // 10MB
  maxLogFiles: 10
}

/**
 * 确保日志目录存在
 */
async function ensureLogDirectory() {
  try {
    await fs.access(LOG_CONFIG.logDir)
  } catch (error) {
    await fs.mkdir(LOG_CONFIG.logDir, { recursive: true })
    console.log('日志目录已创建:', LOG_CONFIG.logDir)
  }
}

/**
 * 格式化日志内容
 * @param {Object} logData - 日志数据
 * @returns {string} 格式化的日志内容
 */
function formatLogContent(logData) {
  const timestamp = new Date().toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    timeZone: 'Asia/Shanghai'
  })

  const lines = [
    `[${timestamp}] FEEDBACK_LOG`,
    `ID: ${logData.id}`,
    `User: ${logData.user.username} (${logData.user.employeeId})`,
    `Type: ${logData.feedback.type}`,
    `Content: ${logData.feedback.content}`,
    `Contact: ${logData.feedback.contact}`,
    `---`
  ]
  
  return lines.join('\n') + '\n'
}

/**
 * 检查是否需要轮转日志文件
 * @param {string} logFilePath - 日志文件路径
 * @returns {Promise<boolean>} 是否需要轮转
 */
async function shouldRotateLog(logFilePath) {
  try {
    const stats = await fs.stat(logFilePath)
    return stats.size >= LOG_CONFIG.maxLogSize
  } catch (error) {
    // 文件不存在，不需要轮转
    return false
  }
}

/**
 * 轮转日志文件
 * @param {string} logFilePath - 日志文件路径
 */
async function rotateLogFile(logFilePath) {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const rotatedPath = `${logFilePath}.${timestamp}`
    
    await fs.rename(logFilePath, rotatedPath)
    console.log('日志文件已轮转:', rotatedPath)
    
    // 清理旧的轮转文件
    await cleanupOldLogFiles(path.dirname(logFilePath))
  } catch (error) {
    console.error('轮转日志文件失败:', error)
  }
}

/**
 * 清理旧的日志文件
 * @param {string} logDir - 日志目录
 */
async function cleanupOldLogFiles(logDir) {
  try {
    const files = await fs.readdir(logDir)
    const logFiles = files
      .filter(file => file.startsWith(LOG_CONFIG.feedbackLogFile) && file !== LOG_CONFIG.feedbackLogFile)
      .map(file => ({
        name: file,
        path: path.join(logDir, file),
        timestamp: file.split('.').pop()
      }))
      .sort((a, b) => b.timestamp.localeCompare(a.timestamp))
    
    // 删除超过保留数量的旧文件
    if (logFiles.length > LOG_CONFIG.maxLogFiles) {
      const filesToDelete = logFiles.slice(LOG_CONFIG.maxLogFiles)
      for (const file of filesToDelete) {
        await fs.unlink(file.path)
        console.log('删除旧日志文件:', file.name)
      }
    }
  } catch (error) {
    console.error('清理旧日志文件失败:', error)
  }
}

/**
 * 写入日志到文件
 * @param {Object} logData - 日志数据
 */
async function writeLogToFile(logData) {
  const logFilePath = path.join(LOG_CONFIG.logDir, LOG_CONFIG.feedbackLogFile)
  
  // 检查是否需要轮转
  if (await shouldRotateLog(logFilePath)) {
    await rotateLogFile(logFilePath)
  }
  
  // 格式化日志内容
  const logContent = formatLogContent(logData)
  
  // 追加写入日志文件
  await fs.appendFile(logFilePath, logContent, 'utf8')
}

// API路由

/**
 * 健康检查端点
 */
app.get('/api/logs/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    logDir: LOG_CONFIG.logDir
  })
})

/**
 * 接收日志数据
 */
app.post('/api/logs', async (req, res) => {
  try {
    const { type, data, timestamp } = req.body
    
    if (type !== 'feedback_log' || !data) {
      return res.status(400).json({ 
        error: '无效的日志数据格式' 
      })
    }
    
    // 写入日志文件
    await writeLogToFile(data)
    
    console.log('反馈日志已记录:', data.id)
    
    res.json({ 
      success: true, 
      message: '日志记录成功',
      logId: data.id
    })
  } catch (error) {
    console.error('记录日志失败:', error)
    res.status(500).json({ 
      error: '服务器内部错误',
      message: error.message 
    })
  }
})

/**
 * 获取日志统计信息
 */
app.get('/api/logs/stats', async (req, res) => {
  try {
    const logFilePath = path.join(LOG_CONFIG.logDir, LOG_CONFIG.feedbackLogFile)
    
    let stats = {
      totalSize: 0,
      totalEntries: 0,
      rotatedFiles: 0
    }
    
    try {
      const logStats = await fs.stat(logFilePath)
      stats.totalSize = logStats.size
      
      // 简单统计日志条目数（通过分隔符计算）
      const content = await fs.readFile(logFilePath, 'utf8')
      stats.totalEntries = (content.match(/---/g) || []).length
    } catch (error) {
      // 日志文件不存在
    }
    
    // 统计轮转文件数量
    try {
      const files = await fs.readdir(LOG_CONFIG.logDir)
      stats.rotatedFiles = files.filter(file => 
        file.startsWith(LOG_CONFIG.feedbackLogFile) && file !== LOG_CONFIG.feedbackLogFile
      ).length
    } catch (error) {
      // 目录不存在
    }
    
    res.json(stats)
  } catch (error) {
    console.error('获取日志统计失败:', error)
    res.status(500).json({ 
      error: '获取统计信息失败',
      message: error.message 
    })
  }
})

// 启动服务器
async function startServer() {
  try {
    console.log('正在启动日志服务器...')
    await ensureLogDirectory()

    app.listen(PORT, () => {
      console.log(`✅ 日志服务器已启动，端口: ${PORT}`)
      console.log(`📁 日志目录: ${path.resolve(LOG_CONFIG.logDir)}`)
      console.log(`🔍 健康检查: http://localhost:${PORT}/api/logs/health`)
      console.log(`📝 日志接收: http://localhost:${PORT}/api/logs`)
    })
  } catch (error) {
    console.error('❌ 启动日志服务器失败:', error)
    process.exit(1)
  }
}

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n正在关闭日志服务器...')
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('\n正在关闭日志服务器...')
  process.exit(0)
})

// 启动服务器
startServer()
