<template>
  <div class="notification-test">
    <div class="test-header">
      <h2>通知框样式测试</h2>
      <p>测试不同类型的通知框在浅色和深色模式下的显示效果</p>
    </div>

    <div class="test-controls">
      <button @click="showSuccessNotification" class="test-btn success">
        显示成功通知
      </button>
      <button @click="showInfoNotification" class="test-btn info">
        显示信息通知
      </button>
      <button @click="showWarningNotification" class="test-btn warning">
        显示警告通知
      </button>
      <button @click="showErrorNotification" class="test-btn error">
        显示错误通知
      </button>
      <button @click="showAllNotifications" class="test-btn all">
        显示所有类型
      </button>
    </div>

    <div class="test-info">
      <h3>当前主题</h3>
      <p>{{ currentTheme === 'dark' ? '深色模式' : '浅色模式' }}</p>
      <button @click="toggleTheme" class="theme-toggle-btn">
        切换到{{ currentTheme === 'dark' ? '浅色' : '深色' }}模式
      </button>
    </div>

    <!-- 通知组件 -->
    <div v-if="notification.show" class="notification" :class="`notification-${notification.type}`">
      <div class="notification-content">
        <span class="notification-message">{{ notification.message }}</span>
        <button class="notification-close" @click="notification.show = false">×</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 通知状态
const notification = ref({
  show: false,
  message: '',
  type: 'info'
})

// 当前主题
const currentTheme = ref('light')

// 通知定时器
let notificationTimer = null

/**
 * 显示通知
 */
const showNotification = (message, type = 'info', duration = 4000) => {
  // 清除之前的定时器
  if (notificationTimer) {
    clearTimeout(notificationTimer)
  }

  // 设置通知内容
  notification.value = {
    show: true,
    message,
    type
  }

  // 设置自动隐藏
  notificationTimer = setTimeout(() => {
    notification.value.show = false
  }, duration)
}

/**
 * 显示成功通知
 */
const showSuccessNotification = () => {
  showNotification('操作成功完成！这是一个成功通知的示例。', 'success')
}

/**
 * 显示信息通知
 */
const showInfoNotification = () => {
  showNotification('这是一条信息通知，用于向用户提供重要信息。', 'info')
}

/**
 * 显示警告通知
 */
const showWarningNotification = () => {
  showNotification('请注意：这是一个警告通知，提醒用户注意某些情况。', 'warning')
}

/**
 * 显示错误通知
 */
const showErrorNotification = () => {
  showNotification('发生错误：这是一个错误通知，用于显示错误信息。', 'error')
}

/**
 * 显示所有类型的通知
 */
const showAllNotifications = () => {
  const notifications = [
    { message: '成功：Redis连接已建立', type: 'success' },
    { message: '信息：正在加载会话数据...', type: 'info' },
    { message: '警告：Redis连接失败，使用本地存储', type: 'warning' },
    { message: '错误：网络连接失败，请检查网络设置', type: 'error' }
  ]

  notifications.forEach((notif, index) => {
    setTimeout(() => {
      showNotification(notif.message, notif.type, 3000)
    }, index * 800)
  })
}

/**
 * 切换主题
 */
const toggleTheme = () => {
  currentTheme.value = currentTheme.value === 'dark' ? 'light' : 'dark'
  document.documentElement.setAttribute('data-theme', currentTheme.value)
}

/**
 * 检测当前主题
 */
const detectCurrentTheme = () => {
  const theme = document.documentElement.getAttribute('data-theme') || 'light'
  currentTheme.value = theme
}

// 组件挂载时检测主题
onMounted(() => {
  detectCurrentTheme()
})
</script>

<style scoped>
.notification-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
}

.test-header h2 {
  color: var(--text-primary);
  margin-bottom: 10px;
}

.test-header p {
  color: var(--text-secondary);
  font-size: 14px;
}

.test-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
  margin-bottom: 30px;
}

.test-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.test-btn.success {
  background-color: #52c41a;
  color: white;
}

.test-btn.info {
  background-color: #1890ff;
  color: white;
}

.test-btn.warning {
  background-color: #faad14;
  color: white;
}

.test-btn.error {
  background-color: #ff4d4f;
  color: white;
}

.test-btn.all {
  background-color: #722ed1;
  color: white;
}

.test-btn:hover {
  opacity: 0.8;
  transform: translateY(-1px);
}

.test-info {
  text-align: center;
  padding: 20px;
  background-color: var(--bg-secondary);
  border-radius: 8px;
  margin-bottom: 20px;
}

.test-info h3 {
  color: var(--text-primary);
  margin-bottom: 10px;
}

.test-info p {
  color: var(--text-secondary);
  margin-bottom: 15px;
}

.theme-toggle-btn {
  padding: 8px 16px;
  background-color: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.theme-toggle-btn:hover {
  opacity: 0.8;
}

/* 通知组件样式（复制自MainLayout.vue） */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 400px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slideInRight 0.3s ease-out;
}

.notification-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: var(--bg-primary);
  border-radius: 8px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.notification-message {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
  color: var(--text-primary);
  font-weight: 500;
}

.notification-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: var(--text-secondary);
  margin-left: 12px;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-close:hover {
  color: var(--text-primary);
}

/* 深色模式下通知框基础样式增强 */
[data-theme="dark"] .notification-content {
  background-color: rgba(30, 30, 30, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .notification-message {
  color: #ff4d4f;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 不同类型的通知样式 */
.notification-success {
  border-left: 4px solid #52c41a;
}

.notification-success .notification-content {
  background-color: var(--notification-success-bg, #f6ffed);
  border: 1px solid var(--notification-success-border, #b7eb8f);
  color: var(--notification-success-text, #135200);
}

.notification-info {
  border-left: 4px solid #1890ff;
}

.notification-info .notification-content {
  background-color: var(--notification-info-bg, #e6f7ff);
  border: 1px solid var(--notification-info-border, #91d5ff);
  color: var(--notification-info-text, #003a8c);
}

.notification-warning {
  border-left: 4px solid #faad14;
}

.notification-warning .notification-content {
  background-color: var(--notification-warning-bg, #fffbe6);
  border: 1px solid var(--notification-warning-border, #ffe58f);
  color: var(--notification-warning-text, #613400);
}

.notification-error {
  border-left: 4px solid #ff4d4f;
}

.notification-error .notification-content {
  background-color: var(--notification-error-bg, #fff2f0);
  border: 1px solid var(--notification-error-border, #ffccc7);
  color: var(--notification-error-text, #a8071a);
}

/* 深色模式下的通知样式 */
[data-theme="dark"] .notification-success .notification-content {
  background-color: rgba(82, 196, 26, 0.15);
  border: 1px solid rgba(82, 196, 26, 0.3);
  color: #95de64;
}

[data-theme="dark"] .notification-info .notification-content {
  background-color: rgba(24, 144, 255, 0.15);
  border: 1px solid rgba(24, 144, 255, 0.3);
  color: #69c0ff;
}

[data-theme="dark"] .notification-warning .notification-content {
  background-color: rgba(250, 173, 20, 0.15);
  border: 1px solid rgba(250, 173, 20, 0.3);
  color: #ffd666;
}

[data-theme="dark"] .notification-error .notification-content {
  background-color: rgba(255, 77, 79, 0.15);
  border: 1px solid rgba(255, 77, 79, 0.3);
  color: #ff7875;
}

/* 深色模式下通知关闭按钮样式 */
[data-theme="dark"] .notification-close {
  color: var(--text-secondary);
}

[data-theme="dark"] .notification-close:hover {
  color: var(--text-primary);
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
</style>
