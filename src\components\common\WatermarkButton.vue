<template>
  <!-- 水印配置按钮 -->
  <div class="watermark-button">
    <button 
      class="watermark-toggle"
      @click="toggleConfig"
      :class="{ 'active': showConfig }"
      :title="showConfig ? '关闭水印配置' : '打开水印配置'"
    >
      <WatermarkIcon />
      <span class="button-text">水印</span>
    </button>
    
    <!-- 水印状态指示器 -->
    <div 
      v-if="watermarkEnabled" 
      class="watermark-indicator"
      title="水印已启用"
    >
      <div class="indicator-dot"></div>
    </div>
  </div>
</template>

<script setup>
// defineProps 和 defineEmits 在 Vue 3 中是编译器宏，不需要导入

// 图标组件
const WatermarkIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8Z"/>
    </svg>
  `
}

// 组件属性
const props = defineProps({
  // 是否显示配置面板
  showConfig: {
    type: Boolean,
    default: false
  },
  // 水印是否启用
  watermarkEnabled: {
    type: Boolean,
    default: false
  }
})

// 组件事件
const emit = defineEmits(['toggle-config'])

/**
 * 切换配置面板
 */
const toggleConfig = () => {
  emit('toggle-config')
}
</script>

<style scoped>
/* 水印按钮容器 */
.watermark-button {
  position: relative;
  display: inline-block;
}

/* 水印切换按钮 */
.watermark-toggle {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background-color: #ffffff;
  color: #6b7280;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
}

.watermark-toggle:hover {
  background-color: #f8fafc;
  border-color: #3b82f6;
  color: #3b82f6;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.watermark-toggle.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.watermark-toggle svg {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

.watermark-toggle:hover svg {
  transform: scale(1.1);
}

.watermark-toggle.active svg {
  transform: scale(1.05);
}

.button-text {
  white-space: nowrap;
}

/* 水印状态指示器 */
.watermark-indicator {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 12px;
  height: 12px;
  background-color: #10b981;
  border-radius: 50%;
  border: 2px solid white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.indicator-dot {
  width: 4px;
  height: 4px;
  background-color: white;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(0.8);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .watermark-toggle {
    padding: 10px 14px;
    font-size: 14px;
  }
  
  .watermark-toggle svg {
    width: 18px;
    height: 18px;
  }
  
  .watermark-indicator {
    width: 14px;
    height: 14px;
    top: -5px;
    right: -5px;
  }
  
  .indicator-dot {
    width: 5px;
    height: 5px;
  }
}
</style>
