{"name": "bodorai-redis-proxy", "version": "1.0.0", "description": "Redis HTTP代理服务器，为BodorAI前端应用提供Redis访问接口", "main": "redis-proxy-server.js", "scripts": {"start": "node redis-proxy-server.js", "dev": "nodemon redis-proxy-server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["redis", "proxy", "http", "api", "bod<PERSON>i"], "author": "BodorAI Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "redis": "^4.6.0"}, "devDependencies": {"nodemon": "^3.0.0"}, "engines": {"node": ">=16.0.0"}}