# SSO参数更新说明

## 🔄 更新内容

根据Java代码分析，SSO验证需要同时发送两个参数：
- `tokenId`: SSO token
- `access_key`: 访问密钥

## 📋 Java代码分析

原Java代码片段：
```java
// 设置请求参数
String url = "https://oa.bodor.com:8443/portal/r/jd?cmd=com.actionsoft.apps.addons.sso_validate";
String access_key = "2e5295e9-f46d-45e0-8de0-8a1281a6da5c";

// 通过表单提交参数
HttpResponse responseht = HttpRequest.post(url)
        .form("tokenId", tokenId)
        .form("access_key", access_key)
        .execute();
```

## ✅ 已更新的文件

### 1. 前端服务 (`src/services/userService.js`)
```javascript
// 更新前：只发送tokenId
body: JSON.stringify({ tokenId })

// 更新后：同时发送tokenId和access_key
body: JSON.stringify({ 
  tokenId: tokenId,
  access_key: '2e5295e9-f46d-45e0-8de0-8a1281a6da5c'
})
```

### 2. SSO代理服务器 (`sso-proxy-server.js`)
```javascript
// 更新前：只处理tokenId
const { tokenId } = req.body

// 更新后：处理两个参数
const { tokenId, access_key } = req.body
const accessKey = access_key || '2e5295e9-f46d-45e0-8de0-8a1281a6da5c'

// 表单数据构建（对应Java的.form()方法）
formData.append('tokenId', tokenId)
formData.append('access_key', accessKey)
```

### 3. 测试页面 (`sso-test.html`)
- 添加了Access Key输入框
- 更新了测试接口调用逻辑
- 验证两个参数都不为空

### 4. 测试脚本
- `test-sso.js`: 更新了Node.js测试脚本
- `test-sso.bat`: 更新了Windows测试脚本

### 5. 文档更新
- `docs/sso-integration-guide.md`: 更新了API接口说明
- 添加了参数配置说明

## 🔧 技术实现对比

| 功能 | Java实现 | Vue.js实现 |
|------|----------|------------|
| 请求方式 | `HttpRequest.post()` | `fetch()` POST |
| 参数格式 | `.form()` 表单提交 | URLSearchParams 表单 |
| tokenId参数 | `.form("tokenId", tokenId)` | `formData.append('tokenId', tokenId)` |
| access_key参数 | `.form("access_key", access_key)` | `formData.append('access_key', accessKey)` |
| 请求头 | 自动设置 | `'Content-Type': 'application/x-www-form-urlencoded'` |

## 🧪 测试验证

### 1. 使用测试页面
1. 打开 `sso-test.html`
2. 输入Token ID和Access Key
3. 点击"测试SSO验证接口"

### 2. 使用命令行测试
```bash
# Windows
test-sso.bat

# 或使用PowerShell直接测试
powershell -Command "
$body = '{\"tokenId\": \"test-token-123\", \"access_key\": \"2e5295e9-f46d-45e0-8de0-8a1281a6da5c\"}'
Invoke-WebRequest -Uri 'http://localhost:4003/api/sso/validate' -Method POST -Body $body -ContentType 'application/json'
"
```

### 3. 浏览器测试
访问：`http://localhost:3000?tokenId=YOUR_TOKEN`

前端会自动使用默认的access_key进行验证。

## 🔒 安全考虑

1. **access_key保护**：
   - 当前硬编码在代码中（与Java版本一致）
   - 生产环境建议通过环境变量配置
   - 可以考虑加密存储

2. **参数验证**：
   - 代理服务器验证两个参数都不为空
   - 支持传入自定义access_key（向后兼容）

3. **日志记录**：
   - 记录tokenId和access_key（脱敏处理）
   - 便于调试和审计

## 🚀 使用方法

### 标准SSO登录
```
http://localhost:3000?tokenId=YOUR_SSO_TOKEN
```

系统会自动使用默认的access_key进行验证。

### 自定义access_key（高级用法）
如果需要使用不同的access_key，可以在前端代码中修改：

```javascript
// 在userService.js中修改
body: JSON.stringify({ 
  tokenId: tokenId,
  access_key: 'your-custom-access-key'
})
```

## 📝 注意事项

1. **向后兼容**：代理服务器支持只传tokenId的旧版本调用
2. **默认值**：如果没有传access_key，使用默认值
3. **错误处理**：增强了参数验证和错误提示
4. **日志输出**：增加了详细的调试信息

## 🔄 升级步骤

如果您之前部署了旧版本，请按以下步骤升级：

1. **停止服务**：
   ```bash
   # 停止SSO代理服务器
   Ctrl+C
   ```

2. **更新代码**：
   - 所有文件已自动更新

3. **重启服务**：
   ```bash
   # 重新启动
   start-sso-dev.bat
   ```

4. **验证功能**：
   - 打开测试页面验证
   - 测试实际SSO登录流程

## ✅ 验证清单

- [ ] SSO代理服务器正常启动（端口4003）
- [ ] 健康检查接口正常：`http://localhost:4003/health`
- [ ] 测试页面可以正常发送两个参数
- [ ] 前端应用可以检测tokenId并自动登录
- [ ] 错误处理正常工作
- [ ] 日志输出包含两个参数信息

现在SSO功能已完全对应Java版本的实现，支持tokenId和access_key两个参数的验证！
