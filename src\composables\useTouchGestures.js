/**
 * 触摸手势处理工具
 * 用于移动端的滑动、点击等手势识别
 */

import { ref, onMounted, onUnmounted } from 'vue'

/**
 * 触摸手势处理 composable
 * @param {Object} options - 配置选项
 * @returns {Object} 手势处理相关的方法和状态
 */
export function useTouchGestures(options = {}) {
  const {
    threshold = 50,           // 滑动阈值（像素）
    timeThreshold = 300,      // 时间阈值（毫秒）
    preventScroll = false     // 是否阻止默认滚动
  } = options

  // 触摸状态
  const touchState = ref({
    startX: 0,
    startY: 0,
    endX: 0,
    endY: 0,
    startTime: 0,
    endTime: 0,
    isTouching: false
  })

  // 手势识别结果
  const gestureResult = ref({
    type: null,           // 'swipe', 'tap', 'longpress'
    direction: null,      // 'left', 'right', 'up', 'down'
    distance: 0,
    duration: 0
  })

  /**
   * 处理触摸开始
   * @param {TouchEvent} event - 触摸事件
   */
  const handleTouchStart = (event) => {
    if (preventScroll) {
      event.preventDefault()
    }

    const touch = event.touches[0]
    touchState.value = {
      startX: touch.clientX,
      startY: touch.clientY,
      endX: touch.clientX,
      endY: touch.clientY,
      startTime: Date.now(),
      endTime: Date.now(),
      isTouching: true
    }

    // 重置手势结果
    gestureResult.value = {
      type: null,
      direction: null,
      distance: 0,
      duration: 0
    }
  }

  /**
   * 处理触摸移动
   * @param {TouchEvent} event - 触摸事件
   */
  const handleTouchMove = (event) => {
    if (!touchState.value.isTouching) return

    if (preventScroll) {
      event.preventDefault()
    }

    const touch = event.touches[0]
    touchState.value.endX = touch.clientX
    touchState.value.endY = touch.clientY
    touchState.value.endTime = Date.now()
  }

  /**
   * 处理触摸结束
   * @param {TouchEvent} event - 触摸事件
   */
  const handleTouchEnd = (event) => {
    if (!touchState.value.isTouching) return

    if (preventScroll) {
      event.preventDefault()
    }

    touchState.value.isTouching = false
    touchState.value.endTime = Date.now()

    // 分析手势
    analyzeGesture()
  }

  /**
   * 分析手势类型
   */
  const analyzeGesture = () => {
    const { startX, startY, endX, endY, startTime, endTime } = touchState.value
    
    const deltaX = endX - startX
    const deltaY = endY - startY
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
    const duration = endTime - startTime

    gestureResult.value.distance = distance
    gestureResult.value.duration = duration

    // 判断手势类型
    if (distance < threshold) {
      // 距离太小，判断为点击
      if (duration < timeThreshold) {
        gestureResult.value.type = 'tap'
      } else {
        gestureResult.value.type = 'longpress'
      }
    } else {
      // 距离足够，判断为滑动
      gestureResult.value.type = 'swipe'
      
      // 判断滑动方向
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        // 水平滑动
        gestureResult.value.direction = deltaX > 0 ? 'right' : 'left'
      } else {
        // 垂直滑动
        gestureResult.value.direction = deltaY > 0 ? 'down' : 'up'
      }
    }
  }

  /**
   * 绑定触摸事件到元素
   * @param {HTMLElement} element - 目标元素
   */
  const bindTouchEvents = (element) => {
    if (!element) return

    element.addEventListener('touchstart', handleTouchStart, { passive: !preventScroll })
    element.addEventListener('touchmove', handleTouchMove, { passive: !preventScroll })
    element.addEventListener('touchend', handleTouchEnd, { passive: !preventScroll })
  }

  /**
   * 解绑触摸事件
   * @param {HTMLElement} element - 目标元素
   */
  const unbindTouchEvents = (element) => {
    if (!element) return

    element.removeEventListener('touchstart', handleTouchStart)
    element.removeEventListener('touchmove', handleTouchMove)
    element.removeEventListener('touchend', handleTouchEnd)
  }

  /**
   * 检查是否为指定类型的手势
   * @param {string} type - 手势类型
   * @param {string} direction - 方向（可选）
   * @returns {boolean}
   */
  const isGesture = (type, direction = null) => {
    if (gestureResult.value.type !== type) return false
    if (direction && gestureResult.value.direction !== direction) return false
    return true
  }

  /**
   * 重置手势状态
   */
  const resetGesture = () => {
    touchState.value = {
      startX: 0,
      startY: 0,
      endX: 0,
      endY: 0,
      startTime: 0,
      endTime: 0,
      isTouching: false
    }

    gestureResult.value = {
      type: null,
      direction: null,
      distance: 0,
      duration: 0
    }
  }

  return {
    // 状态
    touchState,
    gestureResult,
    
    // 方法
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    bindTouchEvents,
    unbindTouchEvents,
    isGesture,
    resetGesture,
    
    // 便捷方法
    isTap: () => isGesture('tap'),
    isLongPress: () => isGesture('longpress'),
    isSwipeLeft: () => isGesture('swipe', 'left'),
    isSwipeRight: () => isGesture('swipe', 'right'),
    isSwipeUp: () => isGesture('swipe', 'up'),
    isSwipeDown: () => isGesture('swipe', 'down')
  }
}

// 默认导出
export default useTouchGestures
