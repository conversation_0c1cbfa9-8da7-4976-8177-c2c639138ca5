/**
 * 动态水印功能测试
 */

import { 
  generateDynamicWatermarkText, 
  getDynamicWatermarkConfig, 
  getWatermarkConfig,
  refreshAllDynamicWatermarks 
} from '../config/watermark.js'
import { getUserService } from '../services/userService.js'

/**
 * 测试动态水印文本生成
 */
export async function testDynamicWatermarkGeneration() {
  console.log('=== 测试动态水印文本生成 ===')
  
  try {
    const userService = getUserService()
    
    // 测试用户登录状态下的水印
    await userService.login({
      username: '测试用户',
      employeeId: 'TEST001'
    })
    
    const knowledgeText = await generateDynamicWatermarkText('知识中心')
    const businessText = await generateDynamicWatermarkText('业务域')
    const functionText = await generateDynamicWatermarkText('职能域')
    
    console.log('✅ 知识中心水印:', knowledgeText)
    console.log('✅ 业务域水印:', businessText)
    console.log('✅ 职能域水印:', functionText)
    
    // 验证水印格式
    const expectedPattern = /^(知识中心|业务域|职能域) - 测试用户-TEST001-\d{4}\/\d{2}\/\d{2} \d{2}:\d{2}$/
    
    const tests = [
      { name: '知识中心', text: knowledgeText },
      { name: '业务域', text: businessText },
      { name: '职能域', text: functionText }
    ]
    
    tests.forEach(test => {
      if (expectedPattern.test(test.text)) {
        console.log(`✅ ${test.name}水印格式正确`)
      } else {
        console.error(`❌ ${test.name}水印格式错误:`, test.text)
      }
    })
    
    return true
  } catch (error) {
    console.error('❌ 动态水印生成测试失败:', error)
    return false
  }
}

/**
 * 测试未登录状态下的水印
 */
export async function testWatermarkWhenNotLoggedIn() {
  console.log('=== 测试未登录状态水印 ===')
  
  try {
    const userService = getUserService()
    
    // 确保用户已退出
    await userService.logout()
    
    const knowledgeText = await generateDynamicWatermarkText('知识中心')
    const businessText = await generateDynamicWatermarkText('业务域')
    const functionText = await generateDynamicWatermarkText('职能域')
    
    console.log('✅ 知识中心水印（未登录）:', knowledgeText)
    console.log('✅ 业务域水印（未登录）:', businessText)
    console.log('✅ 职能域水印（未登录）:', functionText)
    
    // 验证未登录水印格式
    const expectedTexts = [
      '知识中心 - 未登录用户',
      '业务域 - 未登录用户',
      '职能域 - 未登录用户'
    ]
    
    const actualTexts = [knowledgeText, businessText, functionText]
    
    let allCorrect = true
    expectedTexts.forEach((expected, index) => {
      if (actualTexts[index] === expected) {
        console.log(`✅ 未登录水印 ${index + 1} 格式正确`)
      } else {
        console.error(`❌ 未登录水印 ${index + 1} 格式错误:`, actualTexts[index])
        allCorrect = false
      }
    })
    
    return allCorrect
  } catch (error) {
    console.error('❌ 未登录状态水印测试失败:', error)
    return false
  }
}

/**
 * 测试水印配置获取
 */
export async function testWatermarkConfigRetrieval() {
  console.log('=== 测试水印配置获取 ===')
  
  try {
    const userService = getUserService()
    
    // 登录用户
    await userService.login({
      username: '配置测试',
      employeeId: 'CONFIG001'
    })
    
    // 测试动态配置获取
    const dynamicConfig = await getDynamicWatermarkConfig('knowledge')
    console.log('✅ 动态配置获取成功:', dynamicConfig?.text)
    
    // 测试静态配置获取
    const staticConfig = getWatermarkConfig('knowledge')
    console.log('✅ 静态配置获取成功:', staticConfig?.text)
    
    // 验证配置结构
    const requiredFields = ['enabled', 'text', 'fontSize', 'opacity', 'color']
    let configValid = true
    
    requiredFields.forEach(field => {
      if (!(field in dynamicConfig)) {
        console.error(`❌ 动态配置缺少字段: ${field}`)
        configValid = false
      }
      if (!(field in staticConfig)) {
        console.error(`❌ 静态配置缺少字段: ${field}`)
        configValid = false
      }
    })
    
    if (configValid) {
      console.log('✅ 配置结构验证通过')
    }
    
    return configValid
  } catch (error) {
    console.error('❌ 水印配置获取测试失败:', error)
    return false
  }
}

/**
 * 测试水印刷新功能
 */
export async function testWatermarkRefresh() {
  console.log('=== 测试水印刷新功能 ===')
  
  try {
    const userService = getUserService()
    
    // 登录用户
    await userService.login({
      username: '刷新测试',
      employeeId: 'REFRESH001'
    })
    
    // 刷新所有水印
    const results = await refreshAllDynamicWatermarks()
    console.log('✅ 水印刷新结果:', results)
    
    // 验证刷新结果
    if (Array.isArray(results) && results.length === 3) {
      console.log('✅ 刷新结果数量正确')
      
      const modules = ['knowledge', 'business', 'function']
      let allRefreshed = true
      
      results.forEach((result, index) => {
        if (result.module === modules[index] && result.text) {
          console.log(`✅ ${result.module} 模块刷新成功`)
        } else {
          console.error(`❌ ${result.module} 模块刷新失败`)
          allRefreshed = false
        }
      })
      
      return allRefreshed
    } else {
      console.error('❌ 刷新结果格式错误')
      return false
    }
  } catch (error) {
    console.error('❌ 水印刷新测试失败:', error)
    return false
  }
}

/**
 * 运行所有测试
 */
export async function runAllTests() {
  console.log('开始运行动态水印功能测试...\n')
  
  const tests = [
    { name: '动态水印文本生成', fn: testDynamicWatermarkGeneration },
    { name: '未登录状态水印', fn: testWatermarkWhenNotLoggedIn },
    { name: '水印配置获取', fn: testWatermarkConfigRetrieval },
    { name: '水印刷新功能', fn: testWatermarkRefresh }
  ]
  
  const results = []
  
  for (const test of tests) {
    console.log(`\n${'='.repeat(50)}`)
    console.log(`运行测试: ${test.name}`)
    console.log('='.repeat(50))
    
    try {
      const result = await test.fn()
      results.push({ name: test.name, passed: result })
      
      if (result) {
        console.log(`✅ 测试通过: ${test.name}`)
      } else {
        console.log(`❌ 测试失败: ${test.name}`)
      }
    } catch (error) {
      console.error(`❌ 测试异常: ${test.name}`, error)
      results.push({ name: test.name, passed: false, error })
    }
  }
  
  // 输出测试总结
  console.log(`\n${'='.repeat(50)}`)
  console.log('测试总结')
  console.log('='.repeat(50))
  
  const passed = results.filter(r => r.passed).length
  const total = results.length
  
  console.log(`总测试数: ${total}`)
  console.log(`通过数: ${passed}`)
  console.log(`失败数: ${total - passed}`)
  console.log(`通过率: ${((passed / total) * 100).toFixed(1)}%`)
  
  results.forEach(result => {
    const status = result.passed ? '✅' : '❌'
    console.log(`${status} ${result.name}`)
  })
  
  return results
}

// 导出测试函数供外部调用
export default {
  runAllTests,
  testDynamicWatermarkGeneration,
  testWatermarkWhenNotLoggedIn,
  testWatermarkConfigRetrieval,
  testWatermarkRefresh
}
