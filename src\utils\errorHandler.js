/**
 * 错误处理和重试机制工具
 * 提供统一的错误处理、重试逻辑和错误恢复策略
 */

import logger, { LogLevel } from './logger.js'

/**
 * 错误类型枚举
 */
export const ErrorType = {
  NETWORK: 'network',
  REDIS: 'redis',
  STORAGE: 'storage',
  VALIDATION: 'validation',
  PERMISSION: 'permission',
  TIMEOUT: 'timeout',
  UNKNOWN: 'unknown'
}

/**
 * 重试策略枚举
 */
export const RetryStrategy = {
  NONE: 'none',
  FIXED: 'fixed',
  EXPONENTIAL: 'exponential',
  LINEAR: 'linear'
}

/**
 * 自定义错误类
 */
export class BodorAIError extends Error {
  constructor(message, type = ErrorType.UNKNOWN, code = null, details = null) {
    super(message)
    this.name = 'BodorAIError'
    this.type = type
    this.code = code
    this.details = details
    this.timestamp = new Date()
  }
}

/**
 * 错误处理器类
 */
class ErrorHandler {
  constructor() {
    this.errorCounts = new Map()
    this.errorHistory = []
    this.maxHistorySize = 100
    
    // 错误恢复策略
    this.recoveryStrategies = new Map()
    this.setupDefaultRecoveryStrategies()
  }

  /**
   * 设置默认恢复策略
   */
  setupDefaultRecoveryStrategies() {
    // Redis连接错误恢复策略
    this.recoveryStrategies.set(ErrorType.REDIS, async (error, context) => {
      logger.warn('Redis错误，尝试重连', { error: error.message, context })
      
      if (context.connectionManager) {
        const reconnected = await context.connectionManager.reconnect()
        if (reconnected) {
          logger.info('Redis重连成功')
          return { success: true, action: 'reconnected' }
        }
      }
      
      logger.warn('Redis重连失败，切换到本地存储')
      return { success: false, action: 'fallback_to_local' }
    })

    // 网络错误恢复策略
    this.recoveryStrategies.set(ErrorType.NETWORK, async (error, context) => {
      logger.warn('网络错误，等待重试', { error: error.message })
      
      // 等待一段时间后重试
      await this.delay(2000)
      return { success: false, action: 'retry_later' }
    })

    // 存储错误恢复策略
    this.recoveryStrategies.set(ErrorType.STORAGE, async (error, context) => {
      logger.warn('存储错误，尝试清理缓存', { error: error.message })
      
      try {
        // 清理可能损坏的缓存数据
        if (context.clearCache) {
          await context.clearCache()
        }
        return { success: true, action: 'cache_cleared' }
      } catch (clearError) {
        logger.error('清理缓存失败', clearError)
        return { success: false, action: 'cache_clear_failed' }
      }
    })
  }

  /**
   * 处理错误
   */
  async handleError(error, context = {}) {
    const errorInfo = this.analyzeError(error)
    
    // 记录错误
    this.recordError(errorInfo)
    
    // 记录日志
    logger.error(
      `错误处理: ${errorInfo.message}`,
      {
        type: errorInfo.type,
        code: errorInfo.code,
        details: errorInfo.details,
        context
      },
      'error'
    )

    // 尝试恢复
    const recoveryResult = await this.attemptRecovery(errorInfo, context)
    
    return {
      error: errorInfo,
      recovery: recoveryResult,
      shouldRetry: recoveryResult.success || recoveryResult.action === 'retry_later'
    }
  }

  /**
   * 分析错误
   */
  analyzeError(error) {
    let type = ErrorType.UNKNOWN
    let code = null
    let details = null

    if (error instanceof BodorAIError) {
      return {
        message: error.message,
        type: error.type,
        code: error.code,
        details: error.details,
        timestamp: error.timestamp,
        stack: error.stack
      }
    }

    // 分析标准错误
    const message = error.message || error.toString()

    // 网络错误
    if (message.includes('fetch') || message.includes('network') || 
        message.includes('ECONNREFUSED') || message.includes('timeout')) {
      type = ErrorType.NETWORK
    }
    // Redis错误
    else if (message.includes('Redis') || message.includes('redis') ||
             message.includes('ECONNRESET')) {
      type = ErrorType.REDIS
    }
    // 存储错误
    else if (message.includes('localStorage') || message.includes('storage') ||
             message.includes('quota')) {
      type = ErrorType.STORAGE
    }
    // 验证错误
    else if (message.includes('validation') || message.includes('invalid') ||
             message.includes('required')) {
      type = ErrorType.VALIDATION
    }
    // 权限错误
    else if (message.includes('permission') || message.includes('unauthorized') ||
             message.includes('forbidden')) {
      type = ErrorType.PERMISSION
    }

    return {
      message,
      type,
      code,
      details,
      timestamp: new Date(),
      stack: error.stack
    }
  }

  /**
   * 记录错误
   */
  recordError(errorInfo) {
    // 更新错误计数
    const key = `${errorInfo.type}:${errorInfo.message}`
    this.errorCounts.set(key, (this.errorCounts.get(key) || 0) + 1)

    // 添加到历史记录
    this.errorHistory.push(errorInfo)
    
    // 限制历史记录大小
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(-this.maxHistorySize)
    }
  }

  /**
   * 尝试恢复
   */
  async attemptRecovery(errorInfo, context) {
    const strategy = this.recoveryStrategies.get(errorInfo.type)
    
    if (strategy) {
      try {
        return await strategy(errorInfo, context)
      } catch (recoveryError) {
        logger.error('错误恢复策略执行失败', recoveryError)
        return { success: false, action: 'recovery_failed', error: recoveryError }
      }
    }

    return { success: false, action: 'no_strategy' }
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 获取错误统计
   */
  getErrorStats() {
    const stats = {
      totalErrors: this.errorHistory.length,
      errorsByType: {},
      recentErrors: this.errorHistory.slice(-10),
      topErrors: []
    }

    // 按类型统计
    this.errorHistory.forEach(error => {
      stats.errorsByType[error.type] = (stats.errorsByType[error.type] || 0) + 1
    })

    // 最常见的错误
    const sortedErrors = Array.from(this.errorCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
    
    stats.topErrors = sortedErrors.map(([key, count]) => ({ key, count }))

    return stats
  }

  /**
   * 清除错误历史
   */
  clearErrorHistory() {
    this.errorHistory = []
    this.errorCounts.clear()
  }
}

/**
 * 重试执行器
 */
export class RetryExecutor {
  constructor(options = {}) {
    this.maxRetries = options.maxRetries || 3
    this.strategy = options.strategy || RetryStrategy.EXPONENTIAL
    this.baseDelay = options.baseDelay || 1000
    this.maxDelay = options.maxDelay || 30000
    this.retryCondition = options.retryCondition || this.defaultRetryCondition
  }

  /**
   * 默认重试条件
   */
  defaultRetryCondition(error, attempt) {
    // 网络错误和Redis错误可以重试
    if (error instanceof BodorAIError) {
      return error.type === ErrorType.NETWORK || error.type === ErrorType.REDIS
    }
    
    // 标准错误的重试条件
    const message = error.message || error.toString()
    return message.includes('network') || 
           message.includes('timeout') || 
           message.includes('ECONNREFUSED') ||
           message.includes('Redis')
  }

  /**
   * 计算延迟时间
   */
  calculateDelay(attempt) {
    switch (this.strategy) {
      case RetryStrategy.FIXED:
        return this.baseDelay
      
      case RetryStrategy.LINEAR:
        return Math.min(this.baseDelay * attempt, this.maxDelay)
      
      case RetryStrategy.EXPONENTIAL:
        return Math.min(this.baseDelay * Math.pow(2, attempt - 1), this.maxDelay)
      
      default:
        return this.baseDelay
    }
  }

  /**
   * 执行带重试的操作
   */
  async execute(operation, context = {}) {
    let lastError = null
    
    for (let attempt = 1; attempt <= this.maxRetries + 1; attempt++) {
      try {
        logger.debug(`执行操作，尝试次数: ${attempt}`, { context })
        const result = await operation()
        
        if (attempt > 1) {
          logger.info(`操作在第${attempt}次尝试后成功`, { context })
        }
        
        return result
      } catch (error) {
        lastError = error
        
        logger.warn(`操作失败，尝试次数: ${attempt}`, { 
          error: error.message, 
          context 
        })

        // 检查是否应该重试
        if (attempt <= this.maxRetries && this.retryCondition(error, attempt)) {
          const delay = this.calculateDelay(attempt)
          logger.debug(`等待${delay}ms后重试`)
          await this.delay(delay)
        } else {
          break
        }
      }
    }

    // 所有重试都失败了
    logger.error(`操作最终失败，已尝试${this.maxRetries + 1}次`, { 
      error: lastError.message, 
      context 
    })
    
    throw lastError
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// 创建默认实例
const defaultErrorHandler = new ErrorHandler()

// 导出
export { ErrorHandler, defaultErrorHandler }
export default defaultErrorHandler
