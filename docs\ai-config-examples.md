# AI配置示例

## 历史对话轮数配置示例

### 基础配置示例

```javascript
// src/config/ai.js

// 知识中心 - 适中的历史上下文
export const knowledgeAIConfig = {
  baseURL: '/api/v1',
  apiKey: 'your-api-key',
  model: 'Qwen3-235B-A22B',
  maxTokens: 20000,
  temperature: 0.7,
  timeout: 40000,
  
  // 历史对话配置
  historyTurns: 3, // 携带3轮历史对话
  
  systemPrompt: '你是一个专业的知识助手...',
  module: 'knowledge',
  enableMockMode: true
}

// 业务域 - 更多历史上下文
export const businessAIConfig = {
  baseURL: '/api/v1',
  apiKey: 'your-api-key',
  model: 'Qwen3-235B-A22B',
  maxTokens: 20000,
  temperature: 0.6,
  timeout: 40000,
  
  // 历史对话配置
  historyTurns: 5, // 业务咨询需要更多上下文
  
  systemPrompt: '你是一个专业的业务顾问...',
  module: 'business',
  enableMockMode: true
}

// 职能域 - 较少历史上下文
export const functionAIConfig = {
  baseURL: '/api/v1',
  apiKey: 'your-api-key',
  model: 'Qwen3-235B-A22B',
  maxTokens: 20000,
  temperature: 0.5,
  timeout: 40000,
  
  // 历史对话配置
  historyTurns: 2, // 功能操作不需要太多历史
  
  systemPrompt: '你是一个专业的功能助手...',
  module: 'function',
  enableMockMode: true
}
```

### 特殊场景配置

#### 1. 不携带历史对话（独立问答）

```javascript
export const independentQAConfig = {
  // 基础配置...
  historyTurns: 0, // 每次都是独立的问答
  
  systemPrompt: '请针对用户的问题提供独立的回答，不需要考虑之前的对话内容。'
}
```

**适用场景：**
- 快速问答系统
- 搜索式查询
- 成本敏感的应用

#### 2. 携带全部历史对话（深度分析）

```javascript
export const deepAnalysisConfig = {
  // 基础配置...
  historyTurns: -1, // 携带全部历史对话
  
  systemPrompt: '请基于完整的对话历史，提供深入的分析和建议。'
}
```

**适用场景：**
- 长期咨询服务
- 复杂问题分析
- 个性化推荐

#### 3. 动态配置（根据会话类型）

```javascript
// 动态配置函数
const getDynamicConfig = (sessionType) => {
  const baseConfig = {
    baseURL: '/api/v1',
    apiKey: 'your-api-key',
    model: 'Qwen3-235B-A22B',
    maxTokens: 20000,
    temperature: 0.7,
    timeout: 40000,
    enableMockMode: true
  }
  
  const typeConfigs = {
    'quick-help': {
      ...baseConfig,
      historyTurns: 1,
      systemPrompt: '提供简洁快速的帮助。'
    },
    'detailed-consultation': {
      ...baseConfig,
      historyTurns: 8,
      systemPrompt: '基于详细的对话历史提供深入咨询。'
    },
    'troubleshooting': {
      ...baseConfig,
      historyTurns: 5,
      systemPrompt: '基于问题描述和历史信息提供故障排除建议。'
    }
  }
  
  return typeConfigs[sessionType] || baseConfig
}
```

### 环境变量配置示例

#### 开发环境 (.env.development)

```bash
# 开发环境 - 可以使用更多历史进行测试
VITE_AI_BASE_URL=/api/v1
VITE_AI_API_KEY=your-dev-api-key
VITE_AI_MODEL=Qwen3-235B-A22B
VITE_AI_ENABLE_MOCK=true
VITE_AI_HISTORY_TURNS=5

# Redis配置
VITE_REDIS_HOST=*************
VITE_REDIS_PORT=6379
VITE_REDIS_PASSWORD=X4gN7
```

#### 生产环境 (.env.production)

```bash
# 生产环境 - 平衡性能和效果
VITE_AI_BASE_URL=http://***********:3006/api/v1
VITE_AI_API_KEY=your-prod-api-key
VITE_AI_MODEL=Qwen3-235B-A22B
VITE_AI_ENABLE_MOCK=false
VITE_AI_HISTORY_TURNS=3

# Redis配置
VITE_REDIS_HOST=*************
VITE_REDIS_PORT=6379
VITE_REDIS_PASSWORD=X4gN7
```

#### 测试环境 (.env.test)

```bash
# 测试环境 - 使用模拟模式
VITE_AI_BASE_URL=/api/v1
VITE_AI_API_KEY=test-api-key
VITE_AI_MODEL=test-model
VITE_AI_ENABLE_MOCK=true
VITE_AI_HISTORY_TURNS=2
```

### 性能优化配置

#### 高性能配置（快速响应）

```javascript
export const highPerformanceConfig = {
  baseURL: '/api/v1',
  apiKey: 'your-api-key',
  model: 'Qwen3-235B-A22B',
  
  // 性能优化参数
  maxTokens: 1000,      // 限制输出长度
  temperature: 0.3,     // 降低随机性，提高一致性
  timeout: 20000,       // 较短的超时时间
  historyTurns: 1,      // 最少的历史上下文
  
  systemPrompt: '请提供简洁准确的回答。'
}
```

#### 高质量配置（详细回答）

```javascript
export const highQualityConfig = {
  baseURL: '/api/v1',
  apiKey: 'your-api-key',
  model: 'Qwen3-235B-A22B',
  
  // 质量优化参数
  maxTokens: 4000,      // 允许更长的输出
  temperature: 0.7,     // 平衡创造性和准确性
  timeout: 60000,       // 更长的超时时间
  historyTurns: 8,      // 更多的历史上下文
  
  systemPrompt: '请提供详细、准确、有深度的回答，充分利用对话历史。'
}
```

### 成本控制配置

#### 经济模式

```javascript
export const economicConfig = {
  baseURL: '/api/v1',
  apiKey: 'your-api-key',
  model: 'Qwen3-235B-A22B',
  
  // 成本控制参数
  maxTokens: 500,       // 严格限制输出长度
  temperature: 0.1,     // 最低随机性
  timeout: 15000,       // 短超时
  historyTurns: 0,      // 不携带历史
  
  systemPrompt: '请提供简洁的回答，直接回答问题要点。'
}
```

#### 标准模式

```javascript
export const standardConfig = {
  baseURL: '/api/v1',
  apiKey: 'your-api-key',
  model: 'Qwen3-235B-A22B',
  
  // 标准参数
  maxTokens: 2000,      // 适中的输出长度
  temperature: 0.5,     // 适中的随机性
  timeout: 30000,       // 标准超时
  historyTurns: 3,      // 适中的历史上下文
  
  systemPrompt: '请提供准确、有用的回答。'
}
```

### 使用示例

#### 在组件中使用不同配置

```javascript
// MainLayout.vue
import { getAIConfig } from '@/config/ai.js'

// 根据模块获取配置
const aiService = getAIService(currentModule.value)

// 或者动态选择配置
const getConfigByScenario = (scenario) => {
  switch (scenario) {
    case 'quick':
      return { ...getAIConfig('knowledge'), historyTurns: 1 }
    case 'detailed':
      return { ...getAIConfig('knowledge'), historyTurns: 8 }
    case 'economic':
      return { ...getAIConfig('knowledge'), historyTurns: 0, maxTokens: 500 }
    default:
      return getAIConfig('knowledge')
  }
}
```

#### 运行时配置调整

```javascript
// 根据用户偏好调整配置
const adjustConfigForUser = (baseConfig, userPreferences) => {
  return {
    ...baseConfig,
    historyTurns: userPreferences.contextLevel || baseConfig.historyTurns,
    temperature: userPreferences.creativity || baseConfig.temperature,
    maxTokens: userPreferences.responseLength || baseConfig.maxTokens
  }
}
```

### 监控和调试

#### 配置验证

```javascript
// 验证配置的有效性
const validateConfig = (config) => {
  const warnings = []
  
  if (config.historyTurns > 10) {
    warnings.push('历史轮数过多可能影响性能')
  }
  
  if (config.historyTurns === 0 && config.temperature > 0.8) {
    warnings.push('无历史上下文时高温度可能导致不一致回答')
  }
  
  if (config.maxTokens > 4000 && config.historyTurns > 5) {
    warnings.push('高token限制和多历史轮数可能导致成本过高')
  }
  
  return warnings
}
```

#### 性能监控

```javascript
// 监控配置效果
const monitorConfig = (config, metrics) => {
  console.log('配置性能指标:', {
    historyTurns: config.historyTurns,
    avgResponseTime: metrics.avgResponseTime,
    avgTokenUsage: metrics.avgTokenUsage,
    userSatisfaction: metrics.userSatisfaction
  })
}
```

这些配置示例可以帮助您根据不同的使用场景和需求，选择最合适的历史对话轮数配置。
