/**
 * 用户状态管理服务
 * 负责用户登录状态管理、用户信息存储和会话隔离
 */

// 存储键名常量
const USER_STORAGE_KEYS = {
  CURRENT_USER: 'bodorai_current_user',     // 当前登录用户
  USER_SESSIONS: 'bodorai_user_sessions',   // 用户会话映射
  LOGIN_HISTORY: 'bodorai_login_history'    // 登录历史
}

/**
 * 用户服务类
 */
class UserService {
  constructor() {
    this.currentUser = null
    this.isInitialized = false
    this.init()
  }

  /**
   * 初始化用户服务
   */
  init() {
    try {
      // 从存储中恢复当前用户信息
      const savedUser = localStorage.getItem(USER_STORAGE_KEYS.CURRENT_USER)
      if (savedUser) {
        this.currentUser = JSON.parse(savedUser)
        console.log('恢复用户登录状态:', this.currentUser)
      }
      this.isInitialized = true
    } catch (error) {
      console.error('初始化用户服务失败:', error)
      this.isInitialized = true
    }
  }

  /**
   * 用户登录
   * @param {Object} userData - 用户数据 {username, employeeId}
   * @returns {Promise<boolean>} 登录是否成功
   */
  async login(userData) {
    try {
      // 验证用户数据
      if (!userData.username || !userData.employeeId) {
        throw new Error('用户名和工号不能为空')
      }

      // 生成用户唯一标识
      const userKey = this.generateUserKey(userData.username, userData.employeeId)

      // 创建完整的用户信息
      const userInfo = {
        ...userData,
        userKey,
        loginTime: new Date().toISOString(),
        lastActiveTime: new Date().toISOString()
      }

      // 保存当前用户信息
      this.currentUser = userInfo
      localStorage.setItem(USER_STORAGE_KEYS.CURRENT_USER, JSON.stringify(userInfo))

      // 记录登录历史
      this.recordLoginHistory(userInfo)

      console.log('用户登录成功:', userInfo)
      return true
    } catch (error) {
      console.error('用户登录失败:', error)
      throw error
    }
  }

  /**
   * 单点登录验证
   * @param {string} tokenId - SSO token
   * @returns {Promise<Object>} 验证结果 {success: boolean, userData?: Object, error?: string}
   */
  async ssoLogin(tokenId) {
    try {
      if (!tokenId || tokenId.trim() === '') {
        throw new Error('tokenId不能为空')
      }

      console.log('开始SSO验证，tokenId:', tokenId)

      // 通过代理服务器发送SSO验证请求
      // 对应Java代码：需要同时发送tokenId和access_key
      const response = await fetch('/api/sso/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          tokenId: tokenId,
          access_key: '2e5295e9-f46d-45e0-8de0-8a1281a6da5c'
        })
      })

      if (!response.ok) {
        throw new Error(`SSO验证请求失败: ${response.status} ${response.statusText}`)
      }

      const jsonResponse = await response.json()
      console.log('SSO验证响应:', jsonResponse)

      // 检查代理服务器的响应
      if (!jsonResponse.success) {
        throw new Error(jsonResponse.error || 'SSO验证失败')
      }

      // 检查SSO服务的响应数据
      if (!jsonResponse.data || !jsonResponse.data.data) {
        throw new Error('SSO响应数据格式错误')
      }

      const { data } = jsonResponse.data
      const uid = data.uid // 用户唯一标识、工号
      const validate = data.validate // true代表该授权有效

      if (!validate) {
        return {
          success: false,
          error: 'SSO token验证失败，授权无效'
        }
      }

      if (!uid) {
        return {
          success: false,
          error: 'SSO验证成功但未获取到用户信息'
        }
      }

      let username1 = uid;

      //==========================================================================
      // 创建AbortController实例用于控制请求
      const controller = new AbortController();
      const signal = controller.signal;

      // 设置3秒超时
      const timeoutId = setTimeout(() => {
        controller.abort(); // 超时后终止请求
      }, 3000);

      try {
        // 发起请求并关联signal
        const response = await fetch('/api/sso/usernamebyjobnumber', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            jobNumber: uid
          }),
          signal // 将AbortController的signal传递给请求
        });

        // 清除超时计时器（请求成功时）
        clearTimeout(timeoutId);

        // 处理响应
        if (!response.ok) {
          throw new Error(`HTTP错误，状态码：${response.status}`);
        }

        const data = await response.json();
        username1 = data.data.username
        console.log('请求成功======username=====:', data);
      } catch (error) {
        // 超时错误处理
        if (error.name === 'AbortError') {
          console.error('请求超时：3秒内未收到响应');
        } else {
          console.error('请求失败:', error);
        }
      }
      //==========================================================================


      // 构建用户数据（这里假设uid就是工号，用户名可以从其他字段获取或使用工号）
      const userData = {
        username: username1, // 优先使用用户名，否则使用工号
        employeeId: uid,
        loginType: 'sso',
        ssoTokenId: tokenId
      }

      // 执行登录
      const loginSuccess = await this.login(userData)

      if (loginSuccess) {
        const currentTime = new Date().toISOString()
        console.log(`单点登录知识库维护时间：${new Date().toLocaleString()}，用户：${JSON.stringify(userData)}`)

        return {
          success: true,
          userData: this.currentUser
        }
      } else {
        return {
          success: false,
          error: '登录处理失败'
        }
      }

    } catch (error) {
      console.error('SSO登录失败:', error)
      return {
        success: false,
        error: error.message || 'SSO登录过程中发生错误'
      }
    }
  }



  /**
   * 用户退出登录
   * @returns {Promise<boolean>} 退出是否成功
   */
  async logout() {
    try {
      if (this.currentUser) {
        console.log('用户退出登录:', this.currentUser.username)
        
        // 清除当前用户信息
        this.currentUser = null
        localStorage.removeItem(USER_STORAGE_KEYS.CURRENT_USER)
      }
      
      return true
    } catch (error) {
      console.error('用户退出登录失败:', error)
      throw error
    }
  }

  /**
   * 获取当前登录用户
   * @returns {Promise<Object|null>} 当前用户信息
   */
  async getCurrentUser() {
    if (!this.isInitialized) {
      await new Promise(resolve => {
        const checkInit = () => {
          if (this.isInitialized) {
            resolve()
          } else {
            setTimeout(checkInit, 10)
          }
        }
        checkInit()
      })
    }

    // 更新最后活跃时间
    if (this.currentUser) {
      this.currentUser.lastActiveTime = new Date().toISOString()
      localStorage.setItem(USER_STORAGE_KEYS.CURRENT_USER, JSON.stringify(this.currentUser))
    }

    return this.currentUser
  }

  /**
   * 检查用户是否已登录
   * @returns {boolean} 是否已登录
   */
  isLoggedIn() {
    return !!this.currentUser
  }

  /**
   * 生成用户唯一标识
   * @param {string} username - 用户名
   * @param {string} employeeId - 工号
   * @returns {string} 用户唯一标识
   */
  generateUserKey(username, employeeId) {
    return `${username}_${employeeId}`.toLowerCase()
  }

  /**
   * 获取用户会话存储键
   * @param {string} userKey - 用户唯一标识
   * @returns {string} 会话存储键
   */
  getUserSessionKey(userKey = null) {
    const key = userKey || this.currentUser?.userKey
    if (!key) {
      throw new Error('用户未登录或用户标识无效')
    }
    return `bodorai_sessions_${key}`
  }

  /**
   * 获取用户当前会话存储键
   * @param {string} userKey - 用户唯一标识
   * @returns {string} 当前会话存储键
   */
  getUserCurrentSessionKey(userKey = null) {
    const key = userKey || this.currentUser?.userKey
    if (!key) {
      throw new Error('用户未登录或用户标识无效')
    }
    return `bodorai_current_sessions_${key}`
  }

  /**
   * 记录登录历史
   * @param {Object} userInfo - 用户信息
   */
  recordLoginHistory(userInfo) {
    try {
      const historyKey = USER_STORAGE_KEYS.LOGIN_HISTORY
      let history = []
      
      const savedHistory = localStorage.getItem(historyKey)
      if (savedHistory) {
        history = JSON.parse(savedHistory)
      }

      // 添加新的登录记录
      history.unshift({
        username: userInfo.username,
        employeeId: userInfo.employeeId,
        userKey: userInfo.userKey,
        loginTime: userInfo.loginTime
      })

      // 只保留最近50条记录
      if (history.length > 50) {
        history = history.slice(0, 50)
      }

      localStorage.setItem(historyKey, JSON.stringify(history))
    } catch (error) {
      console.error('记录登录历史失败:', error)
    }
  }

  /**
   * 获取登录历史
   * @returns {Array} 登录历史列表
   */
  getLoginHistory() {
    try {
      const savedHistory = localStorage.getItem(USER_STORAGE_KEYS.LOGIN_HISTORY)
      return savedHistory ? JSON.parse(savedHistory) : []
    } catch (error) {
      console.error('获取登录历史失败:', error)
      return []
    }
  }

  /**
   * 清理过期的用户数据
   * @param {number} daysToKeep - 保留天数，默认30天
   */
  cleanupExpiredData(daysToKeep = 30) {
    try {
      const cutoffTime = new Date()
      cutoffTime.setDate(cutoffTime.getDate() - daysToKeep)

      // 清理登录历史
      const history = this.getLoginHistory()
      const validHistory = history.filter(record => {
        return new Date(record.loginTime) > cutoffTime
      })

      if (validHistory.length !== history.length) {
        localStorage.setItem(USER_STORAGE_KEYS.LOGIN_HISTORY, JSON.stringify(validHistory))
        console.log(`清理了 ${history.length - validHistory.length} 条过期登录记录`)
      }

      // 清理过期的用户会话数据
      const allKeys = Object.keys(localStorage)
      const sessionKeys = allKeys.filter(key => key.startsWith('bodorai_sessions_'))
      
      sessionKeys.forEach(key => {
        try {
          const data = localStorage.getItem(key)
          if (data) {
            const sessionData = JSON.parse(data)
            // 检查会话数据的最后更新时间
            let hasValidSessions = false
            
            if (sessionData.moduleSessions) {
              Object.values(sessionData.moduleSessions).forEach(sessions => {
                if (Array.isArray(sessions)) {
                  sessions.forEach(session => {
                    if (session.updatedAt && new Date(session.updatedAt) > cutoffTime) {
                      hasValidSessions = true
                    }
                  })
                }
              })
            }

            if (!hasValidSessions) {
              localStorage.removeItem(key)
              localStorage.removeItem(key.replace('sessions_', 'current_sessions_'))
              console.log(`清理了过期的用户会话数据: ${key}`)
            }
          }
        } catch (error) {
          console.error(`清理会话数据失败 ${key}:`, error)
        }
      })

    } catch (error) {
      console.error('清理过期数据失败:', error)
    }
  }

  /**
   * 获取用户统计信息
   * @returns {Object} 用户统计信息
   */
  getUserStats() {
    try {
      const history = this.getLoginHistory()
      const allKeys = Object.keys(localStorage)
      const userSessionKeys = allKeys.filter(key => key.startsWith('bodorai_sessions_'))

      return {
        totalUsers: new Set(history.map(record => record.userKey)).size,
        totalLogins: history.length,
        activeUsers: userSessionKeys.length,
        currentUser: this.currentUser,
        lastCleanup: localStorage.getItem('bodorai_last_cleanup')
      }
    } catch (error) {
      console.error('获取用户统计信息失败:', error)
      return {
        totalUsers: 0,
        totalLogins: 0,
        activeUsers: 0,
        currentUser: this.currentUser,
        lastCleanup: null
      }
    }
  }
}

// 创建单例实例
let userServiceInstance = null

/**
 * 获取用户服务实例
 * @returns {UserService} 用户服务实例
 */
export function getUserService() {
  if (!userServiceInstance) {
    userServiceInstance = new UserService()
  }
  return userServiceInstance
}

// 默认导出
export default getUserService
