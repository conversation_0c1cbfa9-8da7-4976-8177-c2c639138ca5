/**
 * 会话存储服务
 * 负责会话数据的持久化存储、加载和管理
 * 使用 LocalStorage 作为主要存储方案
 * 支持基于用户的会话隔离
 */

import { getUserService } from './userService.js'

// 存储键名常量
const STORAGE_KEYS = {
  SESSIONS: 'bodorai_sessions',           // 会话数据（全局，兼容旧版本）
  CURRENT_SESSIONS: 'bodorai_current_sessions', // 当前选中的会话（全局，兼容旧版本）
  STORAGE_VERSION: 'bodorai_storage_version',   // 存储版本
  BACKUP_PREFIX: 'bodorai_backup_'       // 备份前缀
}

// 当前存储版本
const CURRENT_VERSION = '2.0.0' // 升级版本以支持用户隔离

/**
 * 会话存储服务类
 */
class SessionStorageService {
  constructor() {
    this.isSupported = this.checkStorageSupport()
    this.userService = null
    this.initializeStorage()
  }

  /**
   * 获取用户服务实例
   */
  getUserService() {
    if (!this.userService) {
      this.userService = getUserService()
    }
    return this.userService
  }

  /**
   * 获取当前用户的存储键
   * @param {string} baseKey - 基础键名
   * @returns {string} 用户专用的存储键
   */
  getUserStorageKey(baseKey) {
    const userService = this.getUserService()
    const currentUser = userService.currentUser

    if (currentUser && currentUser.userKey) {
      return `${baseKey}_${currentUser.userKey}`
    }

    // 如果没有登录用户，使用全局键（兼容旧版本）
    return baseKey
  }

  /**
   * 获取用户会话存储键
   * @returns {string} 用户会话存储键
   */
  getUserSessionsKey() {
    return this.getUserStorageKey(STORAGE_KEYS.SESSIONS)
  }

  /**
   * 获取用户当前会话存储键
   * @returns {string} 用户当前会话存储键
   */
  getUserCurrentSessionsKey() {
    return this.getUserStorageKey(STORAGE_KEYS.CURRENT_SESSIONS)
  }

  /**
   * 从全局存储迁移数据到用户专用存储
   * @returns {Object|null} 迁移后的数据
   */
  migrateFromGlobalStorage() {
    try {
      console.log('检测到用户登录，正在迁移全局会话数据...')

      // 加载全局数据
      const globalSessionDataStr = localStorage.getItem(STORAGE_KEYS.SESSIONS)
      const globalCurrentSessionDataStr = localStorage.getItem(STORAGE_KEYS.CURRENT_SESSIONS)

      if (!globalSessionDataStr) {
        console.log('没有全局会话数据需要迁移')
        return null
      }

      const globalSessionData = JSON.parse(globalSessionDataStr)
      const globalCurrentSessionData = globalCurrentSessionDataStr ?
        JSON.parse(globalCurrentSessionDataStr) : { moduleCurrentSession: {} }

      // 合并数据
      const migratedData = {
        moduleSessions: globalSessionData.moduleSessions || {},
        moduleCurrentSession: globalCurrentSessionData.moduleCurrentSession || {},
        timestamp: new Date().toISOString(),
        version: CURRENT_VERSION
      }

      // 保存到用户专用存储
      this.saveAllSessions(migratedData.moduleSessions, migratedData.moduleCurrentSession)

      console.log('全局会话数据迁移完成')
      return migratedData
    } catch (error) {
      console.error('从全局存储迁移数据失败:', error)
      return null
    }
  }

  /**
   * 检查浏览器是否支持 LocalStorage
   * @returns {boolean} 是否支持
   */
  checkStorageSupport() {
    try {
      const testKey = '__storage_test__'
      localStorage.setItem(testKey, 'test')
      localStorage.removeItem(testKey)
      return true
    } catch (error) {
      console.warn('LocalStorage 不可用:', error)
      return false
    }
  }

  /**
   * 初始化存储
   */
  initializeStorage() {
    if (!this.isSupported) {
      console.warn('存储服务不可用，会话数据将不会持久化')
      return
    }

    // 检查存储版本，进行必要的迁移
    this.migrateStorageIfNeeded()
  }

  /**
   * 存储版本迁移
   */
  migrateStorageIfNeeded() {
    const storedVersion = localStorage.getItem(STORAGE_KEYS.STORAGE_VERSION)
    
    if (!storedVersion) {
      // 首次使用，设置版本号
      localStorage.setItem(STORAGE_KEYS.STORAGE_VERSION, CURRENT_VERSION)
      console.log('初始化存储版本:', CURRENT_VERSION)
    } else if (storedVersion !== CURRENT_VERSION) {
      // 版本不匹配，执行迁移
      this.performMigration(storedVersion, CURRENT_VERSION)
    }
  }

  /**
   * 执行数据迁移
   * @param {string} fromVersion 源版本
   * @param {string} toVersion 目标版本
   */
  performMigration(fromVersion, toVersion) {
    console.log(`执行数据迁移: ${fromVersion} -> ${toVersion}`)

    try {
      // 创建备份
      this.createBackup(`migration_${fromVersion}_to_${toVersion}`)

      // 执行具体的迁移逻辑
      if (this.shouldMigrate(fromVersion, toVersion)) {
        this.executeMigrationSteps(fromVersion, toVersion)
      }

      // 更新版本号
      localStorage.setItem(STORAGE_KEYS.STORAGE_VERSION, toVersion)

      console.log('数据迁移完成')
    } catch (error) {
      console.error('数据迁移失败:', error)
      // 迁移失败时恢复备份
      this.restoreFromBackup(`migration_${fromVersion}_to_${toVersion}`)
    }
  }

  /**
   * 判断是否需要执行迁移
   * @param {string} fromVersion 源版本
   * @param {string} toVersion 目标版本
   * @returns {boolean} 是否需要迁移
   */
  shouldMigrate(fromVersion, toVersion) {
    // 这里可以添加版本比较逻辑
    // 目前简单判断版本不同就需要迁移
    return fromVersion !== toVersion
  }

  /**
   * 执行迁移步骤
   * @param {string} fromVersion 源版本
   * @param {string} toVersion 目标版本
   */
  executeMigrationSteps(fromVersion, toVersion) {
    console.log(`执行迁移步骤: ${fromVersion} -> ${toVersion}`)

    // 加载现有数据
    const existingData = this.loadAllSessions()
    if (!existingData) return

    // 执行数据结构升级
    const migratedData = this.upgradeDataStructure(existingData, fromVersion, toVersion)

    // 保存升级后的数据
    this.saveAllSessions(migratedData.moduleSessions, migratedData.moduleCurrentSession)

    console.log('数据结构升级完成')
  }

  /**
   * 升级数据结构
   * @param {Object} data 原始数据
   * @param {string} fromVersion 源版本
   * @param {string} toVersion 目标版本
   * @returns {Object} 升级后的数据
   */
  upgradeDataStructure(data, fromVersion, toVersion) {
    let upgradedData = { ...data }

    // 确保所有会话都有必需的属性
    if (upgradedData.moduleSessions) {
      Object.keys(upgradedData.moduleSessions).forEach(moduleKey => {
        upgradedData.moduleSessions[moduleKey] = upgradedData.moduleSessions[moduleKey].map(session => ({
          id: session.id || Date.now().toString(),
          title: session.title || '未命名会话',
          module: session.module || moduleKey,
          messages: Array.isArray(session.messages) ? session.messages : [],
          createdAt: session.createdAt ? new Date(session.createdAt) : new Date(),
          updatedAt: session.updatedAt ? new Date(session.updatedAt) : new Date(),
          pinned: Boolean(session.pinned) // 确保 pinned 属性存在
        }))
      })
    }

    // 确保当前会话数据完整性
    if (upgradedData.moduleCurrentSession) {
      Object.keys(upgradedData.moduleCurrentSession).forEach(moduleKey => {
        const currentSession = upgradedData.moduleCurrentSession[moduleKey]
        if (currentSession && !currentSession.id) {
          // 如果当前会话没有ID，尝试从会话列表中找到匹配的会话
          const sessions = upgradedData.moduleSessions[moduleKey] || []
          const matchedSession = sessions.find(s => s.title === currentSession.title)
          if (matchedSession) {
            upgradedData.moduleCurrentSession[moduleKey] = {
              id: matchedSession.id,
              title: matchedSession.title,
              module: matchedSession.module
            }
          } else {
            // 找不到匹配的会话，清除当前会话引用
            upgradedData.moduleCurrentSession[moduleKey] = null
          }
        }
      })
    }

    return upgradedData
  }

  /**
   * 从备份恢复数据
   * @param {string} backupName 备份名称
   */
  restoreFromBackup(backupName) {
    if (!this.isSupported) return false

    try {
      const backupKey = `${STORAGE_KEYS.BACKUP_PREFIX}${backupName}`
      const backupData = localStorage.getItem(backupKey)

      if (!backupData) {
        console.warn('备份数据不存在:', backupKey)
        return false
      }

      const backup = JSON.parse(backupData)

      // 恢复会话数据
      localStorage.setItem(STORAGE_KEYS.SESSIONS, JSON.stringify({
        moduleSessions: backup.moduleSessions,
        timestamp: backup.timestamp,
        version: backup.version
      }))

      // 恢复当前会话数据
      if (backup.moduleCurrentSession) {
        localStorage.setItem(STORAGE_KEYS.CURRENT_SESSIONS, JSON.stringify({
          moduleCurrentSession: backup.moduleCurrentSession,
          timestamp: backup.timestamp,
          version: backup.version
        }))
      }

      console.log('数据已从备份恢复:', backupKey)
      return true
    } catch (error) {
      console.error('从备份恢复数据失败:', error)
      return false
    }
  }

  /**
   * 保存所有会话数据
   * @param {Object} moduleSessions 模块会话数据
   * @param {Object} moduleCurrentSession 当前选中的会话
   */
  saveAllSessions(moduleSessions, moduleCurrentSession) {
    if (!this.isSupported) return false

    try {
      // 准备存储数据
      const sessionData = {
        moduleSessions: this.sanitizeSessionData(moduleSessions),
        timestamp: new Date().toISOString(),
        version: CURRENT_VERSION
      }

      const currentSessionData = {
        moduleCurrentSession: this.sanitizeCurrentSessionData(moduleCurrentSession),
        timestamp: new Date().toISOString(),
        version: CURRENT_VERSION
      }

      // 使用用户专用的存储键保存数据
      const sessionsKey = this.getUserSessionsKey()
      const currentSessionsKey = this.getUserCurrentSessionsKey()

      localStorage.setItem(sessionsKey, JSON.stringify(sessionData))
      localStorage.setItem(currentSessionsKey, JSON.stringify(currentSessionData))

      console.log('会话数据已保存到本地存储')
      return true
    } catch (error) {
      console.error('保存会话数据失败:', error)
      return false
    }
  }

  /**
   * 加载所有会话数据
   * @returns {Object|null} 会话数据对象或null
   */
  loadAllSessions() {
    if (!this.isSupported) return null

    try {
      // 使用用户专用的存储键加载数据
      const sessionsKey = this.getUserSessionsKey()
      const currentSessionsKey = this.getUserCurrentSessionsKey()

      const sessionDataStr = localStorage.getItem(sessionsKey)
      const currentSessionDataStr = localStorage.getItem(currentSessionsKey)

      // 如果用户专用数据不存在，尝试从全局数据迁移（兼容性处理）
      if (!sessionDataStr && sessionsKey !== STORAGE_KEYS.SESSIONS) {
        return this.migrateFromGlobalStorage()
      }

      if (!sessionDataStr) {
        console.log('没有找到存储的会话数据')
        return null
      }

      const sessionData = JSON.parse(sessionDataStr)
      const currentSessionData = currentSessionDataStr ? JSON.parse(currentSessionDataStr) : {}

      // 验证数据完整性
      if (!this.validateSessionData(sessionData)) {
        console.warn('会话数据格式无效')
        return null
      }

      // 恢复数据结构
      const result = {
        moduleSessions: this.restoreSessionData(sessionData.moduleSessions),
        moduleCurrentSession: this.restoreCurrentSessionData(currentSessionData.moduleCurrentSession || {})
      }

      console.log('会话数据已从本地存储加载')
      return result
    } catch (error) {
      console.error('加载会话数据失败:', error)
      return null
    }
  }

  /**
   * 清理会话数据（移除不可序列化的属性）
   * @param {Object} moduleSessions 原始会话数据
   * @returns {Object} 清理后的数据
   */
  sanitizeSessionData(moduleSessions) {
    const sanitized = {}
    
    for (const [moduleKey, sessions] of Object.entries(moduleSessions)) {
      sanitized[moduleKey] = sessions.map(session => ({
        id: session.id,
        title: session.title,
        module: session.module,
        messages: session.messages || [],
        createdAt: session.createdAt instanceof Date ? session.createdAt.toISOString() : session.createdAt,
        updatedAt: session.updatedAt instanceof Date ? session.updatedAt.toISOString() : session.updatedAt,
        pinned: Boolean(session.pinned)
      }))
    }
    
    return sanitized
  }

  /**
   * 清理当前会话数据
   * @param {Object} moduleCurrentSession 当前会话数据
   * @returns {Object} 清理后的数据
   */
  sanitizeCurrentSessionData(moduleCurrentSession) {
    const sanitized = {}
    
    for (const [moduleKey, session] of Object.entries(moduleCurrentSession)) {
      if (session) {
        sanitized[moduleKey] = {
          id: session.id,
          title: session.title,
          module: session.module
        }
      }
    }
    
    return sanitized
  }

  /**
   * 恢复会话数据（将字符串日期转换为Date对象）
   * @param {Object} sessionData 存储的会话数据
   * @returns {Object} 恢复后的数据
   */
  restoreSessionData(sessionData) {
    const restored = {}
    
    for (const [moduleKey, sessions] of Object.entries(sessionData)) {
      restored[moduleKey] = sessions.map(session => ({
        ...session,
        createdAt: new Date(session.createdAt),
        updatedAt: new Date(session.updatedAt),
        pinned: Boolean(session.pinned),
        messages: session.messages || []
      }))
    }
    
    return restored
  }

  /**
   * 恢复当前会话数据
   * @param {Object} currentSessionData 存储的当前会话数据
   * @returns {Object} 恢复后的数据
   */
  restoreCurrentSessionData(currentSessionData) {
    return currentSessionData || {}
  }

  /**
   * 验证会话数据格式
   * @param {Object} sessionData 会话数据
   * @returns {boolean} 是否有效
   */
  validateSessionData(sessionData) {
    if (!sessionData || typeof sessionData !== 'object') return false
    if (!sessionData.moduleSessions || typeof sessionData.moduleSessions !== 'object') return false
    
    // 验证每个模块的会话数据
    for (const [moduleKey, sessions] of Object.entries(sessionData.moduleSessions)) {
      if (!Array.isArray(sessions)) return false
      
      for (const session of sessions) {
        if (!session.id || !session.title || !session.module) return false
      }
    }
    
    return true
  }

  /**
   * 删除指定模块的会话
   * @param {string} moduleKey 模块键名
   * @param {string} sessionId 会话ID
   */
  deleteSession(moduleKey, sessionId) {
    if (!this.isSupported) return false

    try {
      const data = this.loadAllSessions()
      if (!data) return false

      // 从会话列表中删除
      if (data.moduleSessions[moduleKey]) {
        data.moduleSessions[moduleKey] = data.moduleSessions[moduleKey].filter(s => s.id !== sessionId)
      }

      // 如果删除的是当前会话，清除当前会话引用
      if (data.moduleCurrentSession[moduleKey]?.id === sessionId) {
        delete data.moduleCurrentSession[moduleKey]
      }

      // 保存更新后的数据
      this.saveAllSessions(data.moduleSessions, data.moduleCurrentSession)
      return true
    } catch (error) {
      console.error('删除会话失败:', error)
      return false
    }
  }

  /**
   * 清除所有存储数据
   */
  clearAllData() {
    if (!this.isSupported) return false

    try {
      localStorage.removeItem(STORAGE_KEYS.SESSIONS)
      localStorage.removeItem(STORAGE_KEYS.CURRENT_SESSIONS)
      console.log('所有会话数据已清除')
      return true
    } catch (error) {
      console.error('清除数据失败:', error)
      return false
    }
  }

  /**
   * 创建数据备份
   * @param {string} backupName 备份名称
   */
  createBackup(backupName = null) {
    if (!this.isSupported) return false

    try {
      const data = this.loadAllSessions()
      if (!data) return false

      const backupKey = `${STORAGE_KEYS.BACKUP_PREFIX}${backupName || new Date().toISOString()}`
      localStorage.setItem(backupKey, JSON.stringify({
        ...data,
        backupTime: new Date().toISOString()
      }))

      console.log('数据备份已创建:', backupKey)
      return backupKey
    } catch (error) {
      console.error('创建备份失败:', error)
      return false
    }
  }

  /**
   * 检查数据完整性
   * @returns {Object} 检查结果
   */
  checkDataIntegrity() {
    if (!this.isSupported) {
      return { valid: false, reason: 'Storage not supported' }
    }

    try {
      const data = this.loadAllSessions()
      if (!data) {
        return { valid: true, reason: 'No data to check' }
      }

      // 检查会话数据完整性
      const issues = []

      Object.entries(data.moduleSessions).forEach(([moduleKey, sessions]) => {
        sessions.forEach((session, index) => {
          if (!session.id) {
            issues.push(`${moduleKey}[${index}]: 缺少会话ID`)
          }
          if (!session.title) {
            issues.push(`${moduleKey}[${index}]: 缺少会话标题`)
          }
          if (!session.module) {
            issues.push(`${moduleKey}[${index}]: 缺少模块信息`)
          }
          if (!Array.isArray(session.messages)) {
            issues.push(`${moduleKey}[${index}]: 消息列表格式错误`)
          }
          if (session.pinned === undefined) {
            issues.push(`${moduleKey}[${index}]: 缺少置顶状态`)
          }
        })
      })

      // 检查当前会话引用完整性
      Object.entries(data.moduleCurrentSession).forEach(([moduleKey, currentSession]) => {
        if (currentSession) {
          const sessions = data.moduleSessions[moduleKey] || []
          const sessionExists = sessions.some(s => s.id === currentSession.id)
          if (!sessionExists) {
            issues.push(`${moduleKey}: 当前会话引用无效`)
          }
        }
      })

      return {
        valid: issues.length === 0,
        issues: issues,
        sessionCount: Object.values(data.moduleSessions).reduce((total, sessions) => total + sessions.length, 0)
      }
    } catch (error) {
      return { valid: false, reason: error.message }
    }
  }

  /**
   * 修复数据完整性问题
   * @returns {boolean} 是否修复成功
   */
  repairDataIntegrity() {
    if (!this.isSupported) return false

    try {
      const data = this.loadAllSessions()
      if (!data) return true

      console.log('开始修复数据完整性问题...')

      // 创建修复前的备份
      this.createBackup('before_repair')

      let repaired = false

      // 修复会话数据
      Object.entries(data.moduleSessions).forEach(([moduleKey, sessions]) => {
        data.moduleSessions[moduleKey] = sessions.map((session, index) => {
          const repairedSession = { ...session }

          if (!repairedSession.id) {
            repairedSession.id = `${moduleKey}_${Date.now()}_${index}`
            repaired = true
          }
          if (!repairedSession.title) {
            repairedSession.title = `${moduleKey}会话 ${index + 1}`
            repaired = true
          }
          if (!repairedSession.module) {
            repairedSession.module = moduleKey
            repaired = true
          }
          if (!Array.isArray(repairedSession.messages)) {
            repairedSession.messages = []
            repaired = true
          }
          if (repairedSession.pinned === undefined) {
            repairedSession.pinned = false
            repaired = true
          }
          if (!repairedSession.createdAt) {
            repairedSession.createdAt = new Date()
            repaired = true
          }
          if (!repairedSession.updatedAt) {
            repairedSession.updatedAt = new Date()
            repaired = true
          }

          return repairedSession
        })
      })

      // 修复当前会话引用
      Object.entries(data.moduleCurrentSession).forEach(([moduleKey, currentSession]) => {
        if (currentSession) {
          const sessions = data.moduleSessions[moduleKey] || []
          const sessionExists = sessions.some(s => s.id === currentSession.id)
          if (!sessionExists) {
            // 当前会话引用无效，设置为第一个会话或null
            data.moduleCurrentSession[moduleKey] = sessions.length > 0 ? {
              id: sessions[0].id,
              title: sessions[0].title,
              module: sessions[0].module
            } : null
            repaired = true
          }
        }
      })

      if (repaired) {
        // 保存修复后的数据
        this.saveAllSessions(data.moduleSessions, data.moduleCurrentSession)
        console.log('数据完整性修复完成')
      } else {
        console.log('数据完整性检查通过，无需修复')
      }

      return true
    } catch (error) {
      console.error('修复数据完整性失败:', error)
      return false
    }
  }

  /**
   * 获取存储使用情况
   * @returns {Object} 存储使用信息
   */
  getStorageInfo() {
    if (!this.isSupported) {
      return { supported: false }
    }

    try {
      const sessionData = localStorage.getItem(STORAGE_KEYS.SESSIONS)
      const currentSessionData = localStorage.getItem(STORAGE_KEYS.CURRENT_SESSIONS)
      const version = localStorage.getItem(STORAGE_KEYS.STORAGE_VERSION)

      // 获取所有备份
      const backups = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && key.startsWith(STORAGE_KEYS.BACKUP_PREFIX)) {
          backups.push({
            key: key,
            name: key.replace(STORAGE_KEYS.BACKUP_PREFIX, ''),
            size: localStorage.getItem(key)?.length || 0
          })
        }
      }

      return {
        supported: true,
        version: version,
        sessionDataSize: sessionData ? sessionData.length : 0,
        currentSessionDataSize: currentSessionData ? currentSessionData.length : 0,
        totalSize: (sessionData?.length || 0) + (currentSessionData?.length || 0),
        lastSaved: sessionData ? JSON.parse(sessionData).timestamp : null,
        backups: backups,
        backupCount: backups.length,
        totalBackupSize: backups.reduce((total, backup) => total + backup.size, 0)
      }
    } catch (error) {
      console.error('获取存储信息失败:', error)
      return { supported: true, error: error.message }
    }
  }
}

// 创建单例实例
const sessionStorageService = new SessionStorageService()

export default sessionStorageService
