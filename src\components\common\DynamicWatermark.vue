<template>
  <!-- 动态水印组件 -->
  <div 
    class="watermark-container" 
    :class="{ 'watermark-enabled': enabled }"
    ref="watermarkContainer"
  >
    <!-- 插槽内容 -->
    <slot></slot>
    
    <!-- 水印层 -->
    <div 
      v-if="enabled && watermarkText"
      class="watermark-layer"
      :style="watermarkStyle"
      ref="watermarkLayer"
    ></div>
    
    <!-- 水印配置面板 -->
    <div 
      v-if="showConfig" 
      class="watermark-config"
      @click.stop
    >
      <div class="config-header">
        <h3 class="config-title">水印配置</h3>
        <button class="close-config" @click="closeConfig">
          <CloseIcon />
        </button>
      </div>
      
      <div class="config-content">
        <!-- 基础设置 -->
        <div class="config-section">
          <h4 class="section-title">基础设置</h4>
          
          <div class="config-item">
            <label class="config-label">启用水印</label>
            <input 
              type="checkbox" 
              v-model="localConfig.enabled"
              @change="updateWatermark"
              class="config-checkbox"
            />
          </div>
          
          <div class="config-item">
            <label class="config-label">水印文本</label>
            <input 
              type="text" 
              v-model="localConfig.text"
              @input="updateWatermark"
              placeholder="请输入水印文本"
              class="config-input"
            />
          </div>
          
          <div class="config-item">
            <label class="config-label">字体大小</label>
            <input 
              type="range" 
              v-model="localConfig.fontSize"
              @input="updateWatermark"
              min="12" 
              max="48" 
              class="config-range"
            />
            <span class="range-value">{{ localConfig.fontSize }}px</span>
          </div>
        </div>
        
        <!-- 外观设置 -->
        <div class="config-section">
          <h4 class="section-title">外观设置</h4>
          
          <div class="config-item">
            <label class="config-label">透明度</label>
            <input 
              type="range" 
              v-model="localConfig.opacity"
              @input="updateWatermark"
              min="0.1" 
              max="1" 
              step="0.1"
              class="config-range"
            />
            <span class="range-value">{{ Math.round(localConfig.opacity * 100) }}%</span>
          </div>
          
          <div class="config-item">
            <label class="config-label">颜色</label>
            <input 
              type="color" 
              v-model="localConfig.color"
              @change="updateWatermark"
              class="config-color"
            />
          </div>
          
          <div class="config-item">
            <label class="config-label">旋转角度</label>
            <input 
              type="range" 
              v-model="localConfig.rotate"
              @input="updateWatermark"
              min="-45" 
              max="45"
              class="config-range"
            />
            <span class="range-value">{{ localConfig.rotate }}°</span>
          </div>
        </div>
        
        <!-- 布局设置 -->
        <div class="config-section">
          <h4 class="section-title">布局设置</h4>
          
          <div class="config-item">
            <label class="config-label">水平间距</label>
            <input 
              type="range" 
              v-model="localConfig.gapX"
              @input="updateWatermark"
              min="50" 
              max="300"
              class="config-range"
            />
            <span class="range-value">{{ localConfig.gapX }}px</span>
          </div>
          
          <div class="config-item">
            <label class="config-label">垂直间距</label>
            <input 
              type="range" 
              v-model="localConfig.gapY"
              @input="updateWatermark"
              min="50" 
              max="300"
              class="config-range"
            />
            <span class="range-value">{{ localConfig.gapY }}px</span>
          </div>
          
          <div class="config-item">
            <label class="config-label">水印模式</label>
            <select 
              v-model="localConfig.mode"
              @change="updateWatermark"
              class="config-select"
            >
              <option value="repeat">平铺</option>
              <option value="center">居中</option>
              <option value="corner">角落</option>
            </select>
          </div>
        </div>
        
        <!-- 动态效果 -->
        <div class="config-section">
          <h4 class="section-title">动态效果</h4>
          
          <div class="config-item">
            <label class="config-label">启用动画</label>
            <input 
              type="checkbox" 
              v-model="localConfig.animated"
              @change="updateWatermark"
              class="config-checkbox"
            />
          </div>
          
          <div class="config-item" v-if="localConfig.animated">
            <label class="config-label">动画速度</label>
            <select 
              v-model="localConfig.animationSpeed"
              @change="updateWatermark"
              class="config-select"
            >
              <option value="slow">慢速</option>
              <option value="normal">正常</option>
              <option value="fast">快速</option>
            </select>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="config-actions">
          <button class="action-btn reset-btn" @click="resetConfig">
            重置
          </button>
          <button class="action-btn save-btn" @click="saveConfig">
            保存配置
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, defineProps, defineEmits } from 'vue'

// 图标组件
const CloseIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
    </svg>
  `
}

// 组件属性
const props = defineProps({
  // 水印配置
  config: {
    type: Object,
    default: () => ({})
  },
  // 是否显示配置面板
  showConfig: {
    type: Boolean,
    default: false
  }
})

// 组件事件
const emit = defineEmits(['update:config', 'update:showConfig'])

// 响应式数据
const watermarkContainer = ref(null)
const watermarkLayer = ref(null)

// 默认配置
const defaultConfig = {
  enabled: true,
  text: '机密文档',
  fontSize: 16,
  opacity: 0.1,
  color: '#000000',
  rotate: -15,
  gapX: 150,
  gapY: 100,
  mode: 'repeat', // repeat, center, corner
  animated: false,
  animationSpeed: 'normal' // slow, normal, fast
}

// 本地配置（用于实时预览）
const localConfig = ref({ ...defaultConfig, ...props.config })

// 计算属性
const enabled = computed(() => localConfig.value.enabled)
const watermarkText = computed(() => localConfig.value.text)

// 水印样式
const watermarkStyle = computed(() => {
  const config = localConfig.value
  
  // 动画持续时间映射
  const animationDurations = {
    slow: '30s',
    normal: '20s',
    fast: '10s'
  }
  
  const baseStyle = {
    fontSize: `${config.fontSize}px`,
    opacity: config.opacity,
    color: config.color,
    transform: `rotate(${config.rotate}deg)`,
    '--gap-x': `${config.gapX}px`,
    '--gap-y': `${config.gapY}px`,
    '--watermark-text': `"${config.text}"`
  }
  
  // 添加动画
  if (config.animated) {
    baseStyle.animation = `watermarkFloat ${animationDurations[config.animationSpeed]} ease-in-out infinite`
  }
  
  return baseStyle
})

/**
 * 更新水印
 */
const updateWatermark = () => {
  generateWatermark()
  emit('update:config', { ...localConfig.value })
}

/**
 * 生成水印
 */
const generateWatermark = () => {
  if (!watermarkLayer.value || !localConfig.value.enabled) return
  
  const config = localConfig.value
  const container = watermarkContainer.value
  
  if (!container) return
  
  // 清除现有水印
  watermarkLayer.value.innerHTML = ''
  
  // 根据模式生成水印
  switch (config.mode) {
    case 'repeat':
      generateRepeatWatermark()
      break
    case 'center':
      generateCenterWatermark()
      break
    case 'corner':
      generateCornerWatermark()
      break
  }
}

/**
 * 生成平铺水印
 */
const generateRepeatWatermark = () => {
  const config = localConfig.value
  const container = watermarkContainer.value
  const layer = watermarkLayer.value
  
  const containerRect = container.getBoundingClientRect()
  const cols = Math.ceil(containerRect.width / config.gapX) + 1
  const rows = Math.ceil(containerRect.height / config.gapY) + 1
  
  for (let i = 0; i < rows; i++) {
    for (let j = 0; j < cols; j++) {
      const watermarkItem = document.createElement('div')
      watermarkItem.className = 'watermark-item'
      watermarkItem.textContent = config.text
      watermarkItem.style.cssText = `
        position: absolute;
        left: ${j * config.gapX}px;
        top: ${i * config.gapY}px;
        font-size: ${config.fontSize}px;
        color: ${config.color};
        opacity: ${config.opacity};
        transform: rotate(${config.rotate}deg);
        pointer-events: none;
        user-select: none;
        white-space: nowrap;
        font-weight: 500;
      `
      layer.appendChild(watermarkItem)
    }
  }
}

/**
 * 生成居中水印
 */
const generateCenterWatermark = () => {
  const config = localConfig.value
  const layer = watermarkLayer.value
  
  const watermarkItem = document.createElement('div')
  watermarkItem.className = 'watermark-item watermark-center'
  watermarkItem.textContent = config.text
  watermarkItem.style.cssText = `
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(${config.rotate}deg);
    font-size: ${config.fontSize * 2}px;
    color: ${config.color};
    opacity: ${config.opacity};
    pointer-events: none;
    user-select: none;
    white-space: nowrap;
    font-weight: 600;
  `
  layer.appendChild(watermarkItem)
}

/**
 * 生成角落水印
 */
const generateCornerWatermark = () => {
  const config = localConfig.value
  const layer = watermarkLayer.value
  
  const positions = [
    { top: '20px', left: '20px' },
    { top: '20px', right: '20px' },
    { bottom: '20px', left: '20px' },
    { bottom: '20px', right: '20px' }
  ]
  
  positions.forEach(pos => {
    const watermarkItem = document.createElement('div')
    watermarkItem.className = 'watermark-item watermark-corner'
    watermarkItem.textContent = config.text
    
    let positionStyle = 'position: absolute;'
    Object.entries(pos).forEach(([key, value]) => {
      positionStyle += `${key}: ${value};`
    })
    
    watermarkItem.style.cssText = `
      ${positionStyle}
      font-size: ${config.fontSize}px;
      color: ${config.color};
      opacity: ${config.opacity};
      transform: rotate(${config.rotate}deg);
      pointer-events: none;
      user-select: none;
      white-space: nowrap;
      font-weight: 500;
    `
    layer.appendChild(watermarkItem)
  })
}

/**
 * 重置配置
 */
const resetConfig = () => {
  localConfig.value = { ...defaultConfig }
  updateWatermark()
}

/**
 * 保存配置
 */
const saveConfig = () => {
  localStorage.setItem('watermark-config', JSON.stringify(localConfig.value))
  console.log('水印配置已保存')
}

/**
 * 加载配置
 */
const loadConfig = () => {
  const saved = localStorage.getItem('watermark-config')
  if (saved) {
    try {
      const savedConfig = JSON.parse(saved)
      localConfig.value = { ...defaultConfig, ...savedConfig, ...props.config }
    } catch (error) {
      console.error('加载水印配置失败:', error)
    }
  }
}

/**
 * 关闭配置面板
 */
const closeConfig = () => {
  emit('update:showConfig', false)
}

// 监听容器大小变化
let resizeObserver = null

onMounted(() => {
  loadConfig()
  generateWatermark()
  
  // 监听容器大小变化
  if (watermarkContainer.value && window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      generateWatermark()
    })
    resizeObserver.observe(watermarkContainer.value)
  }
})

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
})

// 监听配置变化
watch(() => props.config, (newConfig) => {
  localConfig.value = { ...localConfig.value, ...newConfig }
  generateWatermark()
}, { deep: true })

// 监听启用状态变化
watch(enabled, () => {
  generateWatermark()
})
</script>

<style scoped>
/* 水印容器 */
.watermark-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 水印层 */
.watermark-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
}

/* 水印动画 */
@keyframes watermarkFloat {
  0%, 100% {
    transform: translateY(0px) rotate(var(--rotate, -15deg));
  }
  50% {
    transform: translateY(-10px) rotate(var(--rotate, -15deg));
  }
}

/* 水印配置面板 */
.watermark-config {
  position: fixed;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  width: 320px;
  max-height: 80vh;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.config-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.config-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.close-config {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.close-config:hover {
  background: rgba(255, 255, 255, 0.3);
}

.close-config svg {
  width: 16px;
  height: 16px;
}

.config-content {
  max-height: calc(80vh - 60px);
  overflow-y: auto;
  padding: 20px;
}

.config-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
  padding-bottom: 6px;
  border-bottom: 1px solid #f3f4f6;
}

.config-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.config-label {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
  min-width: 80px;
}

.config-input,
.config-select {
  flex: 1;
  margin-left: 12px;
  padding: 6px 10px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 13px;
}

.config-range {
  flex: 1;
  margin-left: 12px;
  margin-right: 8px;
}

.range-value {
  font-size: 12px;
  color: #6b7280;
  min-width: 40px;
  text-align: right;
}

.config-checkbox {
  margin-left: 12px;
}

.config-color {
  margin-left: 12px;
  width: 40px;
  height: 30px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
}

.config-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f3f4f6;
}

.action-btn {
  flex: 1;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reset-btn {
  background: #f3f4f6;
  color: #6b7280;
}

.reset-btn:hover {
  background: #e5e7eb;
}

.save-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.save-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .watermark-config {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    max-height: 100vh;
    transform: none;
    border-radius: 0;
  }
  
  .config-content {
    max-height: calc(100vh - 60px);
  }
}
</style>
