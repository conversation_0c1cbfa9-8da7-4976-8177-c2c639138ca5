# SSO单点登录集成指南

## 概述

本项目已集成单点登录(SSO)功能，支持通过URL参数`tokenId`进行自动登录验证。

## 功能特性

- 🔐 支持SSO token验证
- 🚀 自动登录流程
- 🛡️ CORS代理解决方案
- 📱 响应式UI支持
- 🔄 URL参数自动清理

## 架构设计

```
前端应用 → Vite代理 → SSO代理服务器 → OA系统SSO接口
```

### 组件说明

1. **前端组件** (`UserLogin.vue`)
   - 检测URL中的`tokenId`参数
   - 调用SSO验证接口
   - 处理登录成功/失败状态

2. **用户服务** (`userService.js`)
   - 提供`ssoLogin(tokenId)`方法
   - 处理SSO验证逻辑
   - 管理用户登录状态

3. **SSO代理服务器** (`sso-proxy-server.js`)
   - 解决CORS跨域问题
   - 代理SSO验证请求
   - 处理SSL证书验证

## 使用方法

### 1. 启动服务

```bash
# 安装依赖
npm install

# 启动完整服务（包含SSO代理）
npm run dev:full

# 或者分别启动
npm run sso-proxy  # 启动SSO代理服务器（端口4003）
npm run dev        # 启动前端开发服务器（端口3000）
```

### 2. SSO登录URL格式

```
http://localhost:3000?tokenId=YOUR_SSO_TOKEN
```

### 3. 验证流程

1. 用户访问带有`tokenId`参数的URL
2. 前端自动检测`tokenId`参数
3. 调用SSO验证接口验证token
4. 验证成功后自动登录用户
5. 清除URL中的`tokenId`参数

## API接口

### SSO验证接口

**请求**
```http
POST /api/sso/validate
Content-Type: application/json

{
  "tokenId": "your-sso-token",
  "access_key": "2e5295e9-f46d-45e0-8de0-8a1281a6da5c"
}
```

**响应**
```json
{
  "success": true,
  "data": {
    "data": {
      "uid": "employee_id",
      "username": "user_name",
      "validate": true
    }
  }
}
```

## 配置说明

### SSO服务配置

在`sso-proxy-server.js`中配置：

```javascript
// SSO验证配置（对应Java代码）
const ssoConfig = {
  url: 'https://oa.bodor.com:8443/portal/r/jd?cmd=com.actionsoft.apps.addons.sso_validate'
}

// 默认access_key（对应Java代码中的access_key）
const defaultAccessKey = '2e5295e9-f46d-45e0-8de0-8a1281a6da5c'

// 表单参数（对应Java代码的.form()方法）
formData.append('tokenId', tokenId)
formData.append('access_key', accessKey)
```

### 代理配置

在`vite.config.js`中配置：

```javascript
proxy: {
  '/api/sso': {
    target: 'http://localhost:4003',
    changeOrigin: true,
    secure: false
  }
}
```

## 错误处理

### 常见错误及解决方案

1. **CORS错误**
   - 确保SSO代理服务器正在运行
   - 检查代理配置是否正确

2. **SSL证书错误**
   - 代理服务器已配置忽略SSL验证
   - 生产环境需要正确的SSL配置

3. **Token验证失败**
   - 检查tokenId是否有效
   - 确认access_key配置正确

## 部署注意事项

### 开发环境
- 使用`npm run dev:full`启动完整服务
- SSO代理服务器运行在端口4003

### 生产环境
- 需要部署SSO代理服务器
- 配置正确的SSL证书
- 更新代理目标地址

## 安全考虑

1. **Token安全**
   - tokenId仅用于一次性验证
   - 验证后立即清除URL参数

2. **HTTPS支持**
   - 生产环境必须使用HTTPS
   - 保护token传输安全

3. **访问控制**
   - 限制代理服务器访问来源
   - 配置适当的CORS策略

## 测试

### 手动测试

1. 启动服务：`npm run dev:full`
2. 访问：`http://localhost:3000?tokenId=test-token`
3. 观察控制台日志和登录状态

### 健康检查

```bash
# 检查SSO代理服务器状态
curl http://localhost:4003/health
```

## 故障排除

### 日志查看

- 前端日志：浏览器开发者工具控制台
- 代理服务器日志：终端输出
- SSO验证日志：代理服务器控制台

### 常见问题

1. **代理服务器无法启动**
   - 检查端口4002是否被占用
   - 确认依赖包已正确安装

2. **SSO验证超时**
   - 检查网络连接
   - 确认OA系统可访问性

3. **用户信息获取失败**
   - 检查SSO响应数据格式
   - 确认用户映射逻辑
