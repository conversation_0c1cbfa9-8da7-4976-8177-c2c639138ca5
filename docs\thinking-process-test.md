# AI思维过程展示功能测试指南

## 功能概述

现在AI系统已经支持显示真实的思维过程内容。系统会自动解析AI回复中的思维链标记，并在推理过程区域显示AI的实际思考过程。

## 支持的思维链格式

系统支持以下几种思维链格式：

### 1. XML标记格式
```
<thinking>
这里是AI的思考过程...
分析问题的各个方面
考虑不同的解决方案
</thinking>

最终的回答内容...
```

### 2. 中文标记格式
```
【思考】
这里是AI的思考过程...
【/思考】

最终的回答内容...
```

### 3. 方括号格式
```
[思考]
这里是AI的思考过程...
[/思考]

最终的回答内容...
```

### 4. 文本指示格式
```
思考过程：
这里是详细的思考过程...

最终回答：
这里是最终的答案...
```

## 测试步骤

### 1. 启动应用
- 确保开发服务器正在运行：`npm run dev`
- 打开浏览器访问：http://localhost:3000

### 2. 测试推理过程显示

#### 测试用例1：知识中心模块
1. 进入知识中心页面
2. 输入问题："请解释什么是人工智能？"
3. 观察AI回复中是否显示"推理过程"按钮
4. 点击展开推理过程，查看AI的思考内容

#### 测试用例2：业务域模块
1. 进入业务域页面
2. 输入问题："如何提高团队工作效率？"
3. 观察推理过程的显示

#### 测试用例3：职能域模块
1. 进入职能域页面
2. 输入问题："如何配置Vue项目的开发环境？"
3. 观察推理过程的显示

### 3. 验证功能特性

#### 特性1：自动解析思维链
- AI回复包含`<thinking>`标记时，系统会自动提取思维内容
- 思维内容会在推理过程区域单独显示
- 最终回答会移除思维标记，只显示核心内容

#### 特性2：Markdown渲染
- 思维过程内容支持Markdown格式
- 包括代码块、列表、引用等格式

#### 特性3：可折叠展示
- 推理过程默认折叠
- 点击"推理过程"按钮可展开/折叠
- 显示思维过程的图标和标题

#### 特性4：兼容性
- 如果AI回复没有思维链标记，会显示模拟的推理步骤
- 支持新旧格式的兼容

## 配置选项

### 环境变量配置
在`.env`文件中可以配置：

```bash
# 启用推理过程展示
VITE_AI_ENABLE_REASONING=true

# 自动展开推理过程
VITE_AI_REASONING_AUTO_EXPAND=true

# 启用模拟模式
VITE_AI_ENABLE_MOCK=true
```

### 系统提示词
系统已经配置了专门的提示词，要求AI在回答时包含思维过程：

```
请按照以下格式回答：

<thinking>
在这里写出你的详细思考过程，包括：
1. 对问题的理解和分析
2. 相关知识的回忆和整理
3. 解决方案的构思过程
4. 答案的组织和优化
</thinking>

然后给出你的最终回答...
```

## 调试信息

### 控制台日志
在浏览器控制台中可以看到以下调试信息：
- `提取到推理过程:` - 显示解析出的思维内容
- `推理过程检查:` - 显示推理过程的显示条件
- `计算推理步骤:` - 显示推理步骤的计算过程

### 临时调试显示
可以在AIMessage组件中临时启用调试信息显示：
```javascript
// 将 v-if="false" 改为 v-if="true"
<div v-if="true" style="font-size: 12px; color: #999; margin-bottom: 8px;">
```

## 预期效果

### 成功的表现
1. AI回复后，消息下方显示"推理过程"按钮
2. 点击按钮展开，显示AI的实际思考过程
3. 思维内容格式良好，支持Markdown渲染
4. 最终回答内容干净，不包含思维标记

### 故障排除
如果推理过程不显示：
1. 检查环境变量配置
2. 查看浏览器控制台的调试信息
3. 确认AI回复是否包含思维链标记
4. 检查系统提示词是否正确配置

## 后续优化

1. 支持更多思维链格式
2. 优化思维过程的视觉展示
3. 添加思维过程的搜索和高亮功能
4. 支持思维过程的导出和分享
