/**
 * 响应式设备检测工具
 * 用于检测设备类型和屏幕尺寸变化
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'

// 断点配置 - 更细致的断点设置
const BREAKPOINTS = {
  xs: 320,      // 超小屏幕（小手机）
  sm: 480,      // 小屏幕（大手机）
  md: 768,      // 中等屏幕（平板竖屏）
  lg: 1024,     // 大屏幕（平板横屏/小桌面）
  xl: 1200,     // 超大屏幕（桌面）
  '2xl': 1440,  // 超超大屏幕（大桌面）

  // 兼容旧版本
  mobile: 768,
  tablet: 1024,
  desktop: 1200
}

/**
 * 响应式设备检测 composable
 * @returns {Object} 响应式设备信息和工具函数
 */
export function useResponsive() {
  // 当前窗口宽度
  const windowWidth = ref(window.innerWidth)
  
  // 当前窗口高度
  const windowHeight = ref(window.innerHeight)

  // 设备类型计算属性 - 更精确的分类
  const deviceType = computed(() => {
    const width = windowWidth.value
    if (width < BREAKPOINTS.xs) {
      return 'xs'
    } else if (width < BREAKPOINTS.sm) {
      return 'sm'
    } else if (width < BREAKPOINTS.md) {
      return 'md'
    } else if (width < BREAKPOINTS.lg) {
      return 'lg'
    } else if (width < BREAKPOINTS.xl) {
      return 'xl'
    } else {
      return '2xl'
    }
  })

  // 兼容旧版本的设备类型判断
  const legacyDeviceType = computed(() => {
    const width = windowWidth.value
    if (width < BREAKPOINTS.mobile) {
      return 'mobile'
    } else if (width < BREAKPOINTS.tablet) {
      return 'tablet'
    } else {
      return 'desktop'
    }
  })

  // 各种设备类型的判断 - 新版本
  const isXs = computed(() => windowWidth.value < BREAKPOINTS.xs)
  const isSm = computed(() => windowWidth.value >= BREAKPOINTS.xs && windowWidth.value < BREAKPOINTS.sm)
  const isMd = computed(() => windowWidth.value >= BREAKPOINTS.sm && windowWidth.value < BREAKPOINTS.md)
  const isLg = computed(() => windowWidth.value >= BREAKPOINTS.md && windowWidth.value < BREAKPOINTS.lg)
  const isXl = computed(() => windowWidth.value >= BREAKPOINTS.lg && windowWidth.value < BREAKPOINTS.xl)
  const is2Xl = computed(() => windowWidth.value >= BREAKPOINTS.xl)

  // 兼容旧版本的设备类型判断
  const isMobile = computed(() => legacyDeviceType.value === 'mobile')
  const isTablet = computed(() => legacyDeviceType.value === 'tablet')
  const isDesktop = computed(() => legacyDeviceType.value === 'desktop')

  // 是否为小屏设备
  const isSmallScreen = computed(() => windowWidth.value < BREAKPOINTS.md)

  // 是否为大屏设备
  const isLargeScreen = computed(() => windowWidth.value >= BREAKPOINTS.lg)

  // 是否为超大屏设备
  const isExtraLargeScreen = computed(() => windowWidth.value >= BREAKPOINTS.xl)

  // 屏幕方向
  const orientation = computed(() => {
    return windowWidth.value > windowHeight.value ? 'landscape' : 'portrait'
  })

  // 是否为横屏
  const isLandscape = computed(() => orientation.value === 'landscape')
  
  // 是否为竖屏
  const isPortrait = computed(() => orientation.value === 'portrait')

  // 窗口尺寸变化处理函数
  const handleResize = () => {
    windowWidth.value = window.innerWidth
    windowHeight.value = window.innerHeight
  }

  // 防抖处理的resize事件
  let resizeTimer = null
  const debouncedHandleResize = () => {
    if (resizeTimer) {
      clearTimeout(resizeTimer)
    }
    resizeTimer = setTimeout(handleResize, 100)
  }

  // 生命周期钩子
  onMounted(() => {
    window.addEventListener('resize', debouncedHandleResize)
    // 初始化时立即获取一次尺寸
    handleResize()
  })

  onUnmounted(() => {
    window.removeEventListener('resize', debouncedHandleResize)
    if (resizeTimer) {
      clearTimeout(resizeTimer)
    }
  })

  // 工具函数：检查是否匹配指定断点
  const matchesBreakpoint = (breakpoint) => {
    const width = windowWidth.value
    switch (breakpoint) {
      case 'mobile':
        return width < BREAKPOINTS.mobile
      case 'tablet':
        return width >= BREAKPOINTS.mobile && width < BREAKPOINTS.tablet
      case 'desktop':
        return width >= BREAKPOINTS.tablet
      default:
        return false
    }
  }

  // 工具函数：检查是否大于等于指定断点
  const isAboveBreakpoint = (breakpoint) => {
    const width = windowWidth.value
    return width >= BREAKPOINTS[breakpoint]
  }

  // 工具函数：检查是否小于指定断点
  const isBelowBreakpoint = (breakpoint) => {
    const width = windowWidth.value
    return width < BREAKPOINTS[breakpoint]
  }

  return {
    // 响应式数据
    windowWidth,
    windowHeight,
    deviceType,
    legacyDeviceType,
    orientation,

    // 新版本设备类型判断
    isXs,
    isSm,
    isMd,
    isLg,
    isXl,
    is2Xl,

    // 兼容旧版本设备类型判断
    isMobile,
    isTablet,
    isDesktop,
    isSmallScreen,
    isLargeScreen,
    isExtraLargeScreen,

    // 屏幕方向判断
    isLandscape,
    isPortrait,

    // 工具函数
    matchesBreakpoint,
    isAboveBreakpoint,
    isBelowBreakpoint,

    // 断点常量
    BREAKPOINTS
  }
}

// 默认导出
export default useResponsive
