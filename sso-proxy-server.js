/**
 * SSO代理服务器
 * 用于解决前端直接访问SSO验证接口的CORS问题
 */

import express from 'express'
import cors from 'cors'
import axios from 'axios'
import https from 'https'

const app = express()
const PORT = 4003

// 配置CORS
app.use(cors({
  origin: ['http://localhost:3000', 'http://127.0.0.1:3000', 'http://0.0.0.0:3000'],
  credentials: true
}))

// 解析JSON和表单数据
app.use(express.json())
app.use(express.urlencoded({ extended: true }))

// 创建自定义的HTTPS Agent，忽略SSL证书验证（仅用于开发环境）
const httpsAgent = new https.Agent({
  rejectUnauthorized: false // 忽略SSL证书验证
})


/**
 * 根据工号，获取用户名
 * POST /api/sso/usernamebyjobnumber
 * 参数：{ tokenId: string }
 */
app.post('/api/sso/usernamebyjobnumber', async (req, res) => {
  try {
    const { jobNumber} = req.body

    if (!jobNumber) {
      return res.status(400).json({
        success: false,
        error: 'jobNumber参数不能为空'
      })
    }

    const ssoConfig = {
      url: 'http://********:8090/jobByName'
    }

    // 发送验证请求
    const response = await axios.post(ssoConfig.url, req.body, {
      headers: {
        'Content-Type': 'application/json'
      },
      httpsAgent: httpsAgent, // 使用自定义的HTTPS Agent
      timeout: 10000 // 10秒超时
    })

    // 检查响应数据
    if (response.data && typeof response.data === 'object') {
      // 如果响应已经是对象，直接返回
      res.json({
        success: true,
        data: response.data
      })
    } else if (typeof response.data === 'string') {
      // 如果响应是字符串，尝试解析JSON
      try {
        const jsonData = JSON.parse(response.data)
        res.json({
          success: true,
            data: jsonData
        })
      } catch (parseError) {
        console.error('解析本地用户名服务器响应JSON失败:', parseError)
        res.status(500).json({
          success: false,
          error: '本地用户名服务器响应格式错误'
        })
      }
    } else {
      res.status(500).json({
        success: false,
        error: '本地用户名服务器响应格式异常'
      })
    }

  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      res.status(503).json({
        success: false,
        error: '本地用户名服务器不可用，请稍后重试'
      })
    }
  }
})



/**
 * SSO验证接口
 * POST /api/sso/validate
 * 参数：{ tokenId: string }
 */
app.post('/api/sso/validate', async (req, res) => {
  try {
    const { tokenId, access_key } = req.body

    if (!tokenId) {
      return res.status(400).json({
        success: false,
        error: 'tokenId参数不能为空'
      })
    }

    // 使用传入的access_key，如果没有则使用默认值
    const accessKey = access_key || '2e5295e9-f46d-45e0-8de0-8a1281a6da5c'

    // console.log('收到SSO验证请求，tokenId:', tokenId, 'access_key:', accessKey)

    // SSO验证配置（对应Java代码中的URL和参数）
    const ssoConfig = {
      url: 'https://oa.bodor.com:8443/portal/r/jd?cmd=com.actionsoft.apps.addons.sso_validate'
    }

    // 构建表单数据（对应Java代码的.form()方法）
    const formData = new URLSearchParams()
    formData.append('tokenId', tokenId)
    formData.append('access_key', accessKey)

    // 发送验证请求
    const response = await axios.post(ssoConfig.url, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      httpsAgent: httpsAgent, // 使用自定义的HTTPS Agent
      timeout: 10000 // 10秒超时
    })

    console.log('SSO验证响应状态:', response.status)
    console.log('SSO验证响应数据:', response.data)

    // 检查响应数据
    if (response.data && typeof response.data === 'object') {
      // 如果响应已经是对象，直接返回
      res.json({
        success: true,
        data: response.data
      })
    } else if (typeof response.data === 'string') {
      // 如果响应是字符串，尝试解析JSON
      try {
        const jsonData = JSON.parse(response.data)
        res.json({
          success: true,
          data: jsonData
        })
      } catch (parseError) {
        console.error('解析SSO响应JSON失败:', parseError)
        res.status(500).json({
          success: false,
          error: 'SSO服务响应格式错误'
        })
      }
    } else {
      res.status(500).json({
        success: false,
        error: 'SSO服务响应格式异常'
      })
    }

  } catch (error) {
    console.error('SSO验证请求失败:', error.message)
    
    if (error.code === 'ECONNREFUSED') {
      res.status(503).json({
        success: false,
        error: 'SSO服务不可用，请稍后重试'
      })
    } else if (error.code === 'ENOTFOUND') {
      res.status(503).json({
        success: false,
        error: 'SSO服务地址无法访问'
      })
    } else if (error.code === 'ETIMEDOUT') {
      res.status(504).json({
        success: false,
        error: 'SSO服务响应超时'
      })
    } else {
      res.status(500).json({
        success: false,
        error: error.message || 'SSO验证过程中发生错误'
      })
    }
  }
})

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'SSO Proxy Server'
  })
})

// 启动服务器
app.listen(PORT, () => {
  console.log('========================================')
  console.log('    SSO代理服务器启动成功')
  console.log('========================================')
  console.log(`监听端口: ${PORT}`)
  console.log(`健康检查: http://localhost:${PORT}/health`)
  console.log(`健康检查: http://127.0.0.1:${PORT}/health`)
  console.log(`SSO验证接口: http://localhost:${PORT}/api/sso/validate`)
  console.log(`SSO验证接口: http://127.0.0.1:${PORT}/api/sso/validate`)
  console.log('========================================')
})

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...')
  process.exit(0)
})

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务器...')
  process.exit(0)
})
