/**
 * Redis HTTP代理服务器
 * 为前端应用提供Redis访问的HTTP API接口
 * 运行在Node.js环境中，作为前端和Redis之间的桥梁
 */

const express = require('express')
const cors = require('cors')
const Redis = require('redis')

const app = express()
const PORT = 4001

// Redis配置
const REDIS_CONFIG = {
  host: '***********',
  port: 6379,
  password: 'X4gN7',
  db: 0
}

// 创建Redis客户端
let redisClient = null

async function initRedis() {
  try {
    redisClient = Redis.createClient({
      socket: {
        host: REDIS_CONFIG.host,
        port: REDIS_CONFIG.port
      },
      password: REDIS_CONFIG.password,
      database: REDIS_CONFIG.db
    })

    redisClient.on('error', (err) => {
      console.error('Redis连接错误:', err)
    })

    redisClient.on('connect', () => {
      console.log('Redis连接成功')
    })

    redisClient.on('ready', () => {
      console.log('Redis客户端就绪')
    })

    await redisClient.connect()
    console.log('Redis初始化完成')
  } catch (error) {
    console.error('Redis初始化失败:', error)
    process.exit(1)
  }
}

// 中间件
app.use(cors())
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// 认证中间件
app.use('/redis', (req, res, next) => {
  const authHeader = req.headers.authorization
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7)
    if (token === REDIS_CONFIG.password) {
      next()
    } else {
      res.status(401).json({ error: 'Invalid authentication token' })
    }
  } else {
    // 在开发环境中可以跳过认证
    if (process.env.NODE_ENV === 'development') {
      next()
    } else {
      res.status(401).json({ error: 'Authentication required' })
    }
  }
})

// 健康检查
app.get('/redis/ping', async (req, res) => {
  try {
    const result = await redisClient.ping()
    res.json(result)
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// SET操作
app.post('/redis/set', async (req, res) => {
  try {
    const { key, value, ttl } = req.body
    
    if (ttl) {
      await redisClient.setEx(key, ttl, value)
    } else {
      await redisClient.set(key, value)
    }
    
    res.json({ success: true })
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// GET操作
app.get('/redis/get/:key', async (req, res) => {
  try {
    const { key } = req.params
    const value = await redisClient.get(decodeURIComponent(key))
    
    if (value === null) {
      res.status(404).json({ error: 'Key not found' })
    } else {
      res.json({ value })
    }
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// DEL操作
app.delete('/redis/del/:key', async (req, res) => {
  try {
    const { key } = req.params
    const deleted = await redisClient.del(decodeURIComponent(key))
    res.json({ deleted })
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// HSET操作
app.post('/redis/hset', async (req, res) => {
  try {
    const { key, field, value } = req.body
    await redisClient.hSet(key, field, value)
    res.json({ success: true })
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// HGET操作
app.get('/redis/hget/:key/:field', async (req, res) => {
  try {
    const { key, field } = req.params
    const value = await redisClient.hGet(decodeURIComponent(key), decodeURIComponent(field))
    
    if (value === null) {
      res.status(404).json({ error: 'Field not found' })
    } else {
      res.json({ value })
    }
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// HGETALL操作
app.get('/redis/hgetall/:key', async (req, res) => {
  try {
    const { key } = req.params
    const value = await redisClient.hGetAll(decodeURIComponent(key))
    res.json({ value })
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// HDEL操作
app.delete('/redis/hdel/:key/:field', async (req, res) => {
  try {
    const { key, field } = req.params
    const deleted = await redisClient.hDel(decodeURIComponent(key), decodeURIComponent(field))
    res.json({ deleted })
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// EXPIRE操作
app.post('/redis/expire', async (req, res) => {
  try {
    const { key, ttl } = req.body
    const success = await redisClient.expire(key, ttl)
    res.json({ success: success === 1 })
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// EXISTS操作
app.get('/redis/exists/:key', async (req, res) => {
  try {
    const { key } = req.params
    const exists = await redisClient.exists(decodeURIComponent(key))
    res.json({ exists: exists === 1 })
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// KEYS操作
app.get('/redis/keys/:pattern', async (req, res) => {
  try {
    const { pattern } = req.params
    const keys = await redisClient.keys(decodeURIComponent(pattern))
    res.json({ keys })
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// Pipeline操作
app.post('/redis/pipeline', async (req, res) => {
  try {
    const { commands } = req.body
    const pipeline = redisClient.multi()
    
    commands.forEach(cmd => {
      switch (cmd.command) {
        case 'set':
          if (cmd.ttl) {
            pipeline.setEx(cmd.key, cmd.ttl, cmd.value)
          } else {
            pipeline.set(cmd.key, cmd.value)
          }
          break
        case 'get':
          pipeline.get(cmd.key)
          break
        case 'del':
          pipeline.del(cmd.key)
          break
        case 'hset':
          pipeline.hSet(cmd.key, cmd.field, cmd.value)
          break
        case 'hget':
          pipeline.hGet(cmd.key, cmd.field)
          break
        case 'expire':
          pipeline.expire(cmd.key, cmd.ttl)
          break
        default:
          throw new Error(`Unsupported command: ${cmd.command}`)
      }
    })
    
    const results = await pipeline.exec()
    res.json({ results })
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// PUBLISH操作
app.post('/redis/publish', async (req, res) => {
  try {
    const { channel, message } = req.body
    const subscribers = await redisClient.publish(channel, message)
    res.json({ subscribers })
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
})

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error)
  res.status(500).json({ error: 'Internal server error' })
})

// 启动服务器
async function startServer() {
  try {
    await initRedis()
    
    app.listen(PORT, '0.0.0.0', () => {
      console.log(`Redis HTTP代理服务器运行在端口 ${PORT}`)
      console.log(`Redis服务器: ${REDIS_CONFIG.host}:${REDIS_CONFIG.port}`)
      console.log('API端点: http://0.0.0.0:4001/redis')
      console.log('可通过以下地址访问:')
      console.log('  - http://localhost:4001/redis')
      console.log('  - http://127.0.0.1:4001/redis')
      console.log('  - http://[你的IP地址]:4001/redis')
    })
  } catch (error) {
    console.error('启动服务器失败:', error)
    process.exit(1)
  }
}

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('正在关闭服务器...')
  if (redisClient) {
    await redisClient.quit()
  }
  process.exit(0)
})

process.on('SIGTERM', async () => {
  console.log('正在关闭服务器...')
  if (redisClient) {
    await redisClient.quit()
  }
  process.exit(0)
})

startServer()
