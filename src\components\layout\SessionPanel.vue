<template>
  <!-- 会话管理面板 -->
  <div class="session-panel" :class="{ 'collapsed': collapsed }">
    <!-- 折叠状态：只显示必要的内容 -->
    <div v-if="collapsed" class="collapsed-content">
      <!-- 折叠状态下可以显示当前会话的简单指示器 -->
      <div v-if="currentSession" class="current-session-indicator" :title="currentSession.title">
        <div class="session-dot active"></div>
      </div>
    </div>

    <!-- 展开状态：显示完整内容 -->
    <template v-else>
      <!-- 面板头部 -->
      <div class="panel-header">
        <div class="header-top">
          <h3 class="panel-title">会话管理</h3>
          <!-- 移动端关闭按钮 -->
          <button
            class="mobile-close-btn"
            @click="handleMobileClose"
            title="关闭会话面板"
          >
            <CloseIcon />
          </button>
        </div>

        <!-- 新建会话按钮 -->
        <div class="new-session-btn-container">
          <button
            class="new-session-btn"
            @click="handleNewSession"
            title="新建会话"
          >
            <PlusIcon />
            <span>新会话</span>
          </button>
        </div>
      </div>

      <!-- 会话列表 -->
      <div class="session-list">
        <div
          v-for="session in sessions"
          :key="session.id"
          class="session-item"
          :class="{
            'active': currentSession?.id === session.id,
            'pinned': session.pinned
          }"
          @click="handleSessionClick(session)"
        >
          <!-- 会话信息 -->
          <div class="session-info">
          <!-- 会话标题（支持编辑） -->
          <div class="session-title-container">
            <!-- 顶置图标 -->
            <div v-if="session.pinned" class="pin-indicator" title="已置顶">
              <PinIcon />
            </div>

            <!-- 编辑状态 -->
            <input
              v-if="editingSessionId === session.id"
              v-model="editingTitle"
              class="session-title-input"
              @blur="handleTitleBlur(session)"
              @keyup.enter="handleTitleConfirm(session)"
              @keyup.escape="handleTitleCancel"
              ref="titleInput"
              maxlength="50"
            />

            <!-- 显示状态 -->
            <div
              v-else
              class="session-title"
              @dblclick="handleTitleEdit(session)"
              :title="`${session.title} (双击编辑)`"
            >
              {{ session.title }}
            </div>
          </div>

          <div class="session-meta">
            <span class="session-module">{{ getModuleName(session.module) }}</span>
            <span class="session-time">{{ formatTime(session.updatedAt) }}</span>
          </div>
          <div class="session-preview" v-if="session.messages.length > 0">
            {{ getLastMessage(session) }}
          </div>
        </div>

        <!-- 会话操作（仅在展开状态显示） -->
        <div v-if="!collapsed" class="session-actions">
          <!-- 顶置按钮 -->
          <button
            class="action-btn pin-btn"
            :class="{ 'pinned': session.pinned }"
            @click.stop="handleTogglePin(session)"
            :title="session.pinned ? '取消置顶' : '置顶会话'"
          >
            <PinIcon />
          </button>

          <!-- 删除按钮 -->
          <button
            class="action-btn delete-btn-trigger"
            @click.stop="handleDeleteSession(session)"
            title="删除会话"
          >
            <DeleteIcon />
          </button>
        </div>

      </div>
    </div>

      <!-- 空状态 -->
      <div v-if="sessions.length === 0" class="empty-state">
        <div class="empty-icon">💬</div>
        <div class="empty-text">暂无会话</div>
        <div class="empty-hint">点击"新会话"开始对话</div>
      </div>
    </template>
  </div>

  <!-- 删除确认对话框 -->
  <ConfirmDialog
    :visible="showDeleteDialog"
    :title="deleteDialogTitle"
    :message="deleteDialogMessage"
    confirm-text="删除"
    cancel-text="取消"
    @confirm="confirmDelete"
    @cancel="cancelDelete"
    @close="cancelDelete"
  />
</template>

<script setup>
import { ref, nextTick, onMounted, onUnmounted } from 'vue'
import ConfirmDialog from '../common/ConfirmDialog.vue'

// 图标组件
const PlusIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
    </svg>
  `
}

const CloseIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
    </svg>
  `
}

const DeleteIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM8 9h8v10H8V9zm7.5-5l-1-1h-5l-1 1H5v2h14V4h-3.5z"/>
    </svg>
  `
}

const PinIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M16,12V4H17V2H7V4H8V12L6,14V16H11.2V22H12.8V16H18V14L16,12Z"/>
    </svg>
  `
}



// 组件属性
const props = defineProps({
  // 会话列表
  sessions: {
    type: Array,
    default: () => []
  },
  // 当前选中的会话
  currentSession: {
    type: Object,
    default: null
  },
  // 是否折叠状态
  collapsed: {
    type: Boolean,
    default: false
  }
})

// 组件事件
const emit = defineEmits(['new-session', 'session-change', 'delete-session', 'rename-session', 'toggle-pin', 'mobile-close'])

// 模块名称映射
const moduleNames = {
  knowledge: '知识中心',
  business: '业务域',
  function: '职能域',
  help: '帮助反馈'
}

// 编辑相关的响应式数据
const editingSessionId = ref(null) // 当前正在编辑的会话ID
const editingTitle = ref('') // 编辑中的标题
const titleInput = ref(null) // 标题输入框引用

// 删除确认对话框相关数据
const showDeleteDialog = ref(false) // 是否显示删除确认对话框
const deleteTargetSession = ref(null) // 要删除的会话对象
const deleteDialogTitle = ref('删除会话') // 对话框标题
const deleteDialogMessage = ref('') // 对话框消息

/**
 * 获取模块显示名称
 * @param {string} moduleKey - 模块键值
 * @returns {string} 模块显示名称
 */
const getModuleName = (moduleKey) => {
  return moduleNames[moduleKey] || '未知模块'
}

/**
 * 格式化时间显示
 * @param {Date} date - 时间对象
 * @returns {string} 格式化后的时间字符串
 */
const formatTime = (date) => {
  if (!date) return ''
  
  const now = new Date()
  const diff = now - new Date(date)
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  
  return new Date(date).toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  })
}

/**
 * 处理点击外部关闭编辑状态
 * @param {Event} event - 点击事件
 */
const handleClickOutside = (event) => {
  // 如果点击的不是会话相关元素，关闭编辑状态
  if (!event.target.closest('.session-item')) {
    // 可以在这里添加其他需要关闭的状态
  }
}

// 组件挂载时添加全局点击监听
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

// 组件卸载时移除全局点击监听
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

/**
 * 获取会话最后一条消息的预览
 * @param {Object} session - 会话对象
 * @returns {string} 消息预览文本
 */
const getLastMessage = (session) => {
  if (!session.messages || session.messages.length === 0) {
    return '暂无消息'
  }
  
  const lastMessage = session.messages[session.messages.length - 1]
  const content = lastMessage.content || ''
  
  // 限制预览长度
  return content.length > 30 ? content.substring(0, 30) + '...' : content
}

/**
 * 处理新建会话
 */
const handleNewSession = () => {
  console.log('点击新建会话')
  emit('new-session')
}

/**
 * 处理移动端关闭会话面板
 */
const handleMobileClose = () => {
  console.log('移动端关闭会话面板')
  emit('mobile-close')
}

/**
 * 处理会话点击
 * @param {Object} session - 点击的会话对象
 */
const handleSessionClick = (session) => {
  if (session.id !== props.currentSession?.id) {
    console.log('切换到会话:', session.title)
    emit('session-change', session)
  }
}

/**
 * 处理标题编辑
 * @param {Object} session - 要编辑的会话对象
 */
const handleTitleEdit = (session) => {
  // 如果已经在编辑其他会话，先取消
  if (editingSessionId.value && editingSessionId.value !== session.id) {
    handleTitleCancel()
  }

  editingSessionId.value = session.id
  editingTitle.value = session.title

  // 关闭菜单
  showMenu.value = null

  // 下一帧聚焦输入框并选中文本
  nextTick(() => {
    const inputElement = document.querySelector('.session-title-input')
    if (inputElement) {
      inputElement.focus()
      inputElement.select()
    }
  })
}

/**
 * 处理标题确认
 * @param {Object} session - 会话对象
 */
const handleTitleConfirm = (session) => {
  const newTitle = editingTitle.value.trim()

  if (newTitle && newTitle !== session.title) {
    console.log('重命名会话:', session.title, '->', newTitle)
    emit('rename-session', session, newTitle)
  }

  // 退出编辑状态
  editingSessionId.value = null
  editingTitle.value = ''
}

/**
 * 处理标题失焦
 * @param {Object} session - 会话对象
 */
const handleTitleBlur = (session) => {
  // 延迟处理，避免与其他事件冲突
  setTimeout(() => {
    if (editingSessionId.value === session.id) {
      handleTitleConfirm(session)
    }
  }, 100)
}

/**
 * 处理标题取消
 */
const handleTitleCancel = () => {
  editingSessionId.value = null
  editingTitle.value = ''
}

/**
 * 处理置顶切换
 * @param {Object} session - 会话对象
 */
const handleTogglePin = (session) => {
  console.log('切换会话置顶状态:', session.title, session.pinned ? '取消置顶' : '置顶')
  emit('toggle-pin', session)
  // 关闭菜单
  showMenu.value = null
}

/**
 * 处理删除会话 - 直接弹出确认对话框
 * @param {Object} session - 要删除的会话对象
 */
const handleDeleteSession = (session) => {
  // 设置删除对话框数据
  deleteTargetSession.value = session
  deleteDialogTitle.value = '删除会话确认'

  const messageCount = session.messages?.length || 0
  deleteDialogMessage.value = messageCount > 0
    ? `确定要删除会话"${session.title}"吗？\n\n此会话包含 ${messageCount} 条消息，删除后无法恢复。`
    : `确定要删除会话"${session.title}"吗？\n\n删除后无法恢复。`

  // 显示确认对话框
  showDeleteDialog.value = true

  console.log('准备删除会话:', session.title, `(${messageCount} 条消息)`)
}

/**
 * 确认删除会话
 */
const confirmDelete = () => {
  if (deleteTargetSession.value) {
    const session = deleteTargetSession.value
    const messageCount = session.messages?.length || 0

    console.log('用户确认删除会话:', session.title, `(${messageCount} 条消息)`)

    // 发送删除事件到父组件
    emit('delete-session', session)

    // 显示删除成功提示（可选）
    console.log(`会话"${session.title}"已删除`)
  }

  // 关闭对话框并清理数据
  cancelDelete()
}

/**
 * 取消删除会话
 */
const cancelDelete = () => {
  const sessionTitle = deleteTargetSession.value?.title

  // 清理状态
  showDeleteDialog.value = false
  deleteTargetSession.value = null
  deleteDialogTitle.value = ''
  deleteDialogMessage.value = ''

  if (sessionTitle) {
    console.log('用户取消删除会话:', sessionTitle)
  }
}
</script>

<style scoped>
/* 会话面板容器 */
.session-panel {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
  border-right: 1px solid var(--border-light);
  overflow: hidden;
}

/* 折叠状态的会话面板 */
.session-panel.collapsed {
  background-color: transparent;
  border-right: none;
  justify-content: flex-start;
  padding-top: 60px;
}

/* 面板头部 */
.panel-header {
  padding: 20px 16px 16px;
  border-bottom: 1px solid var(--border-secondary);
  background-color: var(--bg-secondary);
}

/* 折叠状态的面板头部 */
.session-panel.collapsed .panel-header {
  padding: 12px 8px;
}

/* 折叠状态的内容 */
.collapsed-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 0;
  width: 100%;
}

/* 当前会话指示器 */
.current-session-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-bottom: 8px;
}

.current-session-indicator .session-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--accent-primary);
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
}

/* 新建会话按钮容器 */
.new-session-btn-container {
  width: 100%;
  position: relative;
  overflow: hidden;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 新建会话按钮 */
.new-session-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 16px;
  background: var(--accent-primary);
  color: var(--text-white);
  border: none !important;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none !important;
  box-shadow: none !important;
}

.new-session-btn:focus {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

.new-session-btn:active {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

.new-session-btn::before,
.new-session-btn::after {
  display: none !important;
}

.new-session-btn *,
.new-session-btn *::before,
.new-session-btn *::after {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.new-session-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* 折叠状态的新建会话按钮 */
.new-session-btn.collapsed {
  width: 34px;
  height: 34px;
  padding: 8px;
  border-radius: 50%;
  justify-content: center;
}

.new-session-btn.collapsed span {
  display: none;
}

.new-session-btn svg {
  width: 16px;
  height: 16px;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: none !important;
}

/* 会话列表 */
.session-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

/* 会话项 */
.session-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 12px;
  margin-bottom: 4px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid transparent;
  position: relative;
  transform: translateX(0);
}

/* 置顶会话的特殊样式 */
.session-item.pinned {
  background: var(--pinned-bg);
  border-color: var(--pinned-border);
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.1);
}

.session-item:hover {
  background-color: var(--bg-secondary);
  border-color: var(--border-primary);
  box-shadow: var(--shadow-sm);
  transform: translateX(2px);
}

.session-item.pinned:hover {
  background: var(--pinned-hover-bg);
  border-color: var(--pinned-hover-border);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.2);
}

.session-item.active {
  background-color: var(--accent-secondary);
  border-color: var(--accent-primary);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}



/* 会话信息 */
.session-info {
  flex: 1;
  min-width: 0;
}

/* 会话标题容器 */
.session-title-container {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
}

/* 置顶指示器 */
.pin-indicator {
  color: var(--pinned-indicator);
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.pin-indicator svg {
  width: 12px;
  height: 12px;
}

/* 会话标题 */
.session-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.session-title:hover {
  background-color: var(--bg-tertiary);
}

/* 标题输入框 */
.session-title-input {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border: 2px solid var(--accent-primary);
  border-radius: 4px;
  padding: 2px 6px;
  outline: none;
  flex: 1;
  min-width: 0;
}

.session-title-input:focus {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.session-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.session-module {
  font-size: 12px;
  color: var(--accent-primary);
  background-color: var(--accent-secondary);
  padding: 2px 6px;
  border-radius: 4px;
}

.session-time {
  font-size: 12px;
  color: var(--text-secondary);
}

.session-preview {
  font-size: 12px;
  color: var(--text-tertiary);
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 会话操作按钮 */
.session-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0;
  transition: all 0.2s ease;
  flex-shrink: 0;
  margin-left: 8px;
}

/* 操作按钮基础样式 */
.action-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 6px;
  background-color: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
}

.action-btn:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.action-btn svg {
  width: 16px;
  height: 16px;
}

/* 顶置按钮样式 */
.pin-btn.pinned {
  color: var(--pinned-indicator);
  background-color: var(--pinned-bg);
}

.pin-btn.pinned:hover {
  color: var(--pinned-hover-border);
  background-color: var(--pinned-hover-bg);
}

/* 删除按钮样式 */
.delete-btn-trigger {
  position: relative;
}

.delete-btn-trigger:hover {
  color: #ef4444;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  transform: translateY(-1px) scale(1.05);
}

.delete-btn-trigger:hover::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 1px solid #ef4444;
  border-radius: 8px;
  opacity: 0.3;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}



/* 悬停时显示操作按钮 */
.session-item:hover .session-actions {
  opacity: 1;
}

/* 选中状态时也显示操作按钮 */
.session-item.active .session-actions {
  opacity: 0.7;
}

.session-item.active:hover .session-actions {
  opacity: 1;
}



/* 操作按钮图标动画 */
.action-btn svg {
  transition: all 0.2s ease;
}

.pin-btn:hover svg {
  transform: rotate(15deg);
}

.delete-btn-trigger:hover svg {
  transform: scale(1.1);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.empty-hint {
  font-size: 14px;
  color: var(--text-tertiary);
}

/* 响应式设计 */

/* 平板端适配 */
@media (max-width: 1024px) {
  .panel-header {
    padding: var(--spacing-md) var(--spacing-md) var(--spacing-sm);
  }

  .panel-title {
    font-size: var(--font-size-lg);
  }

  .new-session-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
  }

  .session-item {
    padding: var(--spacing-sm) var(--spacing-md);
    margin-bottom: var(--spacing-sm);
  }

  .session-title {
    font-size: var(--font-size-sm);
  }

  .session-meta {
    font-size: var(--font-size-xs);
  }

  .session-panel.collapsed .new-session-btn {
    width: 30px;
    height: 30px;
    padding: var(--spacing-xs);
  }

  .session-indicator {
    width: 20px;
    height: 20px;
  }

  .session-dot {
    width: 6px;
    height: 6px;
  }

  .pin-icon-small svg {
    width: 8px;
    height: 8px;
  }
}

/* 移动端适配 - 抽屉式布局 */
@media (max-width: 768px) {
  .session-panel {
    flex-direction: column;
    padding: var(--spacing-md);
    background: var(--bg-primary);
    border-right: none;
    box-shadow: var(--shadow-xl);
  }

  /* 折叠状态下隐藏内容 */
  .session-panel.collapsed {
    padding: 0;
    overflow: hidden;
  }

  .session-panel.collapsed .panel-header,
  .session-panel.collapsed .session-list,
  .session-panel.collapsed .empty-state {
    display: none;
  }

  .collapsed-content {
    display: none; /* 移动端折叠时完全隐藏 */
  }

  .current-session-indicator {
    display: none; /* 移动端不显示指示器 */
  }

  /* 移动端面板头部 */
  .panel-header {
    flex-shrink: 0;
    padding: var(--spacing-lg) 0 var(--spacing-md);
    border-bottom: 1px solid var(--border-light);
    margin-bottom: var(--spacing-md);
    position: relative;
  }

  .header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
  }

  .panel-title {
    font-size: var(--font-size-lg);
    margin: 0;
    font-weight: 600;
    color: var(--text-primary);
  }

  .mobile-close-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-md);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .mobile-close-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-secondary);
  }

  .mobile-close-btn svg {
    width: 16px;
    height: 16px;
  }

  .new-session-btn {
    width: 100%;
    padding: var(--spacing-md);
    font-size: var(--font-size-base);
    justify-content: center;
    border-radius: var(--border-radius-lg);
    font-weight: 500;
  }

  .session-list {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    gap: var(--spacing-xs);
  }

  .session-item {
    margin-bottom: var(--spacing-xs);
    padding: var(--spacing-sm);
  }

  .session-title {
    font-size: var(--font-size-sm);
  }

  .session-meta {
    font-size: var(--font-size-xs);
  }

  .empty-state {
    padding: var(--spacing-lg);
    text-align: center;
  }

  .empty-icon {
    font-size: 28px;
    margin-bottom: var(--spacing-sm);
  }

  .empty-text {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
  }

  .empty-hint {
    font-size: var(--font-size-xs);
  }
}

/* 小屏手机适配 */
@media (max-width: 480px) {
  .session-panel {
    padding: var(--spacing-xs);
  }

  .panel-header {
    padding: var(--spacing-xs) 0;
  }

  .panel-title {
    font-size: var(--font-size-sm);
  }

  .new-session-btn {
    padding: var(--spacing-xs);
    font-size: var(--font-size-xs);
  }

  .session-item {
    padding: var(--spacing-xs);
  }

  .session-title {
    font-size: var(--font-size-xs);
  }

  .session-meta {
    font-size: 10px;
  }
}
</style>
