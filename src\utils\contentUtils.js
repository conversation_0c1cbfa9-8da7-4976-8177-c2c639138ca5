/**
 * 内容处理工具函数
 * 用于处理AI回复中可能出现的各种数据类型问题
 */

/**
 * 安全地将任何类型的内容转换为字符串
 * @param {any} content - 需要转换的内容
 * @param {string} fallback - 转换失败时的备用文本
 * @returns {string} 安全的字符串内容
 */
export function safeStringify(content, fallback = '内容格式错误') {
  // 处理 null 和 undefined
  if (content === null || content === undefined) {
    return ''
  }
  
  // 如果已经是字符串，直接返回
  if (typeof content === 'string') {
    return content
  }
  
  // 处理数字和布尔值
  if (typeof content === 'number' || typeof content === 'boolean') {
    return String(content)
  }
  
  // 处理对象和数组
  if (typeof content === 'object') {
    try {
      // 检查是否是错误对象
      if (content instanceof Error) {
        return content.message || content.toString()
      }
      
      // 检查是否有 toString 方法且不是默认的 [object Object]
      if (content.toString && content.toString() !== '[object Object]') {
        const stringResult = content.toString()
        if (stringResult !== '[object Object]') {
          return stringResult
        }
      }
      
      // 尝试 JSON 序列化
      return JSON.stringify(content, null, 2)
    } catch (error) {
      console.error('对象转换为字符串失败:', error, content)
      return fallback
    }
  }
  
  // 处理函数
  if (typeof content === 'function') {
    return content.toString()
  }
  
  // 其他类型，强制转换为字符串
  try {
    return String(content)
  } catch (error) {
    console.error('强制转换为字符串失败:', error, content)
    return fallback
  }
}

/**
 * 验证内容是否为有效的字符串
 * @param {any} content - 需要验证的内容
 * @returns {boolean} 是否为有效字符串
 */
export function isValidString(content) {
  return typeof content === 'string' && content.trim().length > 0
}

/**
 * 清理和格式化AI回复内容
 * @param {any} content - AI回复内容
 * @returns {string} 清理后的内容
 */
export function cleanAIContent(content) {
  const safeContent = safeStringify(content)

  // 移除可能的控制字符
  let cleaned = safeContent
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // 移除控制字符
    .replace(/\r\n/g, '\n') // 统一换行符
    .replace(/\r/g, '\n')
    .trim()

  // 修复可能的对象字符串问题
  cleaned = fixObjectStrings(cleaned)

  return cleaned
}

/**
 * 修复内容中的对象字符串问题
 * @param {string} content - 内容字符串
 * @returns {string} 修复后的内容
 */
export function fixObjectStrings(content) {
  if (typeof content !== 'string') {
    return safeStringify(content)
  }

  // 查找并替换 [object Object] 模式
  let fixed = content.replace(/\[object Object\]/g, '[数据对象]')

  // 查找并修复代码块中的对象问题
  fixed = fixed.replace(/```(\w+)?\s*\[object Object\]\s*```/g, (match, lang) => {
    console.warn('发现代码块中的对象问题:', match)
    return `\`\`\`${lang || 'text'}\n[代码内容格式错误]\n\`\`\``
  })

  // 查找并修复行内代码中的对象问题
  fixed = fixed.replace(/`\[object Object\]`/g, '`[对象]`')

  // 查找其他可能的对象字符串模式
  fixed = fixed.replace(/\[object (\w+)\]/g, (match, type) => {
    console.warn('发现对象字符串:', match)
    return `[${type}对象]`
  })

  return fixed
}

/**
 * Markdown内容预处理
 * @param {any} content - 原始内容
 * @returns {string} 预处理后的Markdown内容
 */
export function preprocessMarkdown(content) {
  // 首先确保是安全的字符串
  let safeContent = cleanAIContent(content)

  // 检查是否包含对象字符串
  if (safeContent.includes('[object Object]')) {
    console.warn('Markdown内容包含对象字符串，进行修复:', safeContent.substring(0, 200))
    safeContent = fixObjectStrings(safeContent)
  }

  // 验证代码块的完整性
  safeContent = validateCodeBlocks(safeContent)

  return safeContent
}

/**
 * 验证和修复代码块
 * @param {string} content - Markdown内容
 * @returns {string} 修复后的内容
 */
export function validateCodeBlocks(content) {
  // 查找所有代码块
  const codeBlockRegex = /```(\w+)?\s*([\s\S]*?)```/g

  return content.replace(codeBlockRegex, (match, language, code) => {
    // 检查代码内容是否有问题
    if (!code || code.trim() === '' || code.includes('[object Object]')) {
      console.warn('发现有问题的代码块:', { language, code: code?.substring(0, 100) })

      if (code && code.includes('[object Object]')) {
        // 尝试修复对象问题
        const fixedCode = code.replace(/\[object Object\]/g, '// [对象内容无法显示]')
        return `\`\`\`${language || 'text'}\n${fixedCode}\n\`\`\``
      } else {
        // 空代码块
        return `\`\`\`${language || 'text'}\n// 代码内容为空\n\`\`\``
      }
    }

    // 代码块正常，返回原内容
    return match
  })
}

/**
 * 检测内容类型并提供调试信息
 * @param {any} content - 需要检测的内容
 * @returns {Object} 内容类型信息
 */
export function debugContent(content) {
  const info = {
    type: typeof content,
    isNull: content === null,
    isUndefined: content === undefined,
    isArray: Array.isArray(content),
    constructor: content?.constructor?.name,
    stringValue: null,
    hasToString: false,
    toStringResult: null
  }
  
  try {
    info.stringValue = String(content)
  } catch (error) {
    info.stringValue = '[转换失败]'
  }
  
  if (content && typeof content.toString === 'function') {
    info.hasToString = true
    try {
      info.toStringResult = content.toString()
    } catch (error) {
      info.toStringResult = '[toString失败]'
    }
  }
  
  return info
}

/**
 * 处理流式内容更新
 * @param {any} chunk - 新的内容块
 * @param {string} currentContent - 当前累积的内容
 * @returns {string} 更新后的内容
 */
export function handleStreamChunk(chunk, currentContent = '') {
  const safeChunk = safeStringify(chunk)
  const safeCurrentContent = safeStringify(currentContent)
  
  return safeCurrentContent + safeChunk
}

/**
 * 验证消息对象的完整性
 * @param {Object} message - 消息对象
 * @returns {Object} 验证结果和修复后的消息
 */
export function validateMessage(message) {
  const result = {
    isValid: true,
    errors: [],
    fixedMessage: { ...message }
  }
  
  // 检查必需字段
  if (!message.id) {
    result.errors.push('缺少消息ID')
    result.fixedMessage.id = Date.now().toString()
  }
  
  if (!message.type) {
    result.errors.push('缺少消息类型')
    result.fixedMessage.type = 'ai'
  }
  
  // 检查内容字段
  if (message.content !== undefined) {
    const contentInfo = debugContent(message.content)
    if (contentInfo.type === 'object' && contentInfo.toStringResult === '[object Object]') {
      result.errors.push('内容是未处理的对象')
      result.fixedMessage.content = safeStringify(message.content)
    } else if (!isValidString(message.content) && message.content !== '') {
      result.errors.push('内容不是有效字符串')
      result.fixedMessage.content = safeStringify(message.content)
    }
  }
  
  // 检查流式内容
  if (message.streamingContent !== undefined) {
    if (!isValidString(message.streamingContent) && message.streamingContent !== '') {
      result.errors.push('流式内容不是有效字符串')
      result.fixedMessage.streamingContent = safeStringify(message.streamingContent)
    }
  }
  
  result.isValid = result.errors.length === 0
  
  return result
}

/**
 * 批量验证和修复消息列表
 * @param {Array} messages - 消息列表
 * @returns {Array} 修复后的消息列表
 */
export function validateMessages(messages) {
  if (!Array.isArray(messages)) {
    console.warn('消息列表不是数组:', messages)
    return []
  }
  
  return messages.map((message, index) => {
    const validation = validateMessage(message)
    
    if (!validation.isValid) {
      console.warn(`消息 ${index} 存在问题:`, validation.errors, message)
    }
    
    return validation.fixedMessage
  })
}

// 默认导出
export default {
  safeStringify,
  isValidString,
  cleanAIContent,
  fixObjectStrings,
  preprocessMarkdown,
  validateCodeBlocks,
  debugContent,
  handleStreamChunk,
  validateMessage,
  validateMessages
}
