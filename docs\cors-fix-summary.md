# CORS跨域问题修复总结

## 问题描述

原始错误信息：
```
Access to fetch at 'http://***********:3006/api/v1/chat/completions' from origin 'http://localhost:3000' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

## 解决方案实施

### 1. Vite代理配置

在 `vite.config.js` 中添加了代理配置：

```javascript
export default defineConfig({
  server: {
    host: '0.0.0.0',
    port: 3000,
    open: true,
    proxy: {
      // AI API代理
      '/api/v1': {
        target: 'http://***********:3006',
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('AI API代理错误:', err)
          })
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('AI API代理请求:', req.method, req.url)
          })
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('AI API代理响应:', proxyRes.statusCode, req.url)
          })
        }
      }
    }
  }
})
```

### 2. AI配置修改

修改了 `src/config/ai.js` 中的配置：

**修改前：**
```javascript
baseURL: 'http://***********:3006/api/v1'
```

**修改后：**
```javascript
baseURL: '/api/v1'  // 使用代理路径
```

### 3. 环境变量支持

创建了环境变量配置文件：

**`.env.development`:**
```bash
VITE_AI_BASE_URL=/api/v1
VITE_AI_API_KEY=fastgpt-zifErOcL2pn5t2cDGGnuLd1iVKC5TvdVDvnnCv16wLusOocffWYOkk
VITE_AI_MODEL=Qwen3-235B-A22B
VITE_AI_ENABLE_MOCK=true
```

**`.env.production`:**
```bash
VITE_AI_BASE_URL=http://***********:3006/api/v1
VITE_AI_API_KEY=fastgpt-zifErOcL2pn5t2cDGGnuLd1iVKC5TvdVDvnnCv16wLusOocffWYOkk
VITE_AI_MODEL=Qwen3-235B-A22B
VITE_AI_ENABLE_MOCK=false
```

### 4. 配置文件优化

更新了AI配置文件以支持环境变量：

```javascript
// 从环境变量获取配置
const getEnvConfig = () => ({
  baseURL: import.meta.env.VITE_AI_BASE_URL || '/api/v1',
  apiKey: import.meta.env.VITE_AI_API_KEY || 'default-key',
  model: import.meta.env.VITE_AI_MODEL || 'Qwen3-235B-A22B',
  enableMockMode: import.meta.env.VITE_AI_ENABLE_MOCK === 'true'
})

const envConfig = getEnvConfig()

export const knowledgeAIConfig = {
  baseURL: envConfig.baseURL,
  apiKey: envConfig.apiKey,
  model: envConfig.model,
  enableMockMode: envConfig.enableMockMode,
  // 其他配置...
}
```

## 验证结果

启动开发服务器后，控制台输出显示代理正常工作：

```
VITE v4.5.14  ready in 437 ms

➜  Local:   http://localhost:3000/
➜  Network: http://*************:3000/

AI API代理请求: POST /api/v1/chat/completions
AI API代理响应: 200 /api/v1/chat/completions
```

## 修改的文件列表

1. `vite.config.js` - 添加代理配置
2. `src/config/ai.js` - 修改API配置和环境变量支持
3. `.env.development` - 开发环境变量配置
4. `.env.production` - 生产环境变量配置
5. `docs/cors-solution-guide.md` - CORS解决方案指南
6. `docs/ai-conversation-flow.md` - 更新对话流程文档

## 技术要点

### 代理工作原理
1. 前端请求 `http://localhost:3000/api/v1/chat/completions`
2. Vite代理将请求转发到 `http://***********:3006/api/v1/chat/completions`
3. 后端响应通过代理返回给前端
4. 浏览器认为这是同源请求，不会触发CORS限制

### 环境区分
- **开发环境**: 使用 `/api/v1` 代理路径
- **生产环境**: 使用完整URL `http://***********:3006/api/v1`

### 错误处理
- 启用模拟模式作为降级方案
- 代理错误日志记录
- 请求响应状态监控

## 后续建议

1. **生产部署**: 考虑使用Nginx反向代理
2. **安全性**: 在生产环境中配置适当的CORS策略
3. **监控**: 添加API请求监控和错误追踪
4. **缓存**: 考虑添加API响应缓存机制

## 总结

通过Vite代理配置，成功解决了开发环境中的CORS跨域问题，同时通过环境变量配置实现了开发和生产环境的灵活切换。这种解决方案具有以下优势：

- ✅ 无需修改后端代码
- ✅ 开发体验良好
- ✅ 支持环境变量配置
- ✅ 支持错误处理和降级
- ✅ 易于维护和扩展
- ✅ 验证通过，代理正常工作
