<template>
  <!-- 知识中心页面 - 类似常见AI在线问答界面 -->
  <SimpleWatermark module="knowledge">
    <div class="knowledge-center">
    <!-- 聊天头部区域 -->
    <div class="chat-header">
      <div class="ai-avatar">
        <div class="avatar-icon">💡</div>
      </div>
      <div class="ai-info">
        <h2 class="ai-name">小邦同学智能搜索</h2>
        <p class="ai-description">欢迎使用知识中心，我可以帮您快速查找资料并提供专业解答，有什么问题尽管问我吧～</p>
      </div>
    </div>
    
    <!-- 消息列表区域 -->
    <div class="message-container" ref="messageContainer">
      <div class="message-list">
        <!-- 欢迎消息（仅在没有消息时显示） -->
        <div v-if="messages.length === 0" class="welcome-message">
          <div class="welcome-content">
            <div class="welcome-icon">
                <div class="avatar-icon"><img src="../../../public/favicon.ico" alt="AI头像" style="width: 20%; height: 20%; object-fit: cover;"></div>
            </div>
            <h3>开始您的智能问答之旅</h3>
            <p>您可以询问邦德激光任何问题，我会尽力为您提供准确的答案</p>
          </div>
        </div>
        
        <!-- 消息列表 -->
        <div 
          v-for="message in messages" 
          :key="message.id"
          class="message-item"
          :class="{ 'user-message': message.type === 'user', 'ai-message': message.type === 'ai' }"
        >
          <!-- 用户消息 -->
          <div v-if="message.type === 'user'" class="message-content user-content">
            <div class="message-bubble user-bubble">
              {{ message.content }}
            </div>
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
          
          <!-- AI消息 -->
          <AIMessage
            v-else
            :message="message"
            :streaming-content="message.streamingContent"
            @retry="handleRetryMessage"
          />
        </div>
        
        <!-- 加载状态现在由消息本身的loading属性控制，不需要单独的加载组件 -->

        <!-- 消息重试组件 -->
        <MessageRetry
          v-if="showRetry"
          :show-retry="showRetry"
          :error-message="errorMessage"
          :is-retrying="isRetrying"
          @retry="handleRetryMessage"
          @cancel="handleCancelRetry"
        />
      </div>
    </div>
    
    <!-- 输入区域 -->
    <div class="input-area">
      <div class="input-container">
        <div class="input-actions">
          <button
            class="action-button search-button"
            @click="toggleSearch"
            title="搜索消息 (Ctrl+F)"
          >
            <SearchIcon />
          </button>
          <button
            class="action-button clear-button"
            @click="clearChat"
            title="清空对话 (Ctrl+Shift+K)"
            :disabled="messages.length === 0"
          >
            <ClearIcon />
          </button>
        </div>

        <div class="input-wrapper">
          <textarea
            v-model="inputText"
            ref="textareaRef"
            class="message-input"
            placeholder="输入问题，发送[Enter]/换行[Ctrl+Enter]"
            rows="1"
            @keydown="handleKeydown"
            @input="adjustTextareaHeight"
          ></textarea>

          <button
            class="send-button"
            :disabled="!inputText.trim() || isLoading"
            @click="handleSendMessage"
            title="发送消息 (Ctrl+Enter)"
          >
            <SendIcon />
          </button>
        </div>
      </div>
    </div>

    <!-- 消息搜索组件 -->
    <MessageSearch
      :is-visible="showSearch"
      :messages="messages"
      @close="closeSearch"
      @jump-to-message="jumpToMessage"
    />
    </div>
  </SimpleWatermark>
</template>

<script setup>
import { ref, nextTick, watch, onMounted, defineProps, defineEmits } from 'vue'
import AIMessage from '../common/AIMessage.vue'
import MessageRetry from '../common/MessageRetry.vue'
import MessageSearch from '../common/MessageSearch.vue'
import SimpleWatermark from '../common/SimpleWatermark.vue'
import { useKeyboardShortcuts } from '../../composables/useKeyboardShortcuts.js'

// 图标组件
const SendIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
    </svg>
  `
}

const SearchIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
    </svg>
  `
}

const ClearIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
    </svg>
  `
}

// 组件属性
const props = defineProps({
  // 当前会话
  currentSession: {
    type: Object,
    default: null
  },
  // 消息列表
  messages: {
    type: Array,
    default: () => []
  }
})

// 组件事件
const emit = defineEmits(['send-message', 'clear-messages'])

// 响应式数据
const inputText = ref('') // 输入框文本
const messageContainer = ref(null) // 消息容器引用
const textareaRef = ref(null) // 输入框引用

// 用户体验增强
const showRetry = ref(false) // 显示重试组件
const retryMessage = ref(null) // 需要重试的消息
const isRetrying = ref(false) // 重试状态
const showSearch = ref(false) // 显示搜索组件
const errorMessage = ref('') // 错误消息

// 键盘快捷键
const { registerShortcut, SHORTCUTS } = useKeyboardShortcuts()

/**
 * 格式化时间显示
 * @param {Date} timestamp - 时间戳
 * @returns {string} 格式化后的时间
 */
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  
  const date = new Date(timestamp)
  const now = new Date()
  const isToday = date.toDateString() === now.toDateString()
  
  if (isToday) {
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}

/**
 * 自动调整输入框高度
 */
const adjustTextareaHeight = () => {
  nextTick(() => {
    const textarea = textareaRef.value
    if (textarea) {
      textarea.style.height = 'auto'
      textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'
    }
  })
}

/**
 * 滚动到消息列表底部
 */
const scrollToBottom = () => {
  nextTick(() => {
    if (messageContainer.value) {
      messageContainer.value.scrollTop = messageContainer.value.scrollHeight
    }
  })
}

/**
 * 处理键盘事件
 * @param {KeyboardEvent} event - 键盘事件
 */
const handleKeydown = (event) => {
  // Enter发送，Ctrl+Enter换行
  if (event.key === 'Enter') {
    if (event.ctrlKey) {
      // Ctrl+Enter 换行，不做处理，让默认行为发生
      return
    } else {
      // Enter 发送消息
      event.preventDefault()
      handleSendMessage()
    }
  }
}

/**
 * 发送消息
 */
const handleSendMessage = () => {
  const content = inputText.value.trim()
  if (!content) return

  console.log('知识中心发送消息:', content)

  // 隐藏重试组件
  showRetry.value = false

  // 发送消息事件
  emit('send-message', content)

  // 清空输入框
  inputText.value = ''
  adjustTextareaHeight()

  // 滚动到底部
  scrollToBottom()
}

/**
 * 显示消息错误
 */
const showMessageError = (message) => {
  errorMessage.value = message
  showRetry.value = true
  retryMessage.value = inputText.value
  console.log('消息发送失败:', message)
}

/**
 * 重试发送消息
 */
const handleRetryMessage = () => {
  if (!retryMessage.value) return

  isRetrying.value = true
  console.log('重试发送消息:', retryMessage.value)

  // 模拟重试
  setTimeout(() => {
    isRetrying.value = false
    showRetry.value = false

    // 重试成功率90%
    if (Math.random() < 0.9) {
      console.log('重试成功')
      scrollToBottom()
    } else {
      showMessageError('重试失败，请检查网络连接')
    }
  }, 1000)
}

/**
 * 取消重试
 */
const handleCancelRetry = () => {
  showRetry.value = false
  retryMessage.value = null
  isRetrying.value = false
  console.log('取消重试')
}

/**
 * 切换搜索
 */
const toggleSearch = () => {
  showSearch.value = !showSearch.value
  console.log('切换搜索:', showSearch.value)
}

/**
 * 关闭搜索
 */
const closeSearch = () => {
  showSearch.value = false
}

/**
 * 跳转到指定消息
 */
const jumpToMessage = (message) => {
  // 这里可以实现滚动到指定消息的逻辑
  console.log('跳转到消息:', message.id)

  // 简单实现：滚动到底部
  scrollToBottom()
}

/**
 * 清空对话
 */
const clearChat = () => {
  if (props.messages.length === 0) return

  if (confirm('确定要清空所有对话记录吗？此操作不可撤销。')) {
    console.log('清空对话')
    emit('clear-messages')
  }
}

// 初始化键盘快捷键
onMounted(() => {
  // 注册快捷键
  registerShortcut(SHORTCUTS.SEARCH, toggleSearch, '搜索消息')
  registerShortcut(SHORTCUTS.CLEAR_CHAT, clearChat, '清空对话')
  registerShortcut(SHORTCUTS.FOCUS_INPUT, () => {
    textareaRef.value?.focus()
  }, '聚焦输入框')
  registerShortcut(SHORTCUTS.ESCAPE, () => {
    if (showSearch.value) {
      closeSearch()
    } else if (showRetry.value) {
      handleCancelRetry()
    }
  }, '取消/关闭')

  console.log('知识中心快捷键已注册')
})

// 监听消息变化，自动滚动到底部
watch(() => props.messages, (newMessages, oldMessages) => {
  console.log('📨 KnowledgeCenter 消息变化:', {
    newCount: newMessages?.length || 0,
    oldCount: oldMessages?.length || 0,
    sessionTitle: props.currentSession?.title
  })
  scrollToBottom()
}, { deep: true })

// 监听当前会话变化
watch(() => props.currentSession, (newSession, oldSession) => {
  console.log('🔄 KnowledgeCenter 会话变化:', {
    oldTitle: oldSession?.title,
    newTitle: newSession?.title,
    messagesCount: newSession?.messages?.length || 0
  })
}, { deep: true })
</script>

<style scoped>
/* 知识中心容器 */
.knowledge-center {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
  overflow: hidden;
  position: relative;
}

/* 聊天头部 */
.chat-header {
  display: flex;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-secondary);
  background-color: var(--bg-secondary);
}

.ai-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--accent-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.avatar-icon {
  font-size: 24px;
}

.ai-info h2 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.ai-description {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

/* 消息容器 */
.message-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 24px;
}

.message-list {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px 0;
}

/* 欢迎消息 */
.welcome-message {
  text-align: center;
  padding: 60px 20px;
}

.welcome-content {
  max-width: 400px;
  margin: 0 auto;
}

.welcome-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.welcome-content h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.welcome-content p {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 消息项 */
.message-item {
  margin-bottom: 16px;
}

.message-content {
  display: flex;
  align-items: flex-end;
}

/* 用户消息 */
.user-content {
  justify-content: flex-end;
  flex-direction: column;
  align-items: flex-end;
}

.user-bubble {
  background: var(--accent-primary);
  color: var(--text-white);
  margin-left: 60px;
}

/* AI消息 */
.ai-content {
  justify-content: flex-start;
}

.ai-avatar-small {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--accent-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin-right: 12px;
  flex-shrink: 0;
}

.ai-message-wrapper {
  flex: 1;
  max-width: calc(100% - 44px);
}

.ai-bubble {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  margin-right: 60px;
}

/* 消息气泡 */
.message-bubble {
  padding: 12px 16px;
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.5;
  word-wrap: break-word;
  max-width: 100%;
}

/* 消息时间 */
.message-time {
  font-size: 12px;
  color: #9ca3af;
  margin-top: 4px;
  text-align: right;
}

.ai-content .message-time {
  text-align: left;
}

/* 加载和错误状态消息 */
.loading-message {
  background-color: #f8fafc !important;
  position: relative;
}

.error-message {
  background-color: #fef2f2 !important;
  border-left: 3px solid #ef4444;
}

/* 加载动画 */
.loading-dots {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 0;
}

.loading-dots span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #9ca3af;
  animation: loading-bounce 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #9ca3af;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

/* 输入区域 */
.input-area {
  border-top: 1px solid var(--border-secondary);
  background-color: var(--bg-primary);
  padding: 16px 24px;
}

.input-container {
  max-width: 800px;
  margin: 0 auto;
}

/* 输入操作按钮 */
.input-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  justify-content: flex-end;
}

.action-button {
  width: 36px;
  height: 36px;
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  background-color: var(--bg-primary);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-button:hover:not(:disabled) {
  background-color: var(--bg-secondary);
  border-color: var(--accent-primary);
  color: var(--accent-primary);
  transform: translateY(-1px);
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-button svg {
  width: 16px;
  height: 16px;
}

.search-button:hover {
  background-color: var(--accent-secondary);
}

.clear-button:hover:not(:disabled) {
  background-color: #fef2f2;
  border-color: #ef4444;
  color: #ef4444;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  padding: 12px;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  transition: border-color 0.2s ease;
}

.input-wrapper:focus-within {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.message-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  color: var(--text-primary);
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  min-height: 20px;
  max-height: 120px;
}

.message-input::placeholder {
  color: var(--text-tertiary);
}

.send-button {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  background: var(--accent-primary);
  color: var(--text-white);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.send-button svg {
  width: 16px;
  height: 16px;
}

/* 响应式设计 */

/* 平板端适配 */
@media (max-width: 1024px) {
  .knowledge-center {
    padding: var(--spacing-sm);
  }

  .chat-header {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
  }

  .ai-name {
    font-size: var(--font-size-xl);
  }

  .ai-description {
    font-size: var(--font-size-sm);
  }

  .message-container {
    padding: 0 var(--spacing-sm);
  }

  .input-area {
    padding: var(--spacing-md);
  }

  .input-wrapper {
    padding: var(--spacing-sm);
    gap: var(--spacing-sm);
  }

  .message-input {
    font-size: var(--font-size-sm);
  }

  .send-button {
    width: 32px;
    height: 32px;
  }

  .send-button svg {
    width: 14px;
    height: 14px;
  }

  .action-button {
    width: 32px;
    height: 32px;
  }

  .action-button svg {
    width: 14px;
    height: 14px;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .knowledge-center {
    padding: var(--spacing-xs);
  }

  .chat-header {
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    flex-direction: column;
    text-align: center;
  }

  .ai-avatar {
    margin-right: 0;
    margin-bottom: var(--spacing-sm);
  }

  .ai-name {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
  }

  .ai-description {
    font-size: var(--font-size-sm);
    line-height: 1.4;
  }

  .message-container {
    padding: 0 var(--spacing-xs);
  }

  .input-area {
    padding: var(--spacing-sm);
  }

  .input-container {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .input-actions {
    order: 2;
    justify-content: center;
    gap: var(--spacing-md);
  }

  .input-wrapper {
    order: 1;
    padding: var(--spacing-sm);
  }

  .message-input {
    font-size: var(--font-size-base);
    min-height: 24px;
  }

  .send-button {
    width: 40px;
    height: 40px;
  }

  .send-button svg {
    width: 16px;
    height: 16px;
  }

  .action-button {
    width: 36px;
    height: 36px;
  }

  .action-button svg {
    width: 16px;
    height: 16px;
  }

  .welcome-content h3 {
    font-size: var(--font-size-lg);
  }

  .welcome-content p {
    font-size: var(--font-size-sm);
  }
}

/* 小屏手机适配 */
@media (max-width: 480px) {
  .chat-header {
    padding: var(--spacing-xs);
  }

  .ai-name {
    font-size: var(--font-size-base);
  }

  .ai-description {
    font-size: var(--font-size-xs);
  }

  .input-area {
    padding: var(--spacing-xs);
  }

  .input-wrapper {
    padding: var(--spacing-xs);
    gap: var(--spacing-xs);
  }

  .message-input {
    font-size: var(--font-size-sm);
  }

  .send-button {
    width: 36px;
    height: 36px;
  }

  .action-button {
    width: 32px;
    height: 32px;
  }

  .action-button svg {
    width: 14px;
    height: 14px;
  }
}
</style>
