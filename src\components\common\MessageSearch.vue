<template>
  <!-- 消息搜索组件 -->
  <div class="message-search" v-if="isVisible">
    <div class="search-overlay" @click="closeSearch"></div>
    <div class="search-container">
      <div class="search-header">
        <h3 class="search-title">搜索消息</h3>
        <button class="close-button" @click="closeSearch" title="关闭搜索">
          <CloseIcon />
        </button>
      </div>
      
      <div class="search-input-container">
        <div class="search-input-wrapper">
          <SearchIcon />
          <input
            ref="searchInput"
            v-model="searchQuery"
            type="text"
            class="search-input"
            placeholder="输入关键词搜索消息..."
            @input="handleSearch"
            @keydown.escape="closeSearch"
            @keydown.enter="selectNext"
            @keydown.up.prevent="selectPrevious"
            @keydown.down.prevent="selectNext"
          />
          <div class="search-stats" v-if="searchQuery">
            {{ currentIndex + 1 }} / {{ searchResults.length }}
          </div>
        </div>
        
        <div class="search-controls">
          <button 
            class="nav-button"
            @click="selectPrevious"
            :disabled="searchResults.length === 0"
            title="上一个结果"
          >
            <UpIcon />
          </button>
          <button 
            class="nav-button"
            @click="selectNext"
            :disabled="searchResults.length === 0"
            title="下一个结果"
          >
            <DownIcon />
          </button>
        </div>
      </div>
      
      <div class="search-options">
        <label class="option-item">
          <input 
            type="checkbox" 
            v-model="caseSensitive"
            @change="handleSearch"
          />
          <span>区分大小写</span>
        </label>
        <label class="option-item">
          <input 
            type="checkbox" 
            v-model="wholeWord"
            @change="handleSearch"
          />
          <span>全词匹配</span>
        </label>
      </div>
      
      <div class="search-results" v-if="searchQuery && searchResults.length > 0">
        <div class="results-header">
          <span>搜索结果 ({{ searchResults.length }})</span>
        </div>
        <div class="results-list">
          <div 
            v-for="(result, index) in searchResults.slice(0, 50)"
            :key="result.id"
            class="result-item"
            :class="{ 'active': index === currentIndex }"
            @click="jumpToMessage(result, index)"
          >
            <div class="result-content">
              <div class="result-text" v-html="highlightText(result.content)"></div>
              <div class="result-meta">
                <span class="result-type">{{ result.type === 'user' ? '我' : 'AI' }}</span>
                <span class="result-time">{{ formatTime(result.timestamp) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="no-results" v-else-if="searchQuery && searchResults.length === 0">
        <div class="no-results-icon">🔍</div>
        <div class="no-results-text">未找到匹配的消息</div>
        <div class="no-results-hint">尝试使用不同的关键词</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, defineProps, defineEmits } from 'vue'

// 图标组件
const SearchIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
    </svg>
  `
}

const CloseIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
    </svg>
  `
}

const UpIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z"/>
    </svg>
  `
}

const DownIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
    </svg>
  `
}

// 组件属性
const props = defineProps({
  // 是否显示搜索组件
  isVisible: {
    type: Boolean,
    default: false
  },
  // 要搜索的消息列表
  messages: {
    type: Array,
    default: () => []
  }
})

// 组件事件
const emit = defineEmits(['close', 'jump-to-message'])

// 响应式数据
const searchInput = ref(null)
const searchQuery = ref('')
const caseSensitive = ref(false)
const wholeWord = ref(false)
const currentIndex = ref(0)

// 搜索结果
const searchResults = computed(() => {
  if (!searchQuery.value.trim()) return []
  
  const query = caseSensitive.value ? searchQuery.value : searchQuery.value.toLowerCase()
  const results = []
  
  props.messages.forEach(message => {
    if (!message.content) return
    
    const content = caseSensitive.value ? message.content : message.content.toLowerCase()
    
    let isMatch = false
    if (wholeWord.value) {
      const regex = new RegExp(`\\b${escapeRegExp(query)}\\b`, caseSensitive.value ? 'g' : 'gi')
      isMatch = regex.test(content)
    } else {
      isMatch = content.includes(query)
    }
    
    if (isMatch) {
      results.push(message)
    }
  })
  
  return results
})

/**
 * 转义正则表达式特殊字符
 */
const escapeRegExp = (string) => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

/**
 * 高亮搜索文本
 */
const highlightText = (text) => {
  if (!searchQuery.value.trim()) return text
  
  const query = escapeRegExp(searchQuery.value)
  const flags = caseSensitive.value ? 'g' : 'gi'
  const regex = wholeWord.value 
    ? new RegExp(`\\b(${query})\\b`, flags)
    : new RegExp(`(${query})`, flags)
  
  return text.replace(regex, '<mark class="search-highlight">$1</mark>')
}

/**
 * 格式化时间
 */
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  currentIndex.value = 0
  console.log('搜索消息:', searchQuery.value, '结果数量:', searchResults.value.length)
}

/**
 * 选择下一个结果
 */
const selectNext = () => {
  if (searchResults.value.length === 0) return
  
  currentIndex.value = (currentIndex.value + 1) % searchResults.value.length
  jumpToCurrentResult()
}

/**
 * 选择上一个结果
 */
const selectPrevious = () => {
  if (searchResults.value.length === 0) return
  
  currentIndex.value = currentIndex.value === 0 
    ? searchResults.value.length - 1 
    : currentIndex.value - 1
  jumpToCurrentResult()
}

/**
 * 跳转到当前结果
 */
const jumpToCurrentResult = () => {
  if (searchResults.value.length > 0) {
    const result = searchResults.value[currentIndex.value]
    jumpToMessage(result, currentIndex.value)
  }
}

/**
 * 跳转到指定消息
 */
const jumpToMessage = (message, index) => {
  currentIndex.value = index
  emit('jump-to-message', message)
  console.log('跳转到消息:', message.id)
}

/**
 * 关闭搜索
 */
const closeSearch = () => {
  searchQuery.value = ''
  currentIndex.value = 0
  emit('close')
}

// 监听显示状态，自动聚焦输入框
watch(() => props.isVisible, (visible) => {
  if (visible) {
    nextTick(() => {
      searchInput.value?.focus()
    })
  }
})
</script>

<style scoped>
/* 搜索组件容器 */
.message-search {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 10vh;
}

.search-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.search-container {
  position: relative;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  background-color: var(--bg-primary, #ffffff);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 搜索头部 */
.search-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-primary, #e5e7eb);
  background-color: var(--bg-secondary, #f8fafc);
}

.search-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #1f2937);
  margin: 0;
}

.close-button {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background-color: transparent;
  color: var(--text-secondary, #6b7280);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: var(--bg-tertiary, #f1f5f9);
  color: var(--text-primary, #1f2937);
}

.close-button svg {
  width: 18px;
  height: 18px;
}

/* 搜索输入区域 */
.search-input-container {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-secondary, #f3f4f6);
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.search-input-wrapper svg {
  position: absolute;
  left: 12px;
  width: 18px;
  height: 18px;
  color: var(--text-tertiary, #9ca3af);
}

.search-input {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 1px solid var(--border-primary, #e5e7eb);
  border-radius: 8px;
  font-size: 14px;
  background-color: var(--bg-primary, #ffffff);
  color: var(--text-primary, #1f2937);
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--accent-primary, #3b82f6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-stats {
  position: absolute;
  right: 12px;
  font-size: 12px;
  color: var(--text-tertiary, #9ca3af);
  background-color: var(--bg-secondary, #f8fafc);
  padding: 2px 6px;
  border-radius: 4px;
}

.search-controls {
  display: flex;
  gap: 4px;
}

.nav-button {
  width: 32px;
  height: 32px;
  border: 1px solid var(--border-primary, #e5e7eb);
  border-radius: 6px;
  background-color: var(--bg-primary, #ffffff);
  color: var(--text-secondary, #6b7280);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.nav-button:hover:not(:disabled) {
  background-color: var(--bg-secondary, #f8fafc);
  border-color: var(--accent-primary, #3b82f6);
}

.nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.nav-button svg {
  width: 16px;
  height: 16px;
}

/* 搜索选项 */
.search-options {
  display: flex;
  gap: 16px;
  padding: 0 20px 16px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: var(--text-secondary, #6b7280);
  cursor: pointer;
}

.option-item input[type="checkbox"] {
  margin: 0;
}

/* 搜索结果 */
.search-results {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.results-header {
  padding: 12px 20px;
  background-color: var(--bg-secondary, #f8fafc);
  border-bottom: 1px solid var(--border-secondary, #f3f4f6);
  font-size: 13px;
  font-weight: 500;
  color: var(--text-secondary, #6b7280);
}

.results-list {
  flex: 1;
  overflow-y: auto;
}

.result-item {
  padding: 12px 20px;
  border-bottom: 1px solid var(--border-secondary, #f3f4f6);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.result-item:hover {
  background-color: var(--bg-secondary, #f8fafc);
}

.result-item.active {
  background-color: var(--accent-secondary, #eff6ff);
  border-left: 3px solid var(--accent-primary, #3b82f6);
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.result-text {
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary, #1f2937);
}

.result-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: var(--text-tertiary, #9ca3af);
}

.result-type {
  font-weight: 500;
}

/* 搜索高亮 */
:deep(.search-highlight) {
  background-color: #fef08a;
  color: #92400e;
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: 500;
}

/* 无结果状态 */
.no-results {
  padding: 40px 20px;
  text-align: center;
  color: var(--text-secondary, #6b7280);
}

.no-results-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-results-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.no-results-hint {
  font-size: 14px;
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-container {
    width: 95%;
    max-height: 90vh;
  }
  
  .search-header,
  .search-input-container,
  .results-header {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  .result-item {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  .search-options {
    padding-left: 16px;
    padding-right: 16px;
    flex-direction: column;
    gap: 8px;
  }
}
</style>
