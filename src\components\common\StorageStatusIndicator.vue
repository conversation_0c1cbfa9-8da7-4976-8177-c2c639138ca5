<template>
  <div class="storage-status-indicator">
    <!-- 存储状态指示器 -->
    <div 
      class="status-badge" 
      :class="statusClass"
      @click="toggleDetails"
      :title="statusTooltip"
    >
      <div class="status-icon">
        <span v-if="storageStatus.type === 'redis'" class="redis-icon">R</span>
        <span v-else class="local-icon">L</span>
      </div>
      <div class="status-text">
        {{ statusText }}
      </div>
    </div>

    <!-- 详细信息面板 -->
    <div v-if="showDetails" class="details-panel">
      <div class="details-header">
        <h4>存储服务状态</h4>
        <button class="close-btn" @click="showDetails = false">×</button>
      </div>
      
      <div class="details-content">
        <div class="status-item">
          <label>存储类型:</label>
          <span :class="typeClass">{{ storageTypeText }}</span>
        </div>
        
        <div class="status-item">
          <label>连接状态:</label>
          <span :class="connectionClass">{{ connectionText }}</span>
        </div>
        
        <div v-if="storageStatus.error" class="status-item error">
          <label>错误信息:</label>
          <span>{{ storageStatus.error }}</span>
        </div>
        
        <div v-if="storageStatus.type === 'redis'" class="redis-details">
          <div class="status-item">
            <label>Redis服务器:</label>
            <span>{{ redisConfig.host }}:{{ redisConfig.port }}</span>
          </div>
          
          <div class="status-item">
            <label>数据库:</label>
            <span>{{ redisConfig.db }}</span>
          </div>
        </div>
        
        <div class="actions">
          <button 
            v-if="storageStatus.type === 'redis' && !storageStatus.connected"
            class="action-btn reconnect"
            @click="handleReconnect"
            :disabled="reconnecting"
          >
            {{ reconnecting ? '重连中...' : '重新连接' }}
          </button>
          
          <button 
            v-if="storageStatus.type === 'localStorage'"
            class="action-btn migrate"
            @click="handleMigration"
            :disabled="migrating"
          >
            {{ migrating ? '迁移中...' : '迁移到Redis' }}
          </button>
          
          <button 
            class="action-btn info"
            @click="showStorageInfo"
          >
            存储信息
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { getRedisConfig } from '../../config/redis.js'

// Props
const props = defineProps({
  storageStatus: {
    type: Object,
    required: true
  },
  storageService: {
    type: Object,
    default: null
  },
  connectionManager: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['reconnect', 'migrate', 'show-info'])

// 状态
const showDetails = ref(false)
const reconnecting = ref(false)
const migrating = ref(false)
const redisConfig = getRedisConfig()

// 计算属性
const statusClass = computed(() => {
  const baseClass = 'status-badge'
  if (props.storageStatus.type === 'redis') {
    return props.storageStatus.connected ? 
      `${baseClass} redis connected` : 
      `${baseClass} redis disconnected`
  } else {
    return `${baseClass} local`
  }
})

const statusText = computed(() => {
  if (props.storageStatus.type === 'redis') {
    return props.storageStatus.connected ? 'Redis' : 'Redis (断开)'
  } else {
    return '本地存储'
  }
})

const statusTooltip = computed(() => {
  if (props.storageStatus.type === 'redis') {
    return props.storageStatus.connected ? 
      'Redis存储服务已连接' : 
      'Redis存储服务已断开，点击查看详情'
  } else {
    return '使用本地存储服务'
  }
})

const storageTypeText = computed(() => {
  return props.storageStatus.type === 'redis' ? 'Redis存储' : '本地存储'
})

const typeClass = computed(() => {
  return props.storageStatus.type === 'redis' ? 'redis-type' : 'local-type'
})

const connectionText = computed(() => {
  return props.storageStatus.connected ? '已连接' : '未连接'
})

const connectionClass = computed(() => {
  return props.storageStatus.connected ? 'connected' : 'disconnected'
})

// 方法
const toggleDetails = () => {
  showDetails.value = !showDetails.value
}

const handleReconnect = async () => {
  if (reconnecting.value) return
  
  reconnecting.value = true
  try {
    await emit('reconnect')
  } finally {
    reconnecting.value = false
  }
}

const handleMigration = async () => {
  if (migrating.value) return
  
  migrating.value = true
  try {
    await emit('migrate')
  } finally {
    migrating.value = false
  }
}

const showStorageInfo = () => {
  emit('show-info')
}

// 点击外部关闭详情面板
const handleClickOutside = (event) => {
  if (showDetails.value && !event.target.closest('.storage-status-indicator')) {
    showDetails.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.storage-status-indicator {
  position: relative;
  display: inline-block;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.status-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-badge.redis.connected {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border-color: #059669;
}

.status-badge.redis.disconnected {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border-color: #d97706;
}

.status-badge.local {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
  border-color: #4f46e5;
}

.status-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  background: rgba(255, 255, 255, 0.2);
}

.details-panel {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  width: 300px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow: hidden;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.details-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #374151;
}

.details-content {
  padding: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 13px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-item label {
  font-weight: 500;
  color: #6b7280;
}

.status-item span {
  font-weight: 500;
}

.status-item.error {
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.status-item.error span {
  color: #dc2626;
  font-size: 12px;
  word-break: break-all;
}

.redis-type {
  color: #059669;
}

.local-type {
  color: #4f46e5;
}

.connected {
  color: #059669;
}

.disconnected {
  color: #dc2626;
}

.redis-details {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e5e7eb;
}

.actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-btn.reconnect {
  background: #f59e0b;
  color: white;
}

.action-btn.reconnect:hover:not(:disabled) {
  background: #d97706;
}

.action-btn.migrate {
  background: #10b981;
  color: white;
}

.action-btn.migrate:hover:not(:disabled) {
  background: #059669;
}

.action-btn.info {
  background: #6366f1;
  color: white;
}

.action-btn.info:hover {
  background: #4f46e5;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .status-badge {
    padding: 3px 6px;
    font-size: 11px;
    gap: 4px;
  }

  .status-icon {
    width: 14px;
    height: 14px;
    font-size: 9px;
  }

  .status-text {
    display: none; /* 在移动端隐藏文字，只显示图标 */
  }

  .details-panel {
    width: 280px;
    right: -20px; /* 调整位置避免超出屏幕 */
  }
}

/* 小屏手机适配 */
@media (max-width: 480px) {
  .status-badge {
    padding: 2px 4px;
    font-size: 10px;
    gap: 2px;
  }

  .status-icon {
    width: 12px;
    height: 12px;
    font-size: 8px;
  }

  .details-panel {
    width: calc(100vw - 32px);
    right: -60px;
    left: auto;
  }
}

/* 移动端工具栏样式 */
.mobile-toolbar-item .status-badge {
  background: transparent !important;
  border: none !important;
  color: var(--text-primary) !important;
  padding: 4px 8px;
  font-size: 12px;
}

.mobile-toolbar-item .status-text {
  display: inline !important;
}

.mobile-toolbar-item .status-icon {
  width: 16px;
  height: 16px;
  font-size: 10px;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .details-panel {
    background: #1f2937;
    border-color: #374151;
  }

  .details-header {
    background: #111827;
    border-color: #374151;
  }

  .details-header h4 {
    color: #f9fafb;
  }

  .close-btn {
    color: #9ca3af;
  }

  .close-btn:hover {
    color: #f9fafb;
  }

  .status-item label {
    color: #9ca3af;
  }

  .status-item span {
    color: #f9fafb;
  }

  .redis-details {
    border-color: #374151;
  }

  .actions {
    border-color: #374151;
  }
}
</style>
