# 主题切换与通知框颜色配置

## 概述

本文档描述了主题切换功能和通知框在不同主题下的颜色配置，特别是Moon模式下通知框字体为红色的实现。

## 主题系统

### 1. 主题命名

应用使用以下主题命名约定：

- **Sun模式** (`data-theme="sun"`) - 浅色主题
- **Moon模式** (`data-theme="moon"`) - 深色主题

### 2. 主题切换组件

位置：`src/components/common/ThemeToggle.vue`

#### 2.1 核心功能

```javascript
const applyTheme = (dark) => {
  const root = document.documentElement
  
  if (dark) {
    root.setAttribute('data-theme', 'moon')
    root.classList.add('dark-theme')
    root.classList.remove('light-theme')
  } else {
    root.setAttribute('data-theme', 'sun')
    root.classList.add('light-theme')
    root.classList.remove('dark-theme')
  }
}
```

#### 2.2 主题持久化

```javascript
const saveThemePreference = (dark) => {
  localStorage.setItem('theme-preference', dark ? 'moon' : 'sun')
}

const loadThemePreference = () => {
  const saved = localStorage.getItem('theme-preference')
  
  if (saved) {
    isDark.value = saved === 'moon'
  } else {
    // 检测系统主题偏好
    isDark.value = window.matchMedia('(prefers-color-scheme: dark)').matches
  }
  
  applyTheme(isDark.value)
}
```

## 通知框颜色配置

### 1. 基础样式

位置：`src/components/layout/MainLayout.vue`

#### 1.1 Sun模式（浅色主题）

```css
/* 成功通知 */
.notification-success .notification-content {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #135200;
}

/* 信息通知 */
.notification-info .notification-content {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  color: #003a8c;
}

/* 警告通知 */
.notification-warning .notification-content {
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
  color: #613400;
}

/* 错误通知 */
.notification-error .notification-content {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  color: #a8071a;
}
```

#### 1.2 Moon模式（深色主题）

```css
/* 统一的红色字体 */
[data-theme="moon"] .notification-message {
  color: #ff4d4f;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 不同类型的背景色和边框色 */
[data-theme="moon"] .notification-success .notification-content {
  background-color: rgba(82, 196, 26, 0.15);
  border: 1px solid rgba(82, 196, 26, 0.3);
}

[data-theme="moon"] .notification-info .notification-content {
  background-color: rgba(24, 144, 255, 0.15);
  border: 1px solid rgba(24, 144, 255, 0.3);
}

[data-theme="moon"] .notification-warning .notification-content {
  background-color: rgba(250, 173, 20, 0.15);
  border: 1px solid rgba(250, 173, 20, 0.3);
}

[data-theme="moon"] .notification-error .notification-content {
  background-color: rgba(255, 77, 79, 0.15);
  border: 1px solid rgba(255, 77, 79, 0.3);
}
```

### 2. 设计理念

#### 2.1 Moon模式字体颜色选择

- **颜色值**: `#ff4d4f` (红色)
- **理由**: 
  - 在深色背景下具有良好的对比度
  - 红色能够有效吸引用户注意
  - 统一的字体颜色确保视觉一致性

#### 2.2 背景色区分

虽然字体颜色统一为红色，但通过不同的背景色和边框色来区分通知类型：

- **成功**: 绿色系半透明背景
- **信息**: 蓝色系半透明背景
- **警告**: 橙色系半透明背景
- **错误**: 红色系半透明背景

## 测试功能

### 1. 全局测试函数

在浏览器控制台中可以使用以下函数：

```javascript
// 测试主题切换和通知框颜色
window.testThemeAndNotification()
```

### 2. 测试步骤

1. **切换到Moon模式**
   ```javascript
   // 手动切换主题
   document.documentElement.setAttribute('data-theme', 'moon')
   ```

2. **触发测试通知**
   ```javascript
   window.testThemeAndNotification()
   ```

3. **观察结果**
   - 所有通知的字体应该显示为红色
   - 不同类型的通知应该有不同的背景色
   - 字体应该清晰可读

### 3. 验证清单

- [ ] Moon模式下通知字体为红色 (#ff4d4f)
- [ ] Sun模式下通知字体保持原有颜色
- [ ] 主题切换功能正常工作
- [ ] 主题偏好能够正确保存和加载
- [ ] 不同类型通知的背景色正确显示

## 使用示例

### 1. 手动切换主题

```javascript
// 切换到Moon模式
document.documentElement.setAttribute('data-theme', 'moon')

// 切换到Sun模式
document.documentElement.setAttribute('data-theme', 'sun')
```

### 2. 显示测试通知

```javascript
// 显示成功通知
showNotification('操作成功！', 'success')

// 显示信息通知
showNotification('这是一条信息', 'info')

// 显示警告通知
showNotification('请注意！', 'warning')

// 显示错误通知
showNotification('发生错误！', 'error')
```

### 3. 检查当前主题

```javascript
// 获取当前主题
const currentTheme = document.documentElement.getAttribute('data-theme')
console.log('当前主题:', currentTheme) // 'sun' 或 'moon'
```

## 兼容性说明

### 1. 浏览器支持

- ✅ Chrome/Edge (现代版本)
- ✅ Firefox (现代版本)
- ✅ Safari (现代版本)
- ✅ 移动端浏览器

### 2. 向后兼容

- 保持了原有的 `dark-theme` 和 `light-theme` 类名
- 新增了 `data-theme` 属性支持
- 主题偏好存储格式已更新为 'moon'/'sun'

## 自定义配置

### 1. 修改Moon模式字体颜色

如需修改Moon模式下的通知字体颜色，编辑以下CSS：

```css
[data-theme="moon"] .notification-message {
  color: #your-color-here;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}
```

### 2. 添加新主题

可以通过添加新的 `data-theme` 值来支持更多主题：

```css
[data-theme="custom"] .notification-message {
  color: #custom-color;
}
```

## 总结

通过使用 `data-theme` 属性和统一的红色字体配置，Moon模式下的通知框具有良好的可读性和视觉效果。主题切换功能支持用户偏好保存和系统主题检测，提供了完整的主题管理解决方案。
