<template>
  <!-- 帮助与反馈页面 -->
  <div class="help-feedback">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">帮助与反馈</h1>
        <p class="page-description">使用指南、常见问题和意见反馈</p>
      </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="content-container">
        <!-- 标签页导航 -->
        <div class="tab-navigation">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            class="tab-button"
            :class="{ 'active': activeTab === tab.id }"
            @click="switchTab(tab.id)"
          >
            <span class="tab-icon">{{ tab.icon }}</span>
            <span class="tab-text">{{ tab.name }}</span>
          </button>
        </div>
        
        <!-- 标签页内容 -->
        <div class="tab-content">
          <!-- 使用指南 -->
          <div v-if="activeTab === 'guide'" class="content-section">
            <div class="guide-content">
              <div class="guide-item" v-for="guide in guideItems" :key="guide.id">
                <div class="guide-header">
                  <div class="guide-icon">{{ guide.icon }}</div>
                  <h3 class="guide-title">{{ guide.title }}</h3>
                </div>
                <div class="guide-description">{{ guide.description }}</div>
                <div class="guide-steps">
                  <div v-for="(step, index) in guide.steps" :key="index" class="step-item">
                    <span class="step-number">{{ index + 1 }}</span>
                    <span class="step-text">{{ step }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 常见问题 -->
          <div v-if="activeTab === 'faq'" class="content-section">
            <div class="faq-content">
              <div class="faq-item" v-for="faq in faqItems" :key="faq.id">
                <div 
                  class="faq-question"
                  @click="toggleFaq(faq.id)"
                  :class="{ 'expanded': expandedFaq === faq.id }"
                >
                  <span class="question-text">{{ faq.question }}</span>
                  <span class="expand-icon">{{ expandedFaq === faq.id ? '−' : '+' }}</span>
                </div>
                <div 
                  class="faq-answer"
                  :class="{ 'show': expandedFaq === faq.id }"
                >
                  {{ faq.answer }}
                </div>
              </div>
            </div>
          </div>
          
          <!-- 意见反馈 -->
          <div v-if="activeTab === 'feedback'" class="content-section">
            <div class="feedback-content">
              <div class="feedback-form">
                <h3 class="form-title">意见反馈</h3>
                <p class="form-description">您的反馈对我们非常重要，请告诉我们您的想法和建议</p>
                
                <form @submit.prevent="submitFeedback">
                  <!-- 反馈类型 -->
                  <div class="form-group">
                    <label class="form-label">反馈类型</label>
                    <div class="radio-group">
                      <label v-for="type in feedbackTypes" :key="type.id" class="radio-item">
                        <input 
                          type="radio" 
                          :value="type.id" 
                          v-model="feedbackForm.type"
                          class="radio-input"
                        >
                        <span class="radio-label">{{ type.name }}</span>
                      </label>
                    </div>
                  </div>
                  
                  <!-- 反馈内容 -->
                  <div class="form-group">
                    <label class="form-label">反馈内容</label>
                    <textarea
                      v-model="feedbackForm.content"
                      class="form-textarea"
                      placeholder="请详细描述您的问题或建议..."
                      rows="6"
                      required
                    ></textarea>
                  </div>
                  
                  <!-- 联系方式 -->
                  <div class="form-group">
                    <label class="form-label">联系方式（可选）</label>
                    <input
                      v-model="feedbackForm.contact"
                      type="text"
                      class="form-input"
                      placeholder="邮箱或电话，方便我们回复您"
                    >
                  </div>
                  
                  <!-- 提交按钮 -->
                  <div class="form-actions">
                    <button 
                      type="submit" 
                      class="submit-button"
                      :disabled="isSubmitting"
                    >
                      <span v-if="isSubmitting">提交中...</span>
                      <span v-else>提交反馈</span>
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits } from 'vue'
import ResponsiveTest from '../test/ResponsiveTest.vue'
import { getFeedbackLogService } from '../../services/feedbackLogService.js'

// 组件属性
const props = defineProps({
  currentSession: {
    type: Object,
    default: null
  },
  messages: {
    type: Array,
    default: () => []
  }
})

// 组件事件
const emit = defineEmits(['send-message'])

// 响应式数据
const activeTab = ref('guide') // 当前激活的标签页
const expandedFaq = ref(null) // 展开的FAQ项
const isSubmitting = ref(false) // 提交状态
const showResponsiveTest = ref(false) // 响应式测试显示状态
const logStats = ref(null) // 日志统计信息
const isLoadingLogStats = ref(false) // 日志统计加载状态

// 反馈表单数据
const feedbackForm = ref({
  type: 'suggestion',
  content: '',
  contact: ''
})

// 标签页配置
const tabs = [
  { id: 'guide', name: '使用指南', icon: '📖' },
  { id: 'faq', name: '常见问题', icon: '❓' },
  { id: 'feedback', name: '意见反馈', icon: '💬' }
]

// 使用指南数据
const guideItems = [
  {
    id: 'knowledge',
    icon: '📚',
    title: '知识中心使用',
    description: '了解如何使用知识中心进行智能问答',
    steps: [
      '点击左侧"知识中心"进入问答界面',
      '在输入框中输入您的问题',
      '按Enter键发送，或按Ctrl+Enter换行',
      '查看AI助手的回答和建议'
    ]
  },
  {
    id: 'business',
    icon: '🏢',
    title: '业务域功能',
    description: '学习如何使用业务域查询特定业务信息',
    steps: [
      '点击左侧"业务域"进入业务查询',
      '在底部选择相应的业务库',
      '输入业务相关问题',
      '获取专业的业务解答'
    ]
  },
  {
    id: 'function',
    icon: '⚙️',
    title: '职能域服务',
    description: '掌握职能域各种功能服务的使用方法',
    steps: [
      '点击左侧"职能域"进入功能服务',
      '选择需要的职能功能类别',
      '点击具体功能选项',
      '按照提示完成相关操作'
    ]
  }
]

// 常见问题数据
const faqItems = [
  {
    id: 'q1',
    question: '如何创建新的会话？',
    answer: '点击右侧会话面板顶部的"新会话"按钮即可创建新的会话。每个会话都是独立的，可以进行不同主题的对话。'
  },
  {
    id: 'q2',
    question: '业务域和职能域有什么区别？',
    answer: '业务域主要针对特定业务领域的专业问答，需要选择相应的业务库；职能域则提供各种功能服务，如数据分析、文档处理等工具性功能。'
  },
  {
    id: 'q3',
    question: '如何切换不同的业务库？',
    answer: '在业务域页面，输入框下方会显示业务库选择器，点击相应的业务库按钮即可切换。选择后，您的问题将在该业务库范围内得到解答。'
  },
  {
    id: 'q4',
    question: '会话记录会保存多久？',
    answer: '会话记录会在本地浏览器中保存，除非您主动删除或清除浏览器数据。建议定期整理会话记录以保持界面整洁。'
  },
  {
    id: 'q5',
    question: '如何删除不需要的会话？',
    answer: '在会话列表中，将鼠标悬停在会话项上，会出现删除按钮。点击删除按钮并确认即可删除该会话。'
  }
]

// 反馈类型配置
const feedbackTypes = [
  { id: 'bug', name: '问题反馈' },
  { id: 'suggestion', name: '功能建议' },
  { id: 'improvement', name: '体验改进' },
  { id: 'other', name: '其他' }
]

/**
 * 切换标签页
 * @param {string} tabId - 标签页ID
 */
const switchTab = (tabId) => {
  activeTab.value = tabId
  console.log('切换到标签页:', tabId)
}

/**
 * 切换FAQ展开状态
 * @param {string} faqId - FAQ项ID
 */
const toggleFaq = (faqId) => {
  expandedFaq.value = expandedFaq.value === faqId ? null : faqId
}

// 开发者工具相关计算属性
const userAgent = computed(() => navigator.userAgent)
const viewportInfo = computed(() => `${window.innerWidth} × ${window.innerHeight}`)
const devicePixelRatio = computed(() => window.devicePixelRatio || 1)

/**
 * 测试主题切换
 */
const testThemeSwitch = () => {
  const root = document.documentElement
  const currentTheme = root.getAttribute('data-theme')
  const newTheme = currentTheme === 'moon' ? 'sun' : 'moon'

  root.setAttribute('data-theme', newTheme)
  root.classList.toggle('dark-theme')
  root.classList.toggle('light-theme')

  console.log(`主题切换测试: ${currentTheme} -> ${newTheme}`)
}

/**
 * 测试通知样式
 */
const testNotifications = () => {
  const notifications = [
    { message: '成功通知测试', type: 'success' },
    { message: '信息通知测试', type: 'info' },
    { message: '警告通知测试', type: 'warning' },
    { message: '错误通知测试', type: 'error' }
  ]

  notifications.forEach((notif, index) => {
    setTimeout(() => {
      // 这里应该调用全局通知方法，暂时用console.log代替
      console.log(`通知测试 [${notif.type}]: ${notif.message}`)

      // 创建临时通知元素进行测试
      const notification = document.createElement('div')
      notification.className = `notification notification-${notif.type}`
      notification.style.cssText = `
        position: fixed;
        top: ${20 + index * 60}px;
        right: 20px;
        z-index: 1000;
        padding: 12px 16px;
        border-radius: 8px;
        background: var(--bg-primary);
        border: 1px solid var(--border-primary);
        box-shadow: var(--shadow-md);
        max-width: 300px;
      `
      notification.textContent = notif.message

      document.body.appendChild(notification)

      // 3秒后移除
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification)
        }
      }, 3000)
    }, index * 500)
  })
}

/**
 * 提交反馈
 */
const submitFeedback = async () => {
  if (!feedbackForm.value.content.trim()) {
    alert('请填写反馈内容')
    return
  }

  isSubmitting.value = true

  try {
    // 获取反馈日志服务实例
    const feedbackLogService = getFeedbackLogService()

    // 记录反馈到服务器端 logs/feedback.log 文件
    console.log('正在将反馈记录到服务器端日志文件...')
    const logSuccess = await feedbackLogService.logFeedback({
      type: feedbackForm.value.type,
      content: feedbackForm.value.content.trim(),
      contact: feedbackForm.value.contact.trim()
    })

    if (logSuccess) {
      console.log('✅ 反馈已成功记录到日志文件')

      // 重置表单
      feedbackForm.value = {
        type: 'suggestion',
        content: '',
        contact: ''
      }

      alert('反馈提交成功！您的意见已记录到日志文件中，感谢您的宝贵建议。')
    } else {
      console.error('❌ 反馈记录失败')
      alert('反馈记录失败，但您的意见已保存到本地备份。我们会尽快处理技术问题。')
    }

  } catch (error) {
    console.error('❌ 提交反馈失败:', error)
    alert(`提交失败: ${error.message}，请稍后重试。`)
  } finally {
    isSubmitting.value = false
  }
}

/**
 * 加载日志统计信息
 */
const loadLogStats = async () => {
  isLoadingLogStats.value = true
  try {
    const feedbackLogService = getFeedbackLogService()
    logStats.value = await feedbackLogService.getLogStats()
    console.log('日志统计信息已加载:', logStats.value)
  } catch (error) {
    console.error('加载日志统计失败:', error)
    alert('加载日志统计失败')
  } finally {
    isLoadingLogStats.value = false
  }
}

/**
 * 下载日志文件
 */
const downloadLogs = async () => {
  try {
    const feedbackLogService = getFeedbackLogService()
    await feedbackLogService.downloadLogFile()
    console.log('日志文件下载完成')
  } catch (error) {
    console.error('下载日志文件失败:', error)
    alert('下载日志文件失败')
  }
}

/**
 * 清空日志
 */
const clearLogs = async () => {
  if (!confirm('确定要清空所有反馈日志吗？此操作不可恢复。')) {
    return
  }

  try {
    const feedbackLogService = getFeedbackLogService()
    const success = await feedbackLogService.clearAllLogs()
    if (success) {
      logStats.value = null
      alert('所有日志已清空')
    } else {
      alert('清空日志失败')
    }
  } catch (error) {
    console.error('清空日志失败:', error)
    alert('清空日志失败')
  }
}

/**
 * 格式化字节数
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的字符串
 */
const formatBytes = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 获取反馈类型名称
 * @param {string} type - 反馈类型
 * @returns {string} 类型名称
 */
const getTypeName = (type) => {
  const typeNames = {
    bug: '问题反馈',
    suggestion: '功能建议',
    improvement: '体验改进',
    other: '其他'
  }
  return typeNames[type] || type
}
</script>

<style scoped>
/* 帮助与反馈页面容器 */
.help-feedback {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
  overflow: hidden;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 32px 24px;
}

.header-content {
  max-width: 800px;
  margin: 0 auto;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 8px;
}

.page-description {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.content-container {
  max-width: 800px;
  margin: 0 auto;
}

/* 标签页导航 */
.tab-navigation {
  display: flex;
  gap: 4px;
  margin-bottom: 24px;
  background-color: var(--bg-secondary);
  padding: 4px;
  border-radius: 12px;
}

.tab-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.tab-button:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.tab-button.active {
  background-color: var(--bg-primary);
  color: var(--accent-primary);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-icon {
  font-size: 16px;
}

/* 标签页内容 */
.tab-content {
  background-color: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.content-section {
  padding: 24px;
}

/* 使用指南样式 */
.guide-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.guide-item {
  border: 1px solid var(--border-primary, #e5e7eb);
  border-radius: 8px;
  padding: 20px;
  background-color: var(--bg-secondary, #fafbfc);
}

.guide-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.guide-icon {
  font-size: 24px;
}

.guide-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary, #1f2937);
  margin: 0;
}

.guide-description {
  color: var(--text-secondary, #6b7280);
  margin-bottom: 16px;
  line-height: 1.6;
}

.guide-steps {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.step-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--accent-primary, #3b82f6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.step-text {
  color: var(--text-primary, #374151);
  line-height: 1.5;
}

/* 常见问题样式 */
.faq-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.faq-item {
  border: 1px solid var(--border-primary, #e5e7eb);
  border-radius: 8px;
  overflow: hidden;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background-color: var(--bg-secondary, #fafbfc);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.faq-question:hover {
  background-color: var(--bg-tertiary, #f3f4f6);
}

.faq-question.expanded {
  background-color: var(--accent-secondary, #eff6ff);
  color: var(--accent-primary, #3b82f6);
}

.question-text {
  font-weight: 500;
  flex: 1;
  color: var(--text-primary, #1f2937);
}

.expand-icon {
  font-size: 18px;
  font-weight: bold;
  transition: transform 0.2s ease;
  color: var(--text-secondary, #6b7280);
}

.faq-answer {
  padding: 0 20px;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  background-color: var(--bg-primary, #ffffff);
  color: var(--text-primary, #374151);
}

.faq-answer.show {
  padding: 16px 20px;
  max-height: 200px;
}

/* 意见反馈样式 */
.feedback-content {
  max-width: 600px;
  margin: 0 auto;
}

.feedback-form {
  background-color: var(--bg-secondary, #fafbfc);
  border-radius: 8px;
  padding: 24px;
}

.form-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary, #1f2937);
  margin-bottom: 8px;
}

.form-description {
  color: var(--text-secondary, #6b7280);
  margin-bottom: 24px;
  line-height: 1.6;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary, #374151);
  margin-bottom: 8px;
}

.radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}

.radio-input {
  margin: 0;
}

.radio-label {
  font-size: 14px;
  color: var(--text-primary, #374151);
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--border-secondary, #d1d5db);
  border-radius: 6px;
  font-size: 14px;
  background-color: var(--bg-primary, #ffffff);
  color: var(--text-primary, #1f2937);
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--accent-primary, #3b82f6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.form-actions {
  text-align: center;
}

.submit-button {
  padding: 12px 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 开发者工具样式 */
.dev-tools {
  max-width: 800px;
  margin: 0 auto;
}

.tool-section {
  background: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-primary);
}

.tool-section h3 {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.tool-section p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  line-height: 1.5;
}

.tool-button {
  background: var(--accent-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all 0.2s ease;
  margin-right: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.tool-button:hover {
  background: var(--accent-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.tool-button.danger {
  background: #dc3545;
}

.tool-button.danger:hover {
  background: #c82333;
}

.responsive-test-container {
  margin-top: var(--spacing-lg);
  border: 1px solid var(--border-primary);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
}

.theme-test-buttons {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.performance-info {
  background: var(--bg-primary);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  border: 1px solid var(--border-primary);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid var(--border-secondary);
}

.info-item:last-child {
  border-bottom: none;
}

.info-item span:first-child {
  font-weight: 500;
  color: var(--text-secondary);
}

.info-value {
  font-family: monospace;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  word-break: break-all;
  text-align: right;
  max-width: 60%;
}

/* 日志管理样式 */
.log-management {
  margin-top: var(--spacing-md);
}

.log-actions {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
  margin-bottom: var(--spacing-md);
}

.log-stats {
  background: var(--bg-primary);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  border: 1px solid var(--border-primary);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--bg-secondary);
  border-radius: var(--border-radius-sm);
}

.stat-label {
  font-weight: 500;
  color: var(--text-secondary);
}

.stat-value {
  font-family: monospace;
  font-weight: 600;
  color: var(--text-primary);
}

.type-stats h4 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-size: var(--font-size-base);
}

.type-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.type-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--bg-secondary);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-secondary);
}

.type-name {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.type-count {
  font-family: monospace;
  font-weight: 600;
  color: var(--accent-primary);
  background: var(--bg-primary);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: var(--font-size-xs);
}

/* 响应式适配 */
@media (max-width: 768px) {
  .dev-tools {
    padding: 0;
  }

  .tool-section {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
  }

  .theme-test-buttons {
    flex-direction: column;
  }

  .tool-button {
    margin-right: 0;
    width: 100%;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .info-value {
    max-width: 100%;
    text-align: left;
  }
}

@media (max-width: 480px) {
  .tool-section {
    padding: var(--spacing-sm);
  }

  .tool-section h3 {
    font-size: var(--font-size-base);
  }

  .tool-section p {
    font-size: var(--font-size-sm);
  }
}
</style>
