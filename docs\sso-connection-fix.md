# SSO连接问题修复指南

## 🚨 问题描述

错误信息：
```
[vite] http proxy error at /api/sso/validate:
Error: connect ECONNREFUSED ::1:4003
```

这个错误表明Vite代理无法连接到SSO代理服务器，通常是IPv6/IPv4地址解析问题。

## ✅ 已修复的问题

### 1. Vite代理配置修正

**修改前**：
```javascript
'/api/sso': {
  target: 'http://localhost:4003',  // 可能解析为IPv6 ::1
  // ...
}
```

**修改后**：
```javascript
'/api/sso': {
  target: 'http://127.0.0.1:4003',  // 强制使用IPv4
  // ...
}
```

### 2. SSO代理服务器绑定修正

**修改前**：
```javascript
app.listen(PORT, '0.0.0.0', () => {
  // 绑定到所有接口，但可能有IPv6问题
})
```

**修改后**：
```javascript
app.listen(PORT, () => {
  // 让Node.js自动选择最佳绑定方式
  console.log(`健康检查: http://localhost:${PORT}/health`)
  console.log(`健康检查: http://127.0.0.1:${PORT}/health`)
})
```

## 🔧 解决方案步骤

### 步骤1：重启SSO代理服务器
```bash
# 停止当前运行的SSO代理服务器（Ctrl+C）
# 然后重新启动
node sso-proxy-server.js
```

### 步骤2：验证服务器状态
确认看到以下输出：
```
========================================
    SSO代理服务器启动成功
========================================
监听端口: 4003
健康检查: http://localhost:4003/health
健康检查: http://127.0.0.1:4003/health
SSO验证接口: http://localhost:4003/api/sso/validate
SSO验证接口: http://127.0.0.1:4003/api/sso/validate
========================================
```

### 步骤3：重启前端开发服务器
```bash
# 停止当前的前端服务器（Ctrl+C）
# 然后重新启动
npm run dev
```

### 步骤4：测试连接
访问：`http://localhost:3000?tokenId=test-token`

## 🧪 连接测试方法

### 方法1：使用测试页面
1. 打开 `sso-test.html`
2. 点击"检查代理服务器状态"
3. 应该显示"✅ 代理服务器状态正常"

### 方法2：手动测试
```bash
# 测试健康检查接口
curl http://localhost:4003/health
curl http://127.0.0.1:4003/health

# 测试SSO验证接口
curl -X POST http://localhost:4003/api/sso/validate \
  -H "Content-Type: application/json" \
  -d '{"tokenId": "test", "access_key": "2e5295e9-f46d-45e0-8de0-8a1281a6da5c"}'
```

### 方法3：PowerShell测试
```powershell
# 健康检查
Invoke-WebRequest -Uri "http://localhost:4003/health" -UseBasicParsing

# SSO验证
$body = '{"tokenId": "test", "access_key": "2e5295e9-f46d-45e0-8de0-8a1281a6da5c"}'
Invoke-WebRequest -Uri "http://localhost:4003/api/sso/validate" -Method POST -Body $body -ContentType "application/json" -UseBasicParsing
```

## 🔍 故障排除

### 问题1：端口被占用
```bash
# 检查端口4003是否被占用
netstat -ano | findstr :4003

# 如果被占用，杀死进程或更换端口
```

### 问题2：防火墙阻止
- 检查Windows防火墙设置
- 确保允许Node.js访问网络
- 临时关闭防火墙测试

### 问题3：IPv6/IPv4冲突
- 使用127.0.0.1而不是localhost
- 检查系统hosts文件配置
- 禁用IPv6（如果不需要）

### 问题4：权限问题
- 以管理员身份运行命令提示符
- 检查Node.js执行权限

## 📋 验证清单

完成修复后，请验证以下项目：

- [ ] SSO代理服务器正常启动（端口4003）
- [ ] 健康检查接口可访问：`http://localhost:4003/health`
- [ ] 健康检查接口可访问：`http://127.0.0.1:4003/health`
- [ ] 前端开发服务器正常启动（端口3000）
- [ ] Vite代理不再报错
- [ ] 测试页面可以正常连接代理服务器
- [ ] SSO登录流程正常工作

## 🚀 一键修复脚本

创建了 `start-sso-dev.bat` 脚本，自动处理启动顺序：

```bash
start-sso-dev.bat
```

这个脚本会：
1. 检查Node.js环境
2. 安装依赖（如果需要）
3. 同时启动SSO代理服务器和前端服务器
4. 显示所有服务地址

## 💡 预防措施

为避免类似问题：

1. **使用IP地址**：在配置中优先使用127.0.0.1而不是localhost
2. **端口检查**：启动前检查端口是否可用
3. **日志监控**：观察服务器启动日志
4. **自动重启**：使用PM2等工具管理进程
5. **健康检查**：定期检查服务状态

## 📞 技术支持

如果问题仍然存在：

1. 检查Node.js版本（建议18+）
2. 清除npm缓存：`npm cache clean --force`
3. 重新安装依赖：`rm -rf node_modules && npm install`
4. 检查系统网络配置
5. 查看详细错误日志

现在SSO连接问题已经修复，可以正常使用SSO功能了！
