/**
 * 日志记录工具
 * 提供统一的日志记录功能，支持不同级别的日志和持久化存储
 */

/**
 * 日志级别枚举
 */
export const LogLevel = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  FATAL: 4
}

/**
 * 日志级别名称映射
 */
const LogLevelNames = {
  [LogLevel.DEBUG]: 'DEBUG',
  [LogLevel.INFO]: 'INFO',
  [LogLevel.WARN]: 'WARN',
  [LogLevel.ERROR]: 'ERROR',
  [LogLevel.FATAL]: 'FATAL'
}

/**
 * 日志记录器类
 */
class Logger {
  constructor(options = {}) {
    this.name = options.name || 'BodorAI'
    this.level = options.level || LogLevel.INFO
    this.maxLogs = options.maxLogs || 1000
    this.enableConsole = options.enableConsole !== false
    this.enableStorage = options.enableStorage !== false
    
    // 日志存储
    this.logs = []
    this.storageKey = `${this.name.toLowerCase()}_logs`
    
    // 加载已存储的日志
    if (this.enableStorage) {
      this.loadStoredLogs()
    }
  }

  /**
   * 记录日志
   */
  log(level, message, data = null, category = 'general') {
    if (level < this.level) return

    const logEntry = {
      id: Date.now() + Math.random(),
      timestamp: new Date(),
      level,
      levelName: LogLevelNames[level],
      message,
      data,
      category,
      stack: level >= LogLevel.ERROR ? this.getStackTrace() : null
    }

    // 添加到日志列表
    this.logs.push(logEntry)

    // 限制日志数量
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs)
    }

    // 输出到控制台
    if (this.enableConsole) {
      this.outputToConsole(logEntry)
    }

    // 存储到本地
    if (this.enableStorage) {
      this.saveToStorage()
    }

    return logEntry
  }

  /**
   * Debug级别日志
   */
  debug(message, data = null, category = 'general') {
    return this.log(LogLevel.DEBUG, message, data, category)
  }

  /**
   * Info级别日志
   */
  info(message, data = null, category = 'general') {
    return this.log(LogLevel.INFO, message, data, category)
  }

  /**
   * Warning级别日志
   */
  warn(message, data = null, category = 'general') {
    return this.log(LogLevel.WARN, message, data, category)
  }

  /**
   * Error级别日志
   */
  error(message, data = null, category = 'general') {
    return this.log(LogLevel.ERROR, message, data, category)
  }

  /**
   * Fatal级别日志
   */
  fatal(message, data = null, category = 'general') {
    return this.log(LogLevel.FATAL, message, data, category)
  }

  /**
   * Redis相关日志
   */
  redis(level, message, data = null) {
    return this.log(level, message, data, 'redis')
  }

  /**
   * 会话相关日志
   */
  session(level, message, data = null) {
    return this.log(level, message, data, 'session')
  }

  /**
   * 同步相关日志
   */
  sync(level, message, data = null) {
    return this.log(level, message, data, 'sync')
  }

  /**
   * 存储相关日志
   */
  storage(level, message, data = null) {
    return this.log(level, message, data, 'storage')
  }

  /**
   * 输出到控制台
   */
  outputToConsole(logEntry) {
    const { levelName, message, data, timestamp, category } = logEntry
    const timeStr = timestamp.toLocaleTimeString()
    const prefix = `[${timeStr}] [${this.name}] [${levelName}] [${category}]`

    switch (logEntry.level) {
      case LogLevel.DEBUG:
        console.debug(prefix, message, data || '')
        break
      case LogLevel.INFO:
        console.info(prefix, message, data || '')
        break
      case LogLevel.WARN:
        console.warn(prefix, message, data || '')
        break
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        console.error(prefix, message, data || '')
        if (logEntry.stack) {
          console.error('Stack trace:', logEntry.stack)
        }
        break
      default:
        console.log(prefix, message, data || '')
    }
  }

  /**
   * 获取堆栈跟踪
   */
  getStackTrace() {
    try {
      throw new Error()
    } catch (error) {
      return error.stack
    }
  }

  /**
   * 保存到本地存储
   */
  saveToStorage() {
    try {
      const logsToStore = this.logs.slice(-100) // 只存储最近100条日志
      const serializedLogs = logsToStore.map(log => ({
        ...log,
        timestamp: log.timestamp.toISOString()
      }))
      
      localStorage.setItem(this.storageKey, JSON.stringify(serializedLogs))
    } catch (error) {
      console.error('保存日志到本地存储失败:', error)
    }
  }

  /**
   * 从本地存储加载日志
   */
  loadStoredLogs() {
    try {
      const stored = localStorage.getItem(this.storageKey)
      if (stored) {
        const logs = JSON.parse(stored)
        this.logs = logs.map(log => ({
          ...log,
          timestamp: new Date(log.timestamp)
        }))
      }
    } catch (error) {
      console.error('从本地存储加载日志失败:', error)
    }
  }

  /**
   * 获取日志
   */
  getLogs(options = {}) {
    const {
      level = null,
      category = null,
      startTime = null,
      endTime = null,
      limit = null
    } = options

    let filteredLogs = [...this.logs]

    // 按级别过滤
    if (level !== null) {
      filteredLogs = filteredLogs.filter(log => log.level >= level)
    }

    // 按分类过滤
    if (category) {
      filteredLogs = filteredLogs.filter(log => log.category === category)
    }

    // 按时间范围过滤
    if (startTime) {
      filteredLogs = filteredLogs.filter(log => log.timestamp >= startTime)
    }
    if (endTime) {
      filteredLogs = filteredLogs.filter(log => log.timestamp <= endTime)
    }

    // 限制数量
    if (limit) {
      filteredLogs = filteredLogs.slice(-limit)
    }

    return filteredLogs
  }

  /**
   * 清除日志
   */
  clearLogs() {
    this.logs = []
    if (this.enableStorage) {
      localStorage.removeItem(this.storageKey)
    }
  }

  /**
   * 导出日志
   */
  exportLogs(format = 'json') {
    const logs = this.getLogs()
    
    if (format === 'json') {
      return JSON.stringify(logs, null, 2)
    } else if (format === 'csv') {
      const headers = ['timestamp', 'level', 'category', 'message', 'data']
      const csvRows = [headers.join(',')]
      
      logs.forEach(log => {
        const row = [
          log.timestamp.toISOString(),
          log.levelName,
          log.category,
          `"${log.message.replace(/"/g, '""')}"`,
          log.data ? `"${JSON.stringify(log.data).replace(/"/g, '""')}"` : ''
        ]
        csvRows.push(row.join(','))
      })
      
      return csvRows.join('\n')
    } else if (format === 'text') {
      return logs.map(log => {
        const timeStr = log.timestamp.toLocaleString()
        const dataStr = log.data ? ` | Data: ${JSON.stringify(log.data)}` : ''
        return `[${timeStr}] [${log.levelName}] [${log.category}] ${log.message}${dataStr}`
      }).join('\n')
    }
    
    return logs
  }

  /**
   * 下载日志文件
   */
  downloadLogs(format = 'json', filename = null) {
    const content = this.exportLogs(format)
    const timestamp = new Date().toISOString().split('T')[0]
    const defaultFilename = `${this.name.toLowerCase()}_logs_${timestamp}.${format}`
    
    const blob = new Blob([content], { 
      type: format === 'json' ? 'application/json' : 'text/plain' 
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename || defaultFilename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  /**
   * 获取日志统计信息
   */
  getLogStats() {
    const stats = {
      total: this.logs.length,
      byLevel: {},
      byCategory: {},
      timeRange: {
        start: null,
        end: null
      }
    }

    // 统计各级别日志数量
    Object.values(LogLevel).forEach(level => {
      stats.byLevel[LogLevelNames[level]] = this.logs.filter(log => log.level === level).length
    })

    // 统计各分类日志数量
    this.logs.forEach(log => {
      stats.byCategory[log.category] = (stats.byCategory[log.category] || 0) + 1
    })

    // 时间范围
    if (this.logs.length > 0) {
      stats.timeRange.start = this.logs[0].timestamp
      stats.timeRange.end = this.logs[this.logs.length - 1].timestamp
    }

    return stats
  }
}

// 创建默认日志记录器实例
const defaultLogger = new Logger({
  name: 'BodorAI',
  level: process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.INFO
})

// 导出默认实例和类
export { Logger, defaultLogger }
export default defaultLogger
