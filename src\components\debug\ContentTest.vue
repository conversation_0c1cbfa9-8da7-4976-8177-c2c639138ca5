<template>
  <div class="content-test">
    <h2>内容显示测试</h2>
    
    <div class="test-section">
      <h3>测试用例</h3>
      <button @click="testNormalContent">测试正常内容</button>
      <button @click="testObjectContent">测试对象内容</button>
      <button @click="testCodeWithObject">测试代码块对象</button>
      <button @click="testMixedContent">测试混合内容</button>
      <button @click="clearContent">清空内容</button>
    </div>
    
    <div class="test-results">
      <h3>渲染结果</h3>
      <div class="result-container">
        <MarkdownRenderer :content="testContent" />
      </div>
    </div>
    
    <div class="debug-info">
      <h3>调试信息</h3>
      <pre>{{ debugInfo }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import MarkdownRenderer from '../common/MarkdownRenderer.vue'

const testContent = ref('')

const debugInfo = computed(() => {
  if (!testContent.value) return '无内容'

  return {
    原始内容类型: typeof testContent.value,
    原始内容: testContent.value,
    是否为对象: typeof testContent.value === 'object',
    内容长度: String(testContent.value).length
  }
})

const testNormalContent = () => {
  testContent.value = `# 正常内容测试

这是一个正常的Markdown内容测试。

## 代码示例

\`\`\`javascript
function hello() {
  console.log("Hello World!");
}
\`\`\`

行内代码：\`console.log("test")\`

**粗体文本** 和 *斜体文本*`
}

const testObjectContent = () => {
  // 模拟对象内容
  testContent.value = {
    type: 'response',
    content: 'This is object content',
    code: {
      language: 'javascript',
      value: 'console.log("test")'
    }
  }
}

const testCodeWithObject = () => {
  testContent.value = `# 代码块对象测试

这里有一个包含对象的代码块：

\`\`\`javascript
[object Object]
\`\`\`

还有行内代码：\`[object Object]\`

正常代码：
\`\`\`python
print("Hello World")
\`\`\``
}

const testMixedContent = () => {
  testContent.value = `# 混合内容测试

正常文本内容。

## 表格测试
| 列1 | 列2 |
|-----|-----|
| 数据1 | [object Object] |
| 数据2 | 正常数据 |

## 列表测试
- 正常项目
- [object Object]
- 另一个正常项目

\`\`\`json
{
  "normal": "value",
  "problematic": "[object Object]"
}
\`\`\``
}

const clearContent = () => {
  testContent.value = ''
}
</script>

<style scoped>
.content-test {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.test-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #f8fafc;
}

.test-section button {
  margin-right: 10px;
  margin-bottom: 10px;
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.test-section button:hover {
  background: #2563eb;
}

.test-results {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.result-container {
  min-height: 100px;
  padding: 15px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 4px;
}

.debug-info {
  padding: 15px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #f9fafb;
}

.debug-info pre {
  background: #1f2937;
  color: #f9fafb;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  white-space: pre-wrap;
}
</style>
