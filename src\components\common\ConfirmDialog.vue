<template>
  <div v-if="visible" class="confirm-overlay" @click="handleOverlayClick">
    <div class="confirm-dialog" @click.stop>
      <div class="dialog-header">
        <h3 class="dialog-title">{{ title }}</h3>
      </div>
      
      <div class="dialog-content">
        <p class="dialog-message" v-html="message"></p>
      </div>
      
      <div class="dialog-actions">
        <button 
          class="btn btn-cancel" 
          @click="handleCancel"
          :disabled="loading"
        >
          {{ cancelText }}
        </button>
        <button 
          class="btn btn-confirm" 
          @click="handleConfirm"
          :disabled="loading"
          :class="{ 'loading': loading }"
        >
          <span v-if="loading" class="loading-spinner"></span>
          {{ confirmText }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
// defineProps 和 defineEmits 在 Vue 3 中是编译器宏，不需要导入

// 组件属性
const props = defineProps({
  // 是否显示对话框
  visible: {
    type: Boolean,
    default: false
  },
  // 对话框标题
  title: {
    type: String,
    default: '确认操作'
  },
  // 对话框消息内容
  message: {
    type: String,
    default: '确定要执行此操作吗？'
  },
  // 确认按钮文本
  confirmText: {
    type: String,
    default: '确认'
  },
  // 取消按钮文本
  cancelText: {
    type: String,
    default: '取消'
  },
  // 是否正在加载
  loading: {
    type: Boolean,
    default: false
  },
  // 点击遮罩是否关闭
  closeOnOverlay: {
    type: Boolean,
    default: true
  }
})

// 组件事件
const emit = defineEmits(['confirm', 'cancel', 'close'])

/**
 * 处理确认
 */
const handleConfirm = () => {
  emit('confirm')
}

/**
 * 处理取消
 */
const handleCancel = () => {
  emit('cancel')
}

/**
 * 处理遮罩点击
 */
const handleOverlayClick = () => {
  if (props.closeOnOverlay && !props.loading) {
    emit('close')
  }
}
</script>

<style scoped>
/* 确认对话框遮罩 */
.confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000; /* 确保在最上层 */
  backdrop-filter: blur(2px);
}

/* 确认对话框 */
.confirm-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 400px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  animation: dialogSlideIn 0.2s ease-out;
}

@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 对话框头部 */
.dialog-header {
  padding: 20px 20px 0 20px;
}

.dialog-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.4;
}

/* 对话框内容 */
.dialog-content {
  padding: 16px 20px 20px 20px;
}

.dialog-message {
  margin: 0;
  color: #6b7280;
  line-height: 1.6;
  white-space: pre-line;
}

/* 对话框操作按钮 */
.dialog-actions {
  padding: 0 20px 20px 20px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.btn {
  padding: 10px 20px;
  border-radius: 8px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 80px;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 取消按钮 */
.btn-cancel {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-cancel:hover:not(:disabled) {
  background-color: #e5e7eb;
  border-color: #9ca3af;
}

/* 确认按钮 */
.btn-confirm {
  background-color: #ef4444;
  color: white;
}

.btn-confirm:hover:not(:disabled) {
  background-color: #dc2626;
}

.btn-confirm.loading {
  background-color: #f87171;
}

/* 加载动画 */
.loading-spinner {
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .confirm-dialog {
    margin: 20px;
    width: calc(100% - 40px);
  }
  
  .dialog-actions {
    flex-direction: column-reverse;
  }
  
  .btn {
    width: 100%;
  }
}
</style>
