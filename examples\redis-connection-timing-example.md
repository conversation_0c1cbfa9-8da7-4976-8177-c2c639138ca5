# Redis连接时间点功能使用示例

## 功能概述

本示例展示如何使用和测试Redis连接时间点功能。该功能确保用户登录完成时自动建立Redis连接。

## 使用步骤

### 1. 启动应用

首先确保Redis代理服务器正在运行：

```bash
# 启动Redis代理服务器
cd proxy-server
node server.js
```

然后启动前端应用：

```bash
# 启动前端应用
npm run dev
# 或
yarn dev
```

### 2. 测试登录流程

1. **打开应用** - 访问 `http://localhost:3000`（或您的开发服务器地址）

2. **观察强制登录** - 应用会自动显示登录对话框（因为设置了 `force-login="true"`）

3. **填写登录信息**：
   - 用户名：任意用户名（如：张三）
   - 工号：任意工号（如：EMP001）

4. **点击登录** - 观察控制台日志

### 3. 观察Redis连接过程

登录成功后，在浏览器控制台中您应该看到类似以下的日志：

```
用户登录成功，开始初始化Redis连接和会话数据: {username: "张三", employeeId: "EMP001", ...}
🔗 用户登录完成，正在建立Redis连接...
🚀 开始用户登录后的Redis连接初始化...
初始化存储服务...
✅ Redis存储服务初始化成功
✅ 用户登录后Redis连接成功，进行登录后配置...
🔧 执行用户登录后的Redis配置操作...
✅ 用户登录记录已保存到Redis
✅ 用户登录后Redis操作完成
开始加载会话数据...
```

### 4. 使用测试工具

在浏览器控制台中，您可以使用以下测试函数：

#### 4.1 测试登录后Redis连接

```javascript
// 手动测试登录后Redis连接
window.testRedisConnectionAfterLogin()
```

#### 4.2 测试Redis连接时间点

```javascript
// 测试Redis连接时间点功能
window.testRedisConnectionTiming()
```

#### 4.3 模拟登录流程

```javascript
// 模拟完整的登录流程
window.simulateLoginFlow()
```

#### 4.4 调试会话状态

```javascript
// 查看当前会话状态
window.debugSessionState()
```

### 5. 验证Redis数据

您可以通过以下方式验证Redis中的数据：

#### 5.1 使用Redis CLI

```bash
# 连接到Redis服务器
redis-cli -h ************* -p 6379 -a X4gN7

# 查看所有键
KEYS *

# 查看用户登录记录
KEYS user:login:*

# 查看具体的登录记录
GET user:login:张三_EMP001
```

#### 5.2 使用代理服务器API

```bash
# 获取用户登录记录
curl "http://localhost:4001/redis/get/user%3Alogin%3A张三_EMP001"

# 查看所有用户相关的键
curl "http://localhost:4001/redis/keys/user%3A*"
```

### 6. 测试场景

#### 6.1 正常登录流程

1. 用户首次访问应用
2. 显示登录对话框
3. 用户填写信息并登录
4. 系统建立Redis连接
5. 加载用户会话数据

#### 6.2 已登录用户刷新页面

1. 用户已经登录
2. 刷新页面
3. 系统检测到已登录状态
4. 自动建立Redis连接
5. 加载用户会话数据

#### 6.3 Redis连接失败场景

1. 停止Redis代理服务器
2. 用户尝试登录
3. 系统尝试连接Redis失败
4. 自动回退到LocalStorage
5. 显示警告通知

### 7. 预期结果

#### 7.1 成功场景

- 控制台显示连接成功日志
- 用户看到"Redis连接已建立"通知
- 存储状态指示器显示Redis连接状态
- 用户会话数据保存到Redis

#### 7.2 失败场景

- 控制台显示连接失败日志
- 用户看到"Redis连接失败，使用本地存储"通知
- 存储状态指示器显示LocalStorage状态
- 用户会话数据保存到LocalStorage

### 8. 故障排除

#### 8.1 Redis连接失败

**问题**：Redis连接失败
**解决方案**：
1. 检查Redis代理服务器是否运行
2. 检查Redis服务器是否可访问
3. 验证Redis配置信息
4. 查看网络连接

#### 8.2 登录后没有触发Redis连接

**问题**：登录成功但没有建立Redis连接
**解决方案**：
1. 检查控制台是否有错误日志
2. 验证用户服务是否正常工作
3. 检查存储服务初始化过程

#### 8.3 测试函数不可用

**问题**：控制台中测试函数未定义
**解决方案**：
1. 确保应用已完全加载
2. 检查是否在开发环境中
3. 刷新页面重新加载

### 9. 性能考虑

- Redis连接是异步的，不会阻塞用户界面
- 连接失败时有快速回退机制
- 用户登录记录有合理的过期时间（24小时）
- 测试函数仅在开发环境中暴露

### 10. 安全注意事项

- Redis密码通过配置文件管理
- 用户登录记录包含最少必要信息
- 登录记录自动过期，避免数据积累
- 生产环境中应移除调试函数

## 总结

通过以上步骤，您可以完整地测试和验证Redis连接时间点功能。该功能确保用户登录后能够及时建立Redis连接，为后续的数据存储和同步提供可靠支持。
