# AI历史对话轮数配置指南

## 概述

本文档详细说明了如何配置AI对话中携带的历史对话轮数，包括配置选项、使用场景和最佳实践。

## 配置选项

### historyTurns 参数说明

在AI配置文件中，`historyTurns` 参数控制发送给AI的历史对话轮数：

- **0**: 不携带历史对话，只发送当前用户消息
- **1**: 携带1轮历史对话（1条用户消息 + 1条AI回复）
- **2**: 携带2轮历史对话
- **3**: 携带3轮历史对话
- **...**: 以此类推
- **-1**: 携带全部历史对话

### 配置位置

#### 1. AI配置文件 (`src/config/ai.js`)

```javascript
// 知识中心AI配置
export const knowledgeAIConfig = {
  // 其他配置...
  historyTurns: 3, // 携带3轮历史对话
}

// 业务域AI配置
export const businessAIConfig = {
  // 其他配置...
  historyTurns: 5, // 携带5轮历史对话（业务场景需要更多上下文）
}

// 职能域AI配置
export const functionAIConfig = {
  // 其他配置...
  historyTurns: 2, // 携带2轮历史对话（功能性对话通常不需要太多历史）
}
```

#### 2. 环境变量配置

在 `.env.development` 或 `.env.production` 文件中：

```bash
# 全局默认历史轮数（可被具体模块配置覆盖）
VITE_AI_HISTORY_TURNS=5
```

## 工作原理

### 对话轮次定义

一轮对话包含：
1. 一条用户消息（user message）
2. 一条AI回复（assistant message）

### 消息过滤逻辑

系统会按照以下逻辑过滤历史消息：

1. **historyTurns = 0**: 只保留最后一条用户消息
2. **historyTurns = -1**: 保留所有历史消息
3. **historyTurns = N**: 保留最后N轮完整对话

### 示例场景

假设当前会话有以下消息：

```
用户: 什么是人工智能？
AI: 人工智能是...
用户: AI有哪些应用？
AI: AI的应用包括...
用户: 请详细说明机器学习
AI: 机器学习是...
用户: 深度学习和机器学习的区别？ [当前消息]
```

不同配置的效果：

#### historyTurns = 0
```
系统: [系统提示词]
用户: 深度学习和机器学习的区别？
```

#### historyTurns = 1
```
系统: [系统提示词]
用户: 请详细说明机器学习
AI: 机器学习是...
用户: 深度学习和机器学习的区别？
```

#### historyTurns = 2
```
系统: [系统提示词]
用户: AI有哪些应用？
AI: AI的应用包括...
用户: 请详细说明机器学习
AI: 机器学习是...
用户: 深度学习和机器学习的区别？
```

#### historyTurns = -1
```
系统: [系统提示词]
用户: 什么是人工智能？
AI: 人工智能是...
用户: AI有哪些应用？
AI: AI的应用包括...
用户: 请详细说明机器学习
AI: 机器学习是...
用户: 深度学习和机器学习的区别？
```

## 使用场景建议

### 知识中心 (historyTurns = 3)
- **特点**: 知识问答通常需要一定的上下文
- **原因**: 用户可能会基于前面的回答继续提问
- **建议**: 3-5轮历史对话比较合适

### 业务域 (historyTurns = 5)
- **特点**: 业务咨询需要更多上下文信息
- **原因**: 业务问题通常比较复杂，需要多轮交互
- **建议**: 5-8轮历史对话，确保AI理解完整的业务场景

### 职能域 (historyTurns = 2)
- **特点**: 功能性操作通常相对独立
- **原因**: 工具使用指导不需要太多历史上下文
- **建议**: 1-3轮历史对话即可

## 性能考虑

### Token消耗

历史对话轮数直接影响发送给AI的token数量：

- **更多历史**: 更好的上下文理解，但消耗更多token
- **更少历史**: 节省token成本，但可能缺少上下文

### 响应速度

- **更多历史**: 处理时间可能更长
- **更少历史**: 响应更快

### 成本优化

根据不同场景合理配置历史轮数：

```javascript
// 成本敏感场景
historyTurns: 1

// 平衡场景
historyTurns: 3

// 质量优先场景
historyTurns: 5

// 特殊需求（如长对话分析）
historyTurns: -1
```

## 配置最佳实践

### 1. 分模块配置

不同模块使用不同的历史轮数配置：

```javascript
const moduleConfigs = {
  knowledge: { historyTurns: 3 },    // 知识问答
  business: { historyTurns: 5 },     // 业务咨询
  function: { historyTurns: 2 },     // 功能操作
  chat: { historyTurns: 8 },         // 闲聊对话
  analysis: { historyTurns: -1 }     // 数据分析
}
```

### 2. 环境区分

开发和生产环境使用不同配置：

```bash
# 开发环境 - 可以使用更多历史进行测试
VITE_AI_HISTORY_TURNS=5

# 生产环境 - 平衡性能和效果
VITE_AI_HISTORY_TURNS=3
```

### 3. 动态调整

根据用户反馈和使用情况动态调整：

```javascript
// 可以根据用户设置或会话类型动态调整
const getDynamicHistoryTurns = (sessionType, userPreference) => {
  if (userPreference) return userPreference
  
  const defaults = {
    'quick-question': 1,
    'detailed-consultation': 5,
    'troubleshooting': 3
  }
  
  return defaults[sessionType] || 3
}
```

## 调试和监控

### 日志输出

系统会输出历史对话过滤的详细信息：

```javascript
console.log(`[${this.module}] 历史对话配置:`, {
  totalMessages: messages.length,
  historyTurns: this.config.historyTurns,
  filteredMessages: filteredMessages.length,
  actualTurns: Math.floor(filteredMessages.length / 2)
})
```

### 监控指标

建议监控以下指标：

- 平均历史消息数量
- Token消耗统计
- 响应时间分布
- 用户满意度

## 故障排除

### 常见问题

1. **历史对话没有生效**
   - 检查配置文件语法
   - 确认环境变量设置
   - 查看控制台日志

2. **Token消耗过高**
   - 减少historyTurns数值
   - 检查是否有异常长的对话

3. **上下文理解不佳**
   - 适当增加historyTurns
   - 检查系统提示词设置

### 调试步骤

1. 查看控制台日志中的历史对话配置信息
2. 检查发送给AI的实际消息数量
3. 验证过滤逻辑是否正确工作
4. 测试不同配置值的效果

## 总结

通过合理配置历史对话轮数，可以在保证AI回复质量的同时优化性能和成本。建议：

1. **根据业务场景选择合适的轮数**
2. **定期监控和调整配置**
3. **平衡上下文质量和资源消耗**
4. **为不同模块设置差异化配置**

这样可以为用户提供更好的AI对话体验，同时控制系统资源消耗。
