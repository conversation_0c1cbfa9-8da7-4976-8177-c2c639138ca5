/**
 * 主题强制更新工具
 * 解决某些浏览器在主题切换时CSS变量不更新的问题
 */

/**
 * 强制应用主题样式
 * @param {boolean} isDark - 是否为深色主题
 */
export function forceApplyTheme(isDark) {
  const root = document.documentElement
  
  // 设置主题属性
  if (isDark) {
    root.setAttribute('data-theme', 'moon')
    root.classList.add('dark-theme')
    root.classList.remove('light-theme')
  } else {
    root.setAttribute('data-theme', 'sun')
    root.classList.add('light-theme')
    root.classList.remove('dark-theme')
  }
  
  // 强制更新CSS变量
  forceUpdateCSSVariables(isDark)
  
  // 强制更新AI消息样式
  forceUpdateAIMessages(isDark)
  
  // 延迟再次更新，确保所有元素都被正确处理
  setTimeout(() => {
    forceUpdateAIMessages(isDark)
  }, 100)
}

/**
 * 强制更新CSS变量
 * @param {boolean} isDark - 是否为深色主题
 */
function forceUpdateCSSVariables(isDark) {
  const root = document.documentElement
  
  if (isDark) {
    // 深色主题变量
    root.style.setProperty('--bg-primary', '#111827')
    root.style.setProperty('--bg-secondary', '#1f2937')
    root.style.setProperty('--bg-tertiary', '#374151')
    root.style.setProperty('--text-primary', '#f9fafb')
    root.style.setProperty('--text-secondary', '#d1d5db')
    root.style.setProperty('--text-tertiary', '#9ca3af')
    root.style.setProperty('--border-primary', '#374151')
    root.style.setProperty('--border-secondary', '#4b5563')
    root.style.setProperty('--accent-primary', '#60a5fa')
    root.style.setProperty('--accent-secondary', '#1e3a8a')
  } else {
    // 浅色主题变量
    root.style.setProperty('--bg-primary', '#ffffff')
    root.style.setProperty('--bg-secondary', '#f8fafc')
    root.style.setProperty('--bg-tertiary', '#f1f5f9')
    root.style.setProperty('--text-primary', '#1f2937')
    root.style.setProperty('--text-secondary', '#6b7280')
    root.style.setProperty('--text-tertiary', '#9ca3af')
    root.style.setProperty('--border-primary', '#e5e7eb')
    root.style.setProperty('--border-secondary', '#f3f4f6')
    root.style.setProperty('--accent-primary', '#3b82f6')
    root.style.setProperty('--accent-secondary', '#eff6ff')
  }
}

/**
 * 强制更新AI消息样式
 * @param {boolean} isDark - 是否为深色主题
 */
function forceUpdateAIMessages(isDark) {
  // AI消息容器
  const aiContainers = document.querySelectorAll('.ai-message-container')
  aiContainers.forEach(container => {
    if (isDark) {
      container.style.color = '#f9fafb'
    } else {
      container.style.color = '#1f2937'
    }
  })
  
  // Markdown渲染器
  const markdownRenderers = document.querySelectorAll('.markdown-renderer')
  markdownRenderers.forEach(renderer => {
    if (isDark) {
      renderer.style.color = '#f9fafb'
    } else {
      renderer.style.color = '#1f2937'
    }
  })
  
  // 消息气泡
  const messageBubbles = document.querySelectorAll('.message-bubble')
  messageBubbles.forEach(bubble => {
    if (isDark) {
      bubble.style.backgroundColor = '#1f2937'
      bubble.style.borderColor = '#374151'
      bubble.style.color = '#f9fafb'
    } else {
      bubble.style.backgroundColor = '#f8fafc'
      bubble.style.borderColor = '#e2e8f0'
      bubble.style.color = '#1f2937'
    }
  })
  
  // Markdown内容元素
  const markdownElements = document.querySelectorAll(`
    .markdown-renderer p,
    .markdown-renderer h1,
    .markdown-renderer h2,
    .markdown-renderer h3,
    .markdown-renderer h4,
    .markdown-renderer h5,
    .markdown-renderer h6,
    .markdown-renderer li,
    .markdown-renderer strong,
    .markdown-renderer em,
    .markdown-renderer span,
    .markdown-renderer div
  `)
  
  markdownElements.forEach(element => {
    if (isDark) {
      element.style.color = '#f9fafb'
    } else {
      element.style.color = '#1f2937'
    }
  })
  
  // 代码块
  const codeElements = document.querySelectorAll('.markdown-renderer code, .markdown-renderer pre')
  codeElements.forEach(element => {
    if (isDark) {
      element.style.backgroundColor = '#374151'
      element.style.color = '#f9fafb'
    } else {
      element.style.backgroundColor = '#f3f4f6'
      element.style.color = '#1f2937'
    }
  })
  
  // 引用块
  const blockquotes = document.querySelectorAll('.markdown-renderer blockquote')
  blockquotes.forEach(blockquote => {
    if (isDark) {
      blockquote.style.backgroundColor = '#1f2937'
      blockquote.style.color = '#d1d5db'
      blockquote.style.borderLeftColor = '#60a5fa'
    } else {
      blockquote.style.backgroundColor = '#f8fafc'
      blockquote.style.color = '#6b7280'
      blockquote.style.borderLeftColor = '#3b82f6'
    }
  })

  // 帮助与反馈页面元素
  const helpFeedbackTextElements = document.querySelectorAll(`
    .help-feedback .guide-title,
    .help-feedback .guide-description,
    .help-feedback .step-text,
    .help-feedback .question-text,
    .help-feedback .form-title,
    .help-feedback .form-description,
    .help-feedback .form-label,
    .help-feedback .radio-label,
    .help-feedback .faq-answer
  `)

  helpFeedbackTextElements.forEach(element => {
    if (isDark) {
      element.style.color = '#f9fafb'
    } else {
      element.style.color = '#1f2937'
    }
  })

  // 帮助与反馈页面背景元素
  const helpFeedbackBgElements = document.querySelectorAll(`
    .help-feedback .guide-item,
    .help-feedback .feedback-form,
    .help-feedback .faq-question,
    .help-feedback .faq-answer
  `)

  helpFeedbackBgElements.forEach(element => {
    if (isDark) {
      element.style.backgroundColor = '#1f2937'
      element.style.borderColor = '#374151'
    } else {
      element.style.backgroundColor = '#f8fafc'
      element.style.borderColor = '#e5e7eb'
    }
  })

  // 帮助与反馈页面表单输入元素
  const formInputElements = document.querySelectorAll(`
    .help-feedback .form-input,
    .help-feedback .form-textarea
  `)

  formInputElements.forEach(element => {
    if (isDark) {
      element.style.backgroundColor = '#374151'
      element.style.color = '#f9fafb'
      element.style.borderColor = '#4b5563'
    } else {
      element.style.backgroundColor = '#ffffff'
      element.style.color = '#1f2937'
      element.style.borderColor = '#d1d5db'
    }
  })
}

/**
 * 监听主题变化并强制更新
 */
export function setupThemeWatcher() {
  // 监听data-theme属性变化
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
        const isDark = document.documentElement.getAttribute('data-theme') === 'moon'
        setTimeout(() => {
          forceUpdateAIMessages(isDark)
        }, 50)
      }
    })
  })
  
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['data-theme']
  })
  
  // 监听类名变化
  const classObserver = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
        const isDark = document.documentElement.classList.contains('dark-theme')
        setTimeout(() => {
          forceUpdateAIMessages(isDark)
        }, 50)
      }
    })
  })
  
  classObserver.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class']
  })
}

/**
 * 检查并修复主题不一致问题
 */
export function checkAndFixTheme() {
  const root = document.documentElement
  const dataTheme = root.getAttribute('data-theme')
  const hasLightClass = root.classList.contains('light-theme')
  const hasDarkClass = root.classList.contains('dark-theme')
  
  let shouldBeDark = false
  
  // 确定应该是什么主题
  if (dataTheme === 'moon' || hasDarkClass) {
    shouldBeDark = true
  } else if (dataTheme === 'sun' || hasLightClass) {
    shouldBeDark = false
  } else {
    // 如果没有明确的主题设置，检查localStorage
    const saved = localStorage.getItem('theme-preference')
    shouldBeDark = saved === 'moon'
  }
  
  // 强制应用正确的主题
  forceApplyTheme(shouldBeDark)
  
  console.log(`主题检查完成，当前主题: ${shouldBeDark ? 'Moon' : 'Sun'}`)
}

// 导出给全局使用
if (typeof window !== 'undefined') {
  window.forceApplyTheme = forceApplyTheme
  window.checkAndFixTheme = checkAndFixTheme
  window.setupThemeWatcher = setupThemeWatcher
}
