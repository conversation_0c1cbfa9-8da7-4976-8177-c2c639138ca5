<template>
  <div class="markdown-renderer" v-html="renderedContent"></div>
</template>

<script setup>
import { computed, defineProps } from 'vue'
import { marked } from 'marked'
import hljs from 'highlight.js'

// 组件属性
const props = defineProps({
  // Markdown内容
  content: {
    type: String,
    default: ''
  },
  // 是否启用代码高亮
  enableHighlight: {
    type: Boolean,
    default: true
  }
})

// 重置marked配置为默认设置
marked.setOptions({
  gfm: true, // 启用GitHub风格Markdown
  breaks: true, // 支持换行符转换
  pedantic: false,
  sanitize: false, // 不清理HTML（我们信任AI输出）
  smartLists: true,
  smartypants: true
})

// 如果需要代码高亮，使用marked的highlight选项
if (props.enableHighlight) {
  marked.setOptions({
    highlight: function(code, lang) {
      if (lang && hljs.getLanguage(lang)) {
        try {
          return hljs.highlight(code, { language: lang }).value
        } catch (err) {
          console.warn('代码高亮失败:', err)
        }
      }
      return code
    }
  })
}

// 渲染Markdown内容
const renderedContent = computed(() => {
  // 首先进行基础的内容检查和转换
  let content = props.content

  // 处理null和undefined
  if (content === null || content === undefined) {
    return ''
  }

  // 如果是对象，先检查是否是marked的token对象
  if (typeof content === 'object') {
    console.error('MarkdownRenderer接收到对象类型内容:', content)

    // 检查是否是marked的token对象
    if (content.type && content.raw) {
      console.error('检测到marked token对象，使用raw内容:', content.raw)
      content = content.raw
    } else if (content.text) {
      console.error('检测到包含text属性的对象，使用text内容:', content.text)
      content = content.text
    } else {
      // 其他对象类型，转换为JSON
      try {
        content = JSON.stringify(content, null, 2)
        console.warn('对象已转换为JSON字符串')
      } catch (error) {
        console.error('对象转换失败:', error)
        return '<div style="color: red; padding: 12px; border: 1px solid #ef4444; border-radius: 4px; background: #fef2f2;">内容格式错误：无法显示对象内容</div>'
      }
    }
  }

  // 确保是字符串
  content = String(content).trim()

  // 如果内容为空
  if (!content) {
    return ''
  }

  // 简单的对象字符串修复
  if (content.includes('[object Object]')) {
    console.warn('发现对象字符串，进行修复:', content.substring(0, 100))
    content = content.replace(/\[object Object\]/g, '[对象内容]')
  }

  try {
    // 直接使用marked进行渲染，不使用自定义渲染器
    const result = marked.parse(content)

    // 检查结果是否是字符串
    if (typeof result !== 'string') {
      console.error('marked返回了非字符串结果:', typeof result, result)
      return `<pre style="background: #f5f5f5; padding: 12px; border-radius: 4px; white-space: pre-wrap;">${content}</pre>`
    }

    return result
  } catch (error) {
    console.error('Markdown渲染失败:', error)
    // 返回转义后的原始内容
    const escapedContent = content
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;')
    return `<pre style="background: #f5f5f5; padding: 12px; border-radius: 4px; white-space: pre-wrap; border: 1px solid #e2e8f0;">${escapedContent}</pre>`
  }
})
</script>

<style scoped>
/* Markdown渲染器样式 */
.markdown-renderer {
  line-height: 1.6;
  color: var(--text-primary, #374151);
  word-wrap: break-word;
}

/* 标题样式 */
.markdown-renderer :deep(h1),
.markdown-renderer :deep(h2),
.markdown-renderer :deep(h3),
.markdown-renderer :deep(h4),
.markdown-renderer :deep(h5),
.markdown-renderer :deep(h6) {
  margin: 16px 0 8px 0;
  font-weight: 600;
  line-height: 1.4;
  color: var(--text-primary, #374151);
}

.markdown-renderer :deep(h1) {
  font-size: 1.5em;
  border-bottom: 2px solid var(--border-primary, #e5e7eb);
  padding-bottom: 8px;
}

.markdown-renderer :deep(h2) {
  font-size: 1.3em;
  border-bottom: 1px solid var(--border-primary, #e5e7eb);
  padding-bottom: 4px;
}

.markdown-renderer :deep(h3) {
  font-size: 1.2em;
}

/* 段落样式 */
.markdown-renderer :deep(p) {
  margin: 8px 0;
  color: var(--text-primary, #374151);
}

/* 列表样式 */
.markdown-renderer :deep(ul),
.markdown-renderer :deep(ol) {
  margin: 8px 0;
  padding-left: 24px;
  color: var(--text-primary, #374151);
}

.markdown-renderer :deep(li) {
  margin: 4px 0;
}

/* 代码样式 */
.markdown-renderer :deep(code) {
  background-color: var(--bg-tertiary, #f3f4f6);
  color: var(--text-primary, #374151);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

.markdown-renderer :deep(pre) {
  background-color: var(--bg-tertiary, #1f2937);
  color: var(--text-primary, #f9fafb);
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 12px 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
  line-height: 1.4;
}

.markdown-renderer :deep(pre code) {
  background: none;
  padding: 0;
  color: inherit;
}

/* 表格样式 */
.markdown-renderer :deep(.table-wrapper) {
  overflow-x: auto;
  margin: 12px 0;
}

.markdown-renderer :deep(.markdown-table) {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid var(--border-primary, #e5e7eb);
  border-radius: 6px;
  overflow: hidden;
}

.markdown-renderer :deep(.markdown-table th),
.markdown-renderer :deep(.markdown-table td) {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid var(--border-primary, #e5e7eb);
  color: var(--text-primary, #374151);
}

.markdown-renderer :deep(.markdown-table th) {
  background-color: var(--bg-secondary, #f9fafb);
  font-weight: 600;
}

.markdown-renderer :deep(.markdown-table tr:last-child td) {
  border-bottom: none;
}

/* 引用样式 */
.markdown-renderer :deep(blockquote) {
  border-left: 4px solid var(--accent-primary, #3b82f6);
  background-color: var(--bg-secondary, #f8fafc);
  color: var(--text-secondary, #6b7280);
  padding: 12px 16px;
  margin: 12px 0;
  font-style: italic;
}

.markdown-renderer :deep(blockquote p) {
  margin: 0;
}

/* 链接样式 */
.markdown-renderer :deep(a) {
  color: var(--accent-primary, #3b82f6);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s ease;
}

.markdown-renderer :deep(a:hover) {
  border-bottom-color: var(--accent-primary, #3b82f6);
}

/* 分割线样式 */
.markdown-renderer :deep(hr) {
  border: none;
  border-top: 2px solid var(--border-primary, #e5e7eb);
  margin: 20px 0;
}

/* 强调样式 */
.markdown-renderer :deep(strong) {
  font-weight: 600;
  color: var(--text-primary, #374151);
}

.markdown-renderer :deep(em) {
  font-style: italic;
  color: var(--text-primary, #374151);
}

/* 删除线样式 */
.markdown-renderer :deep(del) {
  text-decoration: line-through;
  opacity: 0.7;
  color: var(--text-secondary, #6b7280);
}
</style>
