<template>
  <div class="session-data-manager">
    <!-- 触发按钮 -->
    <button 
      class="data-manager-trigger"
      @click="showManager = true"
      title="会话数据管理"
    >
      <DataIcon />
      数据管理
    </button>

    <!-- 数据管理弹窗 -->
    <div v-if="showManager" class="manager-overlay" @click="closeManager">
      <div class="manager-dialog" @click.stop>
        <div class="manager-header">
          <h3>会话数据管理</h3>
          <button class="close-btn" @click="closeManager">×</button>
        </div>

        <div class="manager-content">
          <!-- 存储信息 -->
          <div class="info-section">
            <h4>存储信息</h4>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">存储版本:</span>
                <span class="value">{{ storageInfo.version || '未知' }}</span>
              </div>
              <div class="info-item">
                <span class="label">数据大小:</span>
                <span class="value">{{ formatSize(storageInfo.totalSize) }}</span>
              </div>
              <div class="info-item">
                <span class="label">最后保存:</span>
                <span class="value">{{ formatTime(storageInfo.lastSaved) }}</span>
              </div>
              <div class="info-item">
                <span class="label">备份数量:</span>
                <span class="value">{{ storageInfo.backupCount || 0 }}</span>
              </div>
            </div>
          </div>

          <!-- 数据完整性 -->
          <div class="integrity-section">
            <h4>数据完整性</h4>
            <div class="integrity-status" :class="integrityResult.valid ? 'valid' : 'invalid'">
              <span class="status-icon">{{ integrityResult.valid ? '✓' : '⚠' }}</span>
              <span class="status-text">
                {{ integrityResult.valid ? '数据完整' : `发现 ${integrityResult.issues?.length || 0} 个问题` }}
              </span>
              <button 
                v-if="!integrityResult.valid" 
                class="repair-btn"
                @click="repairData"
                :disabled="repairing"
              >
                {{ repairing ? '修复中...' : '修复' }}
              </button>
            </div>
            <div v-if="integrityResult.issues && integrityResult.issues.length > 0" class="issues-list">
              <div v-for="issue in integrityResult.issues.slice(0, 5)" :key="issue" class="issue-item">
                {{ issue }}
              </div>
              <div v-if="integrityResult.issues.length > 5" class="more-issues">
                还有 {{ integrityResult.issues.length - 5 }} 个问题...
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="actions-section">
            <h4>数据操作</h4>
            <div class="actions-grid">
              <button class="action-btn export-btn" @click="exportData">
                <ExportIcon />
                导出数据
              </button>
              <button class="action-btn import-btn" @click="triggerImport">
                <ImportIcon />
                导入数据
              </button>
              <button class="action-btn backup-btn" @click="createBackup">
                <BackupIcon />
                创建备份
              </button>
              <button class="action-btn clear-btn" @click="confirmClearData" :disabled="clearing">
                <ClearIcon />
                {{ clearing ? '清除中...' : '清除数据' }}
              </button>
            </div>
          </div>

          <!-- 备份列表 -->
          <div v-if="storageInfo.backups && storageInfo.backups.length > 0" class="backups-section">
            <h4>备份列表</h4>
            <div class="backups-list">
              <div v-for="backup in storageInfo.backups.slice(0, 5)" :key="backup.key" class="backup-item">
                <span class="backup-name">{{ backup.name }}</span>
                <span class="backup-size">{{ formatSize(backup.size) }}</span>
                <button class="restore-btn" @click="restoreBackup(backup.name)">恢复</button>
              </div>
            </div>
          </div>
        </div>

        <!-- 隐藏的文件输入 -->
        <input
          ref="fileInput"
          type="file"
          accept=".json"
          style="display: none"
          @change="handleFileImport"
        />
      </div>
    </div>

    <!-- 确认对话框 -->
    <div v-if="showConfirm" class="confirm-overlay" @click="cancelConfirm">
      <div class="confirm-dialog" @click.stop>
        <div class="confirm-header">
          <h4>{{ confirmTitle }}</h4>
        </div>
        <div class="confirm-content">
          <p>{{ confirmMessage }}</p>
        </div>
        <div class="confirm-actions">
          <button class="confirm-btn cancel" @click="cancelConfirm">取消</button>
          <button class="confirm-btn confirm" @click="executeConfirm">确认</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import sessionStorageService from '../../services/sessionStorageService.js'

// 图标组件（简单的SVG图标）
const DataIcon = () => '📊'
const ExportIcon = () => '📤'
const ImportIcon = () => '📥'
const BackupIcon = () => '💾'
const ClearIcon = () => '🗑️'

// 响应式数据
const showManager = ref(false)
const storageInfo = ref({})
const integrityResult = ref({ valid: true })
const repairing = ref(false)
const clearing = ref(false)

// 确认对话框
const showConfirm = ref(false)
const confirmTitle = ref('')
const confirmMessage = ref('')
const confirmAction = ref(null)

// 文件输入引用
const fileInput = ref(null)

/**
 * 加载存储信息
 */
const loadStorageInfo = () => {
  storageInfo.value = sessionStorageService.getStorageInfo()
  integrityResult.value = sessionStorageService.checkDataIntegrity()
}

/**
 * 关闭管理器
 */
const closeManager = () => {
  showManager.value = false
}

/**
 * 格式化文件大小
 */
const formatSize = (bytes) => {
  if (!bytes) return '0 B'
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

/**
 * 格式化时间
 */
const formatTime = (timestamp) => {
  if (!timestamp) return '从未'
  return new Date(timestamp).toLocaleString('zh-CN')
}

/**
 * 修复数据
 */
const repairData = async () => {
  repairing.value = true
  try {
    const success = sessionStorageService.repairDataIntegrity()
    if (success) {
      loadStorageInfo()
      alert('数据修复完成')
    } else {
      alert('数据修复失败')
    }
  } catch (error) {
    console.error('修复数据失败:', error)
    alert('数据修复失败: ' + error.message)
  } finally {
    repairing.value = false
  }
}

/**
 * 导出数据
 */
const exportData = () => {
  try {
    const data = sessionStorageService.loadAllSessions()
    if (!data) {
      alert('没有数据可导出')
      return
    }

    const exportData = {
      ...data,
      exportTime: new Date().toISOString(),
      version: storageInfo.value.version || '1.0.0'
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `bodorai_sessions_${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    console.log('数据导出完成')
  } catch (error) {
    console.error('导出数据失败:', error)
    alert('导出数据失败: ' + error.message)
  }
}

/**
 * 触发导入
 */
const triggerImport = () => {
  fileInput.value?.click()
}

/**
 * 处理文件导入
 */
const handleFileImport = (event) => {
  const file = event.target.files[0]
  if (!file) return

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const importData = JSON.parse(e.target.result)
      
      // 验证导入数据格式
      if (!importData.moduleSessions) {
        alert('导入文件格式不正确')
        return
      }

      confirmTitle.value = '确认导入数据'
      confirmMessage.value = '导入数据将覆盖现有的所有会话数据，此操作不可撤销。是否继续？'
      confirmAction.value = () => {
        try {
          // 创建导入前备份
          sessionStorageService.createBackup('before_import')
          
          // 导入数据
          sessionStorageService.saveAllSessions(
            importData.moduleSessions,
            importData.moduleCurrentSession || {}
          )
          
          loadStorageInfo()
          alert('数据导入完成，请刷新页面以查看导入的数据')
        } catch (error) {
          console.error('导入数据失败:', error)
          alert('导入数据失败: ' + error.message)
        }
      }
      showConfirm.value = true
    } catch (error) {
      console.error('解析导入文件失败:', error)
      alert('导入文件格式错误')
    }
  }
  reader.readAsText(file)
  
  // 清空文件输入
  event.target.value = ''
}

/**
 * 创建备份
 */
const createBackup = () => {
  try {
    const backupKey = sessionStorageService.createBackup()
    if (backupKey) {
      loadStorageInfo()
      alert('备份创建成功')
    } else {
      alert('备份创建失败')
    }
  } catch (error) {
    console.error('创建备份失败:', error)
    alert('创建备份失败: ' + error.message)
  }
}

/**
 * 确认清除数据
 */
const confirmClearData = () => {
  confirmTitle.value = '确认清除数据'
  confirmMessage.value = '此操作将删除所有会话数据，包括备份。此操作不可撤销，是否继续？'
  confirmAction.value = () => {
    clearing.value = true
    try {
      const success = sessionStorageService.clearAllData()
      if (success) {
        loadStorageInfo()
        alert('数据清除完成')
      } else {
        alert('数据清除失败')
      }
    } catch (error) {
      console.error('清除数据失败:', error)
      alert('清除数据失败: ' + error.message)
    } finally {
      clearing.value = false
    }
  }
  showConfirm.value = true
}

/**
 * 恢复备份
 */
const restoreBackup = (backupName) => {
  confirmTitle.value = '确认恢复备份'
  confirmMessage.value = `确定要恢复备份"${backupName}"吗？这将覆盖当前的所有数据。`
  confirmAction.value = () => {
    try {
      const success = sessionStorageService.restoreFromBackup(backupName)
      if (success) {
        loadStorageInfo()
        alert('备份恢复完成，请刷新页面以查看恢复的数据')
      } else {
        alert('备份恢复失败')
      }
    } catch (error) {
      console.error('恢复备份失败:', error)
      alert('恢复备份失败: ' + error.message)
    }
  }
  showConfirm.value = true
}

/**
 * 执行确认操作
 */
const executeConfirm = () => {
  if (confirmAction.value) {
    confirmAction.value()
  }
  cancelConfirm()
}

/**
 * 取消确认
 */
const cancelConfirm = () => {
  showConfirm.value = false
  confirmTitle.value = ''
  confirmMessage.value = ''
  confirmAction.value = null
}

// 组件挂载时加载信息
onMounted(() => {
  loadStorageInfo()
})
</script>

<style scoped>
/* 触发按钮 */
.data-manager-trigger {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #495057;
  transition: all 0.2s;
}

.data-manager-trigger:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

/* 弹窗覆盖层 */
.manager-overlay,
.confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 管理器对话框 */
.manager-dialog {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #dee2e6;
}

.manager-header h3 {
  margin: 0;
  color: #212529;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #495057;
}

.manager-content {
  padding: 20px;
}

/* 信息部分 */
.info-section,
.integrity-section,
.actions-section,
.backups-section {
  margin-bottom: 24px;
}

.info-section h4,
.integrity-section h4,
.actions-section h4,
.backups-section h4 {
  margin: 0 0 12px 0;
  color: #495057;
  font-size: 14px;
  font-weight: 600;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 12px;
}

.label {
  color: #6c757d;
}

.value {
  color: #212529;
  font-weight: 500;
}

/* 完整性状态 */
.integrity-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border-radius: 4px;
  font-size: 14px;
}

.integrity-status.valid {
  background: #d4edda;
  color: #155724;
}

.integrity-status.invalid {
  background: #f8d7da;
  color: #721c24;
}

.repair-btn {
  margin-left: auto;
  padding: 4px 12px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.repair-btn:hover:not(:disabled) {
  background: #c82333;
}

.repair-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.issues-list {
  margin-top: 12px;
  font-size: 12px;
}

.issue-item {
  padding: 4px 8px;
  background: #fff3cd;
  color: #856404;
  border-radius: 3px;
  margin-bottom: 4px;
}

.more-issues {
  padding: 4px 8px;
  color: #6c757d;
  font-style: italic;
}

/* 操作按钮 */
.actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s;
}

.action-btn:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.export-btn:hover { border-color: #28a745; color: #28a745; }
.import-btn:hover { border-color: #007bff; color: #007bff; }
.backup-btn:hover { border-color: #ffc107; color: #856404; }
.clear-btn:hover { border-color: #dc3545; color: #dc3545; }

/* 备份列表 */
.backups-list {
  max-height: 200px;
  overflow-y: auto;
}

.backup-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  margin-bottom: 8px;
  font-size: 12px;
}

.backup-name {
  flex: 1;
  color: #495057;
}

.backup-size {
  color: #6c757d;
}

.restore-btn {
  padding: 4px 8px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 11px;
}

.restore-btn:hover {
  background: #0056b3;
}

/* 确认对话框 */
.confirm-dialog {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.confirm-header {
  padding: 16px 20px;
  border-bottom: 1px solid #dee2e6;
}

.confirm-header h4 {
  margin: 0;
  color: #212529;
}

.confirm-content {
  padding: 20px;
}

.confirm-content p {
  margin: 0;
  color: #495057;
  line-height: 1.5;
}

.confirm-actions {
  display: flex;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid #dee2e6;
  justify-content: flex-end;
}

.confirm-btn {
  padding: 8px 16px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.confirm-btn.cancel {
  background: white;
  color: #6c757d;
}

.confirm-btn.cancel:hover {
  background: #f8f9fa;
}

.confirm-btn.confirm {
  background: #dc3545;
  color: white;
  border-color: #dc3545;
}

.confirm-btn.confirm:hover {
  background: #c82333;
  border-color: #bd2130;
}
</style>
