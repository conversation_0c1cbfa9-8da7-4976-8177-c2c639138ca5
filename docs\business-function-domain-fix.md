# 业务域和职能域AI回复内容截断问题修复

## 问题描述

在业务域和职能域中，AI流式回复输出完成后，回复内容突然变成了一半，内容被截断。

## 问题原因

问题出现在 `MainLayout.vue` 的流式回复完成处理逻辑中。代码在流式回复完成后，会对所有模块的AI回复内容进行思维链解析，使用 `parseThinkingContent()` 方法提取 `finalAnswer` 作为最终显示内容。

但是对于业务域和职能域，我们已经去掉了推理过程显示，不应该进行思维链解析，因为：

1. 业务域和职能域的AI回复不包含思维链标记
2. `parseThinkingContent()` 方法会尝试从完整内容中提取"最终答案"部分
3. 当没有找到明确的思维链标记时，该方法可能会错误地截断内容

## 修复方案

在 `MainLayout.vue` 中添加条件判断，只有知识中心模块才进行思维链解析，业务域和职能域直接使用完整的AI回复内容。

### 修复前的代码

```javascript
// 解析思维链内容
let finalContent = safeAiReply
let extractedReasoning = null

try {
  const aiService = getAIService(currentModule.value)
  const parsed = aiService.parseThinkingContent(safeAiReply)
  finalContent = parsed.finalAnswer  // 这里会导致内容被截断
  extractedReasoning = parsed.thinking
} catch (error) {
  console.error('解析思维链失败:', error)
  finalContent = safeAiReply
}
```

### 修复后的代码

```javascript
// 解析思维链内容（仅知识中心需要）
let finalContent = safeAiReply
let extractedReasoning = null

// 只有知识中心才需要解析思维链，业务域和职能域直接使用完整内容
if (shouldShowReasoning) {
  try {
    const aiService = getAIService(currentModule.value)
    const parsed = aiService.parseThinkingContent(safeAiReply)
    finalContent = parsed.finalAnswer
    extractedReasoning = parsed.thinking

    if (extractedReasoning) {
      console.log('提取到推理过程:', extractedReasoning.substring(0, 100))
    }
  } catch (error) {
    console.error('解析思维链失败:', error)
    finalContent = safeAiReply
  }
} else {
  // 业务域和职能域直接使用完整的AI回复内容，不进行思维链解析
  console.log(`[${currentModule.value}] 跳过思维链解析，直接使用完整内容`)
  finalContent = safeAiReply
}
```

## 修复效果

修复后的效果：

1. **知识中心**：保持原有功能，继续进行思维链解析和推理过程展示
2. **业务域**：不进行思维链解析，直接显示完整的AI回复内容，避免内容截断
3. **职能域**：不进行思维链解析，直接显示完整的AI回复内容，避免内容截断

## 测试验证

1. 进入业务域，发送消息，观察AI回复是否完整显示
2. 进入职能域，发送消息，观察AI回复是否完整显示
3. 进入知识中心，发送消息，确认推理过程功能正常工作

## 相关文件

- `src/components/layout/MainLayout.vue` - 主要修复文件
- `src/components/common/AIMessage.vue` - 推理过程显示控制
- `src/services/aiService.js` - 思维链解析服务

## 技术细节

- 使用 `shouldShowReasoning` 变量来判断当前模块是否需要推理过程功能
- `shouldShowReasoning = currentModule.value !== 'business' && currentModule.value !== 'function'`
- 只有当 `shouldShowReasoning` 为 `true` 时才进行思维链解析
- 业务域和职能域跳过思维链解析，直接使用 `safeAiReply` 作为最终内容
