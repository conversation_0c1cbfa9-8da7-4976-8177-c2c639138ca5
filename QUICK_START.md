# Redis会话存储快速启动指南

## 🚀 5分钟快速设置

### 第一步：启动Redis HTTP代理服务器

#### Windows用户
```bash
# 双击运行启动脚本
start-redis-proxy.bat
```

#### Linux/Mac用户
```bash
# 给脚本执行权限
chmod +x start-redis-proxy.sh

# 运行启动脚本
./start-redis-proxy.sh
```

#### 手动启动（所有平台）
```bash
# 1. 创建代理服务器目录
mkdir proxy-server
cd proxy-server

# 2. 复制文件
cp ../redis-proxy-server.js ./server.js
cp ../redis-proxy-package.json ./package.json

# 3. 安装依赖
npm install

# 4. 启动服务器
npm start
```

### 第二步：启动前端应用

```bash
# 在项目根目录
npm run dev
```

### 第三步：验证连接

1. 打开浏览器访问 `http://localhost:3000`
2. 打开浏览器控制台（F12）
3. 运行测试命令：

```javascript
// 测试Redis连接
await window.testRedisConnection()

// 运行完整诊断
await window.runRedisDiagnostics()
```

## ✅ 成功标志

如果设置成功，您应该看到：

1. **代理服务器控制台**显示：
   ```
   Redis HTTP代理服务器运行在端口 4001
   Redis服务器: *************:6379
   Redis连接成功
   ```

2. **浏览器控制台**显示：
   ```
   ✅ Redis存储服务初始化成功
   ✅ 会话同步服务已启用
   ```

3. **页面右上角**显示绿色的Redis状态指示器

## ❌ 常见问题

### 问题1：代理服务器启动失败
```
Error: listen EADDRINUSE :::3001
```

**解决方案**：端口3001被占用
```bash
# Windows
netstat -ano | findstr :3001
taskkill /PID <PID> /F

# Linux/Mac
lsof -ti:3001 | xargs kill -9
```

### 问题2：Redis连接失败
```
Redis连接失败: ECONNREFUSED
```

**解决方案**：
1. 确认Redis服务器运行状态
2. 检查网络连接到 *************:6379
3. 验证Redis密码：X4gN7

### 问题3：CORS错误
```
Access to fetch blocked by CORS policy
```

**解决方案**：确认代理服务器正确启动并配置了CORS

## 🔧 调试命令

在浏览器控制台中使用以下命令进行调试：

```javascript
// 1. 快速连接测试
await window.quickRedisTest()

// 2. 完整系统诊断
await window.runRedisDiagnostics()

// 3. 查看当前状态
window.debugSessionState()

// 4. 测试数据迁移
await window.handleDataMigration()

// 5. 强制刷新会话
await window.forceRefreshSession()
```

## 📋 系统要求

### 必需软件
- Node.js 16.0+
- 现代浏览器（Chrome 80+, Firefox 75+, Safari 13+, Edge 80+）

### 网络要求
- 可访问 *************:6379 (Redis服务器)
- 可访问 localhost:4001 (代理服务器)
- 可访问 localhost:3000 (前端应用)

### Redis服务器配置
- 地址：*************
- 端口：6379
- 密码：X4gN7
- 数据库：0

## 🎯 下一步

设置成功后，您可以：

1. **创建会话**：在应用中正常创建和使用会话
2. **测试同步**：打开多个浏览器标签页测试数据同步
3. **查看存储**：使用Redis客户端查看存储的数据
4. **监控状态**：通过状态指示器监控连接状态

## 📞 获取帮助

如果遇到问题：

1. 查看 [故障排除指南](TROUBLESHOOTING.md)
2. 运行诊断工具：`await window.runRedisDiagnostics()`
3. 查看详细日志：`logger.downloadLogs('json')`
4. 联系技术支持

## 🔄 数据迁移

如果您之前使用LocalStorage存储会话数据：

1. 系统会自动检测并提示迁移
2. 点击存储状态指示器中的"迁移到Redis"按钮
3. 或在控制台运行：`await window.handleDataMigration()`

迁移过程是安全的，不会丢失现有数据。

---

**恭喜！** 您已成功设置Redis会话存储系统。现在可以享受更强大的会话管理功能了！ 🎉
