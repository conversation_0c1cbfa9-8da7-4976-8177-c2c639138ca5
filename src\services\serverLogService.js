/**
 * 服务器端日志记录服务
 * 用于在生产环境中将日志发送到服务器进行文件写入
 */

/**
 * 服务器日志服务类
 */
class ServerLogService {
  constructor(options = {}) {
    this.serverUrl = options.serverUrl || '/api/logs'
    this.timeout = options.timeout || 10000
    this.retryAttempts = options.retryAttempts || 3
    this.retryDelay = options.retryDelay || 1000
  }

  /**
   * 发送日志到服务器
   * @param {Object} logData - 日志数据
   * @returns {Promise<boolean>} 发送是否成功
   */
  async sendLogToServer(logData) {
    let attempts = 0
    
    while (attempts < this.retryAttempts) {
      try {
        const response = await this.makeRequest(logData)
        
        if (response.ok) {
          console.log('日志已成功发送到服务器')
          return true
        } else {
          throw new Error(`服务器响应错误: ${response.status}`)
        }
      } catch (error) {
        attempts++
        console.error(`发送日志失败 (尝试 ${attempts}/${this.retryAttempts}):`, error)
        
        if (attempts < this.retryAttempts) {
          await this.delay(this.retryDelay * attempts)
        }
      }
    }
    
    console.error('所有重试尝试都失败了，日志发送失败')
    return false
  }

  /**
   * 发起HTTP请求
   * @param {Object} logData - 日志数据
   * @returns {Promise<Response>} HTTP响应
   */
  async makeRequest(logData) {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), this.timeout)
    
    try {
      const response = await fetch(this.serverUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'feedback_log',
          data: logData,
          timestamp: new Date().toISOString()
        }),
        signal: controller.signal
      })
      
      clearTimeout(timeoutId)
      return response
    } catch (error) {
      clearTimeout(timeoutId)
      throw error
    }
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise<void>}
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 批量发送日志
   * @param {Array} logDataArray - 日志数据数组
   * @returns {Promise<Object>} 发送结果统计
   */
  async sendLogsBatch(logDataArray) {
    const results = {
      success: 0,
      failed: 0,
      total: logDataArray.length
    }
    
    const promises = logDataArray.map(async (logData) => {
      const success = await this.sendLogToServer(logData)
      if (success) {
        results.success++
      } else {
        results.failed++
      }
    })
    
    await Promise.all(promises)
    
    console.log('批量日志发送完成:', results)
    return results
  }

  /**
   * 检查服务器连接
   * @returns {Promise<boolean>} 连接是否正常
   */
  async checkServerConnection() {
    try {
      const response = await fetch(this.serverUrl + '/health', {
        method: 'GET',
        timeout: 5000
      })
      
      return response.ok
    } catch (error) {
      console.error('服务器连接检查失败:', error)
      return false
    }
  }
}

// 创建单例实例
let serverLogServiceInstance = null

/**
 * 获取服务器日志服务实例
 * @param {Object} options - 配置选项
 * @returns {ServerLogService} 服务器日志服务实例
 */
export function getServerLogService(options = {}) {
  if (!serverLogServiceInstance) {
    serverLogServiceInstance = new ServerLogService(options)
  }
  return serverLogServiceInstance
}

// 导出服务类
export { ServerLogService }
