/**
 * Redis会话存储服务
 * 负责会话数据的Redis持久化存储、加载和管理
 * 实现与sessionStorageService相同的API接口，支持无缝替换
 * 支持基于用户的会话隔离
 */

import { getRedisAdapter } from './redisHttpAdapter.js'
import { getRedisConfig, generateRedisKey } from '../config/redis.js'
import sessionStorageService from './sessionStorageService.js'
import { getUserService } from './userService.js'

// 存储键名常量
const STORAGE_KEYS = {
  MODULES: 'modules',                    // 模块会话数据
  CURRENT: 'current',                    // 当前选中的会话
  DETAIL: 'detail',                      // 会话详情
  USER_SESSIONS: 'user_sessions',        // 用户会话索引
  BACKUP: 'backup',                      // 备份数据
  METADATA: 'metadata'                   // 元数据
}

// 当前存储版本
const CURRENT_VERSION = '2.0.0'

/**
 * Redis会话存储服务类
 */
class RedisSessionStorageService {
  constructor() {
    this.redis = getRedisAdapter()
    this.config = getRedisConfig()
    this.isSupported = false
    this.fallbackToLocal = false
    this.userService = null

    this.initializeStorage()
  }

  /**
   * 获取用户服务实例
   */
  getUserService() {
    if (!this.userService) {
      this.userService = getUserService()
    }
    return this.userService
  }

  /**
   * 获取当前用户ID
   * @returns {string} 用户ID
   */
  getCurrentUserId() {
    const userService = this.getUserService()
    const currentUser = userService.currentUser

    if (currentUser && currentUser.userKey) {
      return currentUser.userKey
    }

    // 如果没有登录用户，使用默认用户ID
    return 'default_user'
  }

  /**
   * 生成用户专用的Redis键
   * @param {string} keyType - 键类型
   * @param {string} suffix - 键后缀
   * @returns {string} Redis键
   */
  generateUserRedisKey(keyType, suffix = '') {
    const userId = this.getCurrentUserId()
    const baseKey = `bodorai:sessions:${userId}:${keyType}`
    return suffix ? `${baseKey}:${suffix}` : baseKey
  }

  /**
   * 初始化存储
   */
  async initializeStorage() {
    try {
      // 测试Redis连接
      await this.checkRedisConnection()
      
      if (this.isSupported) {
        console.log('Redis会话存储服务初始化成功')
        // 检查存储版本，进行必要的迁移
        await this.migrateStorageIfNeeded()
      } else {
        console.warn('Redis不可用，将使用LocalStorage作为后备存储')
        this.fallbackToLocal = true
      }
    } catch (error) {
      console.error('Redis会话存储服务初始化失败:', error)
      this.fallbackToLocal = true
    }
  }

  /**
   * 检查Redis连接
   */
  async checkRedisConnection() {
    try {
      const connected = await this.redis.ping()
      this.isSupported = connected
      return connected
    } catch (error) {
      console.warn('Redis连接检查失败:', error)
      this.isSupported = false
      return false
    }
  }

  /**
   * 检查浏览器是否支持存储（兼容性方法）
   */
  checkStorageSupport() {
    return this.isSupported || this.fallbackToLocal
  }

  /**
   * 存储版本迁移
   */
  async migrateStorageIfNeeded() {
    try {
      const versionKey = `${STORAGE_KEYS.METADATA}:version`
      const storedVersion = await this.redis.get(versionKey)
      
      if (!storedVersion) {
        // 首次使用Redis，设置版本号
        await this.redis.set(versionKey, CURRENT_VERSION, this.config.sessionTTL)
        console.log('初始化Redis存储版本:', CURRENT_VERSION)
        
        // 尝试从LocalStorage迁移数据
        await this.migrateFromLocalStorage()
      } else if (storedVersion !== CURRENT_VERSION) {
        // 版本不匹配，执行迁移
        await this.performMigration(storedVersion, CURRENT_VERSION)
      }
    } catch (error) {
      console.error('Redis存储版本迁移失败:', error)
      throw error
    }
  }

  /**
   * 从LocalStorage迁移数据到Redis
   */
  async migrateFromLocalStorage() {
    try {
      console.log('开始从LocalStorage迁移数据到Redis...')
      
      // 加载LocalStorage中的数据
      const localData = sessionStorageService.loadAllSessions()
      
      if (localData && localData.moduleSessions) {
        // 迁移会话数据
        await this.saveAllSessions(localData.moduleSessions, localData.moduleCurrentSession || {})
        
        console.log('LocalStorage数据迁移到Redis完成')
        
        // 创建迁移备份
        await this.createBackup('localstorage_migration')
      } else {
        console.log('LocalStorage中没有找到会话数据，跳过迁移')
      }
    } catch (error) {
      console.error('从LocalStorage迁移数据失败:', error)
      throw error
    }
  }

  /**
   * 执行数据迁移
   */
  async performMigration(fromVersion, toVersion) {
    console.log(`执行Redis数据迁移: ${fromVersion} -> ${toVersion}`)

    try {
      // 创建备份
      await this.createBackup(`migration_${fromVersion}_to_${toVersion}`)

      // 执行具体的迁移逻辑
      if (this.shouldMigrate(fromVersion, toVersion)) {
        await this.executeMigrationSteps(fromVersion, toVersion)
      }

      // 更新版本号
      const versionKey = `${STORAGE_KEYS.METADATA}:version`
      await this.redis.set(versionKey, toVersion, this.config.sessionTTL)

      console.log('Redis数据迁移完成')
    } catch (error) {
      console.error('Redis数据迁移失败:', error)
      // 迁移失败时恢复备份
      await this.restoreFromBackup(`migration_${fromVersion}_to_${toVersion}`)
      throw error
    }
  }

  /**
   * 判断是否需要执行迁移
   */
  shouldMigrate(fromVersion, toVersion) {
    return fromVersion !== toVersion
  }

  /**
   * 执行迁移步骤
   */
  async executeMigrationSteps(fromVersion, toVersion) {
    console.log(`执行Redis迁移步骤: ${fromVersion} -> ${toVersion}`)

    // 加载现有数据
    const existingData = await this.loadAllSessions()
    if (!existingData) return

    // 执行数据结构升级
    const migratedData = this.upgradeDataStructure(existingData, fromVersion, toVersion)

    // 保存升级后的数据
    await this.saveAllSessions(migratedData.moduleSessions, migratedData.moduleCurrentSession)

    console.log('Redis数据结构升级完成')
  }

  /**
   * 升级数据结构
   */
  upgradeDataStructure(data, fromVersion, toVersion) {
    let upgradedData = { ...data }

    // 确保所有会话都有必需的属性
    if (upgradedData.moduleSessions) {
      Object.keys(upgradedData.moduleSessions).forEach(moduleKey => {
        upgradedData.moduleSessions[moduleKey] = upgradedData.moduleSessions[moduleKey].map(session => ({
          id: session.id || Date.now().toString(),
          title: session.title || '未命名会话',
          module: session.module || moduleKey,
          messages: Array.isArray(session.messages) ? session.messages : [],
          createdAt: session.createdAt ? new Date(session.createdAt) : new Date(),
          updatedAt: session.updatedAt ? new Date(session.updatedAt) : new Date(),
          pinned: Boolean(session.pinned)
        }))
      })
    }

    // 确保当前会话数据完整性
    if (upgradedData.moduleCurrentSession) {
      Object.keys(upgradedData.moduleCurrentSession).forEach(moduleKey => {
        const currentSession = upgradedData.moduleCurrentSession[moduleKey]
        if (currentSession && !currentSession.id) {
          // 如果当前会话没有ID，尝试从会话列表中找到匹配的会话
          const sessions = upgradedData.moduleSessions[moduleKey] || []
          const matchedSession = sessions.find(s => s.title === currentSession.title)
          if (matchedSession) {
            upgradedData.moduleCurrentSession[moduleKey] = {
              id: matchedSession.id,
              title: matchedSession.title,
              module: matchedSession.module
            }
          } else {
            // 找不到匹配的会话，清除当前会话引用
            upgradedData.moduleCurrentSession[moduleKey] = null
          }
        }
      })
    }

    return upgradedData
  }

  /**
   * 保存所有会话数据到Redis
   */
  async saveAllSessions(moduleSessions, moduleCurrentSession) {
    // 如果Redis不可用，回退到LocalStorage
    if (!this.isSupported && this.fallbackToLocal) {
      console.log('Redis不可用，使用LocalStorage保存会话数据')
      return sessionStorageService.saveAllSessions(moduleSessions, moduleCurrentSession)
    }

    if (!this.isSupported) return false

    try {
      const timestamp = new Date().toISOString()
      
      // 使用Pipeline批量操作提高性能
      const commands = []
      
      // 保存每个模块的会话数据
      for (const [moduleKey, sessions] of Object.entries(moduleSessions)) {
        const moduleSessionsKey = this.generateUserRedisKey(STORAGE_KEYS.MODULES, moduleKey)
        
        // 清除旧的模块会话数据
        commands.push({ command: 'del', key: moduleSessionsKey })
        
        // 保存会话列表到Hash
        const sessionHash = {}
        sessions.forEach(session => {
          sessionHash[session.id] = JSON.stringify(this.sanitizeSessionData(session))
        })
        
        // 添加元数据
        sessionHash.metadata = JSON.stringify({
          count: sessions.length,
          lastUpdated: timestamp,
          version: CURRENT_VERSION
        })
        
        // 批量设置Hash字段
        for (const [field, value] of Object.entries(sessionHash)) {
          commands.push({ command: 'hset', key: moduleSessionsKey, field, value })
        }
        
        // 设置过期时间
        commands.push({ command: 'expire', key: moduleSessionsKey, ttl: this.config.sessionTTL })
      }
      
      // 保存当前会话数据
      for (const [moduleKey, currentSession] of Object.entries(moduleCurrentSession)) {
        if (currentSession) {
          const currentSessionKey = this.generateUserRedisKey(STORAGE_KEYS.CURRENT, moduleKey)
          const currentSessionData = JSON.stringify({
            ...this.sanitizeCurrentSessionData(currentSession),
            timestamp,
            version: CURRENT_VERSION
          })
          
          commands.push({ command: 'set', key: currentSessionKey, value: currentSessionData, ttl: this.config.sessionTTL })
        }
      }
      
      // 执行批量操作
      await this.redis.pipeline(commands)
      
      console.log('会话数据已保存到Redis')
      return true
    } catch (error) {
      console.error('保存会话数据到Redis失败:', error)
      
      // 如果Redis操作失败，尝试回退到LocalStorage
      if (this.fallbackToLocal) {
        console.log('Redis保存失败，回退到LocalStorage')
        return sessionStorageService.saveAllSessions(moduleSessions, moduleCurrentSession)
      }
      
      return false
    }
  }

  /**
   * 从Redis加载所有会话数据
   */
  async loadAllSessions() {
    // 如果Redis不可用，回退到LocalStorage
    if (!this.isSupported && this.fallbackToLocal) {
      console.log('Redis不可用，使用LocalStorage加载会话数据')
      return sessionStorageService.loadAllSessions()
    }

    if (!this.isSupported) return null

    try {
      const moduleSessions = {}
      const moduleCurrentSession = {}

      // 加载所有模块的会话数据
      const moduleKeys = ['knowledge', 'business', 'function']

      for (const moduleKey of moduleKeys) {
        // 加载模块会话数据
        const moduleSessionsKey = this.generateUserRedisKey(STORAGE_KEYS.MODULES, moduleKey)
        const sessionHash = await this.redis.hgetall(moduleSessionsKey)

        if (sessionHash && Object.keys(sessionHash).length > 0) {
          const sessions = []

          for (const [sessionId, sessionData] of Object.entries(sessionHash)) {
            if (sessionId !== 'metadata') {
              try {
                const session = JSON.parse(sessionData)
                sessions.push(this.restoreSessionData(session))
              } catch (error) {
                console.warn(`解析会话数据失败 ${moduleKey}:${sessionId}:`, error)
              }
            }
          }

          moduleSessions[moduleKey] = sessions
        } else {
          moduleSessions[moduleKey] = []
        }

        // 加载当前会话数据
        const currentSessionKey = this.generateUserRedisKey(STORAGE_KEYS.CURRENT, moduleKey)
        const currentSessionData = await this.redis.get(currentSessionKey)

        if (currentSessionData) {
          try {
            const currentSession = JSON.parse(currentSessionData)
            moduleCurrentSession[moduleKey] = this.restoreCurrentSessionData(currentSession)
          } catch (error) {
            console.warn(`解析当前会话数据失败 ${moduleKey}:`, error)
            moduleCurrentSession[moduleKey] = null
          }
        } else {
          moduleCurrentSession[moduleKey] = null
        }
      }

      // 验证数据完整性
      if (!this.validateSessionData({ moduleSessions })) {
        console.warn('Redis会话数据格式无效')
        return null
      }

      console.log('会话数据已从Redis加载')
      return { moduleSessions, moduleCurrentSession }
    } catch (error) {
      console.error('从Redis加载会话数据失败:', error)

      // 如果Redis操作失败，尝试回退到LocalStorage
      if (this.fallbackToLocal) {
        console.log('Redis加载失败，回退到LocalStorage')
        return sessionStorageService.loadAllSessions()
      }

      return null
    }
  }

  /**
   * 删除指定模块的会话
   */
  async deleteSession(moduleKey, sessionId) {
    // 如果Redis不可用，回退到LocalStorage
    if (!this.isSupported && this.fallbackToLocal) {
      return sessionStorageService.deleteSession(moduleKey, sessionId)
    }

    if (!this.isSupported) return false

    try {
      // 从模块会话Hash中删除会话
      const moduleSessionsKey = `${STORAGE_KEYS.MODULES}:${moduleKey}`
      await this.redis.hdel(moduleSessionsKey, sessionId)

      // 如果删除的是当前会话，清除当前会话引用
      const currentSessionKey = `${STORAGE_KEYS.CURRENT}:${moduleKey}`
      const currentSessionData = await this.redis.get(currentSessionKey)

      if (currentSessionData) {
        try {
          const currentSession = JSON.parse(currentSessionData)
          if (currentSession.id === sessionId) {
            await this.redis.del(currentSessionKey)
          }
        } catch (error) {
          console.warn('检查当前会话时出错:', error)
        }
      }

      console.log(`已从Redis删除会话: ${moduleKey}:${sessionId}`)
      return true
    } catch (error) {
      console.error('从Redis删除会话失败:', error)

      // 如果Redis操作失败，尝试回退到LocalStorage
      if (this.fallbackToLocal) {
        return sessionStorageService.deleteSession(moduleKey, sessionId)
      }

      return false
    }
  }

  /**
   * 清除所有存储数据
   */
  async clearAllData() {
    // 如果Redis不可用，回退到LocalStorage
    if (!this.isSupported && this.fallbackToLocal) {
      return sessionStorageService.clearAllData()
    }

    if (!this.isSupported) return false

    try {
      // 获取所有相关的键
      const pattern = `${STORAGE_KEYS.MODULES}:*`
      const keys = await this.redis.keys(pattern)

      // 添加其他类型的键
      const moduleKeys = ['knowledge', 'business', 'function']
      moduleKeys.forEach(moduleKey => {
        keys.push(`${STORAGE_KEYS.CURRENT}:${moduleKey}`)
      })

      keys.push(`${STORAGE_KEYS.USER_SESSIONS}:${this.userId}`)
      keys.push(`${STORAGE_KEYS.METADATA}:version`)

      // 批量删除
      if (keys.length > 0) {
        const commands = keys.map(key => ({ command: 'del', key }))
        await this.redis.pipeline(commands)
      }

      console.log('所有Redis会话数据已清除')
      return true
    } catch (error) {
      console.error('清除Redis数据失败:', error)

      // 如果Redis操作失败，尝试回退到LocalStorage
      if (this.fallbackToLocal) {
        return sessionStorageService.clearAllData()
      }

      return false
    }
  }

  /**
   * 创建数据备份
   */
  async createBackup(backupName = null) {
    // 如果Redis不可用，回退到LocalStorage
    if (!this.isSupported && this.fallbackToLocal) {
      return sessionStorageService.createBackup(backupName)
    }

    if (!this.isSupported) return false

    try {
      const data = await this.loadAllSessions()
      if (!data) return false

      const backupKey = `${STORAGE_KEYS.BACKUP}:${backupName || new Date().toISOString()}`
      const backupData = JSON.stringify({
        ...data,
        backupTime: new Date().toISOString(),
        version: CURRENT_VERSION
      })

      await this.redis.set(backupKey, backupData, this.config.backupTTL)

      console.log('Redis数据备份已创建:', backupKey)
      return backupKey
    } catch (error) {
      console.error('创建Redis备份失败:', error)

      // 如果Redis操作失败，尝试回退到LocalStorage
      if (this.fallbackToLocal) {
        return sessionStorageService.createBackup(backupName)
      }

      return false
    }
  }

  /**
   * 从备份恢复数据
   */
  async restoreFromBackup(backupName) {
    // 如果Redis不可用，回退到LocalStorage
    if (!this.isSupported && this.fallbackToLocal) {
      return sessionStorageService.restoreFromBackup(backupName)
    }

    if (!this.isSupported) return false

    try {
      const backupKey = `${STORAGE_KEYS.BACKUP}:${backupName}`
      const backupData = await this.redis.get(backupKey)

      if (!backupData) {
        console.warn('备份数据不存在:', backupKey)
        return false
      }

      const backup = JSON.parse(backupData)

      // 恢复会话数据
      await this.saveAllSessions(backup.moduleSessions, backup.moduleCurrentSession)

      console.log('数据已从Redis备份恢复:', backupKey)
      return true
    } catch (error) {
      console.error('从Redis备份恢复数据失败:', error)

      // 如果Redis操作失败，尝试回退到LocalStorage
      if (this.fallbackToLocal) {
        return sessionStorageService.restoreFromBackup(backupName)
      }

      return false
    }
  }

  /**
   * 清理会话数据（移除不可序列化的属性）
   */
  sanitizeSessionData(session) {
    return {
      id: session.id,
      title: session.title,
      module: session.module,
      messages: session.messages || [],
      createdAt: session.createdAt instanceof Date ? session.createdAt.toISOString() : session.createdAt,
      updatedAt: session.updatedAt instanceof Date ? session.updatedAt.toISOString() : session.updatedAt,
      pinned: Boolean(session.pinned)
    }
  }

  /**
   * 清理当前会话数据
   */
  sanitizeCurrentSessionData(session) {
    return {
      id: session.id,
      title: session.title,
      module: session.module
    }
  }

  /**
   * 恢复会话数据（将字符串日期转换为Date对象）
   */
  restoreSessionData(session) {
    return {
      ...session,
      createdAt: new Date(session.createdAt),
      updatedAt: new Date(session.updatedAt),
      pinned: Boolean(session.pinned),
      messages: session.messages || []
    }
  }

  /**
   * 恢复当前会话数据
   */
  restoreCurrentSessionData(currentSession) {
    return currentSession || {}
  }

  /**
   * 验证会话数据格式
   */
  validateSessionData(sessionData) {
    if (!sessionData || typeof sessionData !== 'object') return false
    if (!sessionData.moduleSessions || typeof sessionData.moduleSessions !== 'object') return false

    // 验证每个模块的会话数据
    for (const [moduleKey, sessions] of Object.entries(sessionData.moduleSessions)) {
      if (!Array.isArray(sessions)) return false

      for (const session of sessions) {
        if (!session.id || !session.title || !session.module) return false
      }
    }

    return true
  }

  /**
   * 检查数据完整性
   */
  async checkDataIntegrity() {
    // 如果Redis不可用，回退到LocalStorage
    if (!this.isSupported && this.fallbackToLocal) {
      return sessionStorageService.checkDataIntegrity()
    }

    if (!this.isSupported) {
      return { valid: false, reason: 'Redis not supported' }
    }

    try {
      const data = await this.loadAllSessions()
      if (!data) {
        return { valid: true, reason: 'No data to check' }
      }

      // 检查会话数据完整性
      const issues = []

      Object.entries(data.moduleSessions).forEach(([moduleKey, sessions]) => {
        sessions.forEach((session, index) => {
          if (!session.id) {
            issues.push(`${moduleKey}[${index}]: 缺少会话ID`)
          }
          if (!session.title) {
            issues.push(`${moduleKey}[${index}]: 缺少会话标题`)
          }
          if (!session.module) {
            issues.push(`${moduleKey}[${index}]: 缺少模块信息`)
          }
          if (!Array.isArray(session.messages)) {
            issues.push(`${moduleKey}[${index}]: 消息列表格式错误`)
          }
          if (session.pinned === undefined) {
            issues.push(`${moduleKey}[${index}]: 缺少置顶状态`)
          }
        })
      })

      // 检查当前会话引用完整性
      Object.entries(data.moduleCurrentSession).forEach(([moduleKey, currentSession]) => {
        if (currentSession) {
          const sessions = data.moduleSessions[moduleKey] || []
          const sessionExists = sessions.some(s => s.id === currentSession.id)
          if (!sessionExists) {
            issues.push(`${moduleKey}: 当前会话引用无效`)
          }
        }
      })

      return {
        valid: issues.length === 0,
        issues: issues,
        sessionCount: Object.values(data.moduleSessions).reduce((total, sessions) => total + sessions.length, 0)
      }
    } catch (error) {
      return { valid: false, reason: error.message }
    }
  }

  /**
   * 修复数据完整性问题
   */
  async repairDataIntegrity() {
    // 如果Redis不可用，回退到LocalStorage
    if (!this.isSupported && this.fallbackToLocal) {
      return sessionStorageService.repairDataIntegrity()
    }

    if (!this.isSupported) return false

    try {
      const data = await this.loadAllSessions()
      if (!data) return true

      console.log('开始修复Redis数据完整性问题...')

      // 创建修复前的备份
      await this.createBackup('before_repair')

      let repaired = false

      // 修复会话数据
      Object.entries(data.moduleSessions).forEach(([moduleKey, sessions]) => {
        data.moduleSessions[moduleKey] = sessions.map((session, index) => {
          const repairedSession = { ...session }

          if (!repairedSession.id) {
            repairedSession.id = `${moduleKey}_${Date.now()}_${index}`
            repaired = true
          }
          if (!repairedSession.title) {
            repairedSession.title = `${moduleKey}会话 ${index + 1}`
            repaired = true
          }
          if (!repairedSession.module) {
            repairedSession.module = moduleKey
            repaired = true
          }
          if (!Array.isArray(repairedSession.messages)) {
            repairedSession.messages = []
            repaired = true
          }
          if (repairedSession.pinned === undefined) {
            repairedSession.pinned = false
            repaired = true
          }
          if (!repairedSession.createdAt) {
            repairedSession.createdAt = new Date()
            repaired = true
          }
          if (!repairedSession.updatedAt) {
            repairedSession.updatedAt = new Date()
            repaired = true
          }

          return repairedSession
        })
      })

      // 修复当前会话引用
      Object.entries(data.moduleCurrentSession).forEach(([moduleKey, currentSession]) => {
        if (currentSession) {
          const sessions = data.moduleSessions[moduleKey] || []
          const sessionExists = sessions.some(s => s.id === currentSession.id)
          if (!sessionExists) {
            // 当前会话引用无效，设置为第一个会话或null
            data.moduleCurrentSession[moduleKey] = sessions.length > 0 ? {
              id: sessions[0].id,
              title: sessions[0].title,
              module: sessions[0].module
            } : null
            repaired = true
          }
        }
      })

      if (repaired) {
        // 保存修复后的数据
        await this.saveAllSessions(data.moduleSessions, data.moduleCurrentSession)
        console.log('Redis数据完整性修复完成')
      } else {
        console.log('Redis数据完整性检查通过，无需修复')
      }

      return true
    } catch (error) {
      console.error('修复Redis数据完整性失败:', error)
      return false
    }
  }

  /**
   * 获取存储使用情况
   */
  async getStorageInfo() {
    // 如果Redis不可用，回退到LocalStorage
    if (!this.isSupported && this.fallbackToLocal) {
      return sessionStorageService.getStorageInfo()
    }

    if (!this.isSupported) {
      return { supported: false }
    }

    try {
      const info = {
        supported: true,
        version: CURRENT_VERSION,
        connected: this.redis.isConnected(),
        sessionDataSize: 0,
        currentSessionDataSize: 0,
        totalSize: 0,
        lastSaved: null,
        backups: [],
        backupCount: 0,
        totalBackupSize: 0
      }

      // 获取模块会话数据大小
      const moduleKeys = ['knowledge', 'business', 'function']
      for (const moduleKey of moduleKeys) {
        const moduleSessionsKey = `${STORAGE_KEYS.MODULES}:${moduleKey}`
        const sessionHash = await this.redis.hgetall(moduleSessionsKey)

        if (sessionHash) {
          const sessionDataStr = JSON.stringify(sessionHash)
          info.sessionDataSize += sessionDataStr.length
        }

        const currentSessionKey = `${STORAGE_KEYS.CURRENT}:${moduleKey}`
        const currentSessionData = await this.redis.get(currentSessionKey)

        if (currentSessionData) {
          info.currentSessionDataSize += currentSessionData.length

          try {
            const currentSession = JSON.parse(currentSessionData)
            if (currentSession.timestamp) {
              info.lastSaved = currentSession.timestamp
            }
          } catch (error) {
            // 忽略解析错误
          }
        }
      }

      info.totalSize = info.sessionDataSize + info.currentSessionDataSize

      // 获取备份信息
      const backupKeys = await this.redis.keys(`${STORAGE_KEYS.BACKUP}:*`)
      info.backupCount = backupKeys.length

      for (const backupKey of backupKeys) {
        const backupData = await this.redis.get(backupKey)
        if (backupData) {
          info.backups.push({
            key: backupKey,
            name: backupKey.replace(`${STORAGE_KEYS.BACKUP}:`, ''),
            size: backupData.length
          })
          info.totalBackupSize += backupData.length
        }
      }

      return info
    } catch (error) {
      console.error('获取Redis存储信息失败:', error)
      return { supported: true, error: error.message }
    }
  }

  /**
   * 发布会话更新通知
   */
  async publishSessionUpdate(moduleKey, sessionId, action = 'update') {
    if (!this.isSupported) return false

    try {
      const message = {
        type: 'session_update',
        action,
        moduleKey,
        sessionId,
        userId: this.userId,
        timestamp: new Date().toISOString()
      }

      await this.redis.publish('sessionUpdate', message)
      console.log(`已发布会话${action}通知:`, message)
      return true
    } catch (error) {
      console.error('发布会话更新通知失败:', error)
      return false
    }
  }

  /**
   * 获取Redis连接状态
   */
  getConnectionStatus() {
    return {
      supported: this.isSupported,
      connected: this.redis ? this.redis.isConnected() : false,
      fallbackToLocal: this.fallbackToLocal,
      config: {
        host: this.config.host,
        port: this.config.port,
        db: this.config.db
      }
    }
  }

  /**
   * 重新连接Redis
   */
  async reconnect() {
    try {
      console.log('尝试重新连接Redis...')
      const connected = await this.checkRedisConnection()

      if (connected) {
        this.fallbackToLocal = false
        console.log('Redis重新连接成功')
      } else {
        this.fallbackToLocal = true
        console.log('Redis重新连接失败，继续使用LocalStorage')
      }

      return connected
    } catch (error) {
      console.error('Redis重新连接失败:', error)
      this.fallbackToLocal = true
      return false
    }
  }
}

// 创建单例实例
let redisSessionStorageService = null

/**
 * 获取Redis会话存储服务实例
 */
export const getRedisSessionStorageService = async () => {
  if (!redisSessionStorageService) {
    redisSessionStorageService = new RedisSessionStorageService()
    // 等待初始化完成
    await redisSessionStorageService.initializeStorage()
  }
  return redisSessionStorageService
}

// 默认导出服务实例
export default RedisSessionStorageService
