@import './base.css';

/* 重置默认样式，确保全屏显示 */
#app {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  font-weight: normal;
  overflow: hidden;
}

/* 链接样式保持不变 */
a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

/* 移除响应式布局限制，确保全屏 */
@media (min-width: 1024px) {
  body {
    display: block; /* 改为block */
    place-items: unset;
  }

  #app {
    display: block; /* 改为block */
    grid-template-columns: unset;
    padding: 0;
    width: 100vw;
    height: 100vh;
  }
}
