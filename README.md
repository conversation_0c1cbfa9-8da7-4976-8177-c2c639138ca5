# 邦德激光AI助手 (BodorAI)

基于Vue 3 + Vite构建的智能AI助手应用，支持知识中心、业务域和职能域的AI对话服务，集成单点登录(SSO)功能。

## ✨ 主要功能

- 🤖 **多模块AI对话** - 支持知识中心、业务域、职能域三个模块
- 🔐 **单点登录(SSO)** - 集成企业SSO认证系统
- 💾 **会话管理** - 支持Redis和本地存储的会话持久化
- 📱 **响应式设计** - 完美适配桌面端和移动端
- 🎨 **主题切换** - 支持明暗主题切换
- 🔄 **实时同步** - 多设备会话数据同步

## 🚀 快速开始

### 环境要求

- Node.js 16+
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 开发环境启动

#### 标准启动（不含SSO）
```bash
npm run dev
```

#### 完整启动（包含SSO代理）
```bash
npm run dev:full
```

#### 使用启动脚本
```bash
# Windows
start-sso-dev.bat

# Linux/Mac
./start-sso-dev.sh
```

### 生产构建

```bash
npm run build
```

## 🔐 SSO单点登录

### 使用方法

1. 启动完整服务：`npm run dev:full`
2. 访问SSO登录URL：`http://localhost:3000?tokenId=YOUR_TOKEN`
3. 系统自动验证token并登录用户

### SSO URL格式

```
http://localhost:3000?tokenId=<SSO_TOKEN>
```

### 测试SSO功能

打开 `sso-test.html` 文件进行SSO功能测试。

详细文档：[SSO集成指南](docs/sso-integration-guide.md)

## 📁 项目结构

```
bodorai/
├── src/
│   ├── components/          # Vue组件
│   │   ├── common/         # 通用组件
│   │   ├── knowledge/      # 知识中心组件
│   │   ├── business/       # 业务域组件
│   │   └── function/       # 职能域组件
│   ├── services/           # 服务层
│   │   ├── userService.js  # 用户服务（含SSO）
│   │   ├── aiService.js    # AI服务
│   │   └── sessionStorageService.js # 会话存储
│   ├── config/             # 配置文件
│   └── utils/              # 工具函数
├── docs/                   # 文档
├── sso-proxy-server.js     # SSO代理服务器
├── sso-test.html          # SSO测试页面
└── start-sso-dev.*        # 启动脚本
```

## 🛠️ 开发指南

### 可用脚本

- `npm run dev` - 启动开发服务器
- `npm run dev:full` - 启动完整服务（含SSO代理）
- `npm run build` - 构建生产版本
- `npm run preview` - 预览生产构建
- `npm run sso-proxy` - 单独启动SSO代理服务器

### 环境配置

复制并修改环境配置文件：

```bash
# 开发环境
cp .env.development.example .env.development

# 生产环境
cp .env.production.example .env.production
```

### 服务端口

- 前端应用：`3000`
- SSO代理服务器：`4003`
- Redis代理：`4001`

## 📖 文档

- [SSO集成指南](docs/sso-integration-guide.md)
- [CORS解决方案](docs/cors-solution-guide.md)
- [部署指南](docs/deployment-guide.md)

## 🔧 技术栈

- **前端框架**：Vue 3 + Composition API
- **构建工具**：Vite
- **UI样式**：原生CSS + 响应式设计
- **状态管理**：Vue 3 Reactivity
- **HTTP客户端**：Fetch API + Axios
- **存储方案**：Redis + LocalStorage
- **代理服务器**：Express.js

## 📝 更新日志

### v1.1.0 (2024-01-XX)
- ✅ 新增SSO单点登录功能
- ✅ 添加SSO代理服务器
- ✅ 完善用户认证流程
- ✅ 优化移动端体验

### v1.0.0 (2024-01-XX)
- ✅ 基础AI对话功能
- ✅ 多模块支持
- ✅ 会话管理
- ✅ 响应式设计
