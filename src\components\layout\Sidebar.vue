<template>
  <!-- 左侧导航栏 -->
  <div class="sidebar">
    <!-- 导航菜单列表 -->
    <nav class="nav-menu">
      <div 
        v-for="item in menuItems" 
        :key="item.key"
        class="nav-item"
        :class="{ 'active': currentModule === item.key }"
        @click="handleMenuClick(item.key)"
      >
        <!-- 菜单图标 -->
        <div class="nav-icon">
          <component :is="item.icon" />
        </div>
        
        <!-- 菜单文字 -->
        <div class="nav-text">
          {{ item.label }}
        </div>
      </div>
    </nav>
  </div>
</template>

<script setup>
// defineProps 和 defineEmits 在 Vue 3 中是编译器宏，不需要导入

// 图标组件（简单的SVG图标）
const BookIcon = {
  template: `
    <svg t="1752202348172" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11866" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200">
      <path d="M896 768H256a42.666667 42.666667 0 0 0 0 85.333333h640v85.333334H256a128 128 0 0 1-128-128V170.666667a85.333333 85.333333 0 0 1 85.333333-85.333334h682.666667v682.666667zM213.333333 684.8c6.912-1.408 14.037333-2.133333 21.333334-2.133333H810.666667V170.666667H213.333333v514.133333zM682.666667 384H341.333333V298.666667h341.333334v85.333333z" fill="#000000" p-id="11867"></path>
    </svg>
  `
}

const BusinessIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 7V3H2v18h20V7H12zM6 19H4v-2h2v2zm0-4H4v-2h2v2zm0-4H4V9h2v2zm0-4H4V5h2v2zm4 12H8v-2h2v2zm0-4H8v-2h2v2zm0-4H8V9h2v2zm0-4H8V5h2v2zm10 12h-8v-2h2v-2h-2v-2h2v-2h-2V9h8v10zm-2-8h-2v2h2v-2zm0 4h-2v2h2v-2z"/>
    </svg>
  `
}

const FunctionIcon = {
  template: `
    <svg t="1752202072887" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10856" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200">
      <path d="M919.722667 817.493333c-33.450667 0-61.952 16.725333-81.066667 40.618667l-83.456-71.509333c38.229333-50.176 62.122667-114.517333 62.122667-183.808 0-164.693333-131.242667-298.325333-293.546667-303.104v-90.624c50.176-4.778667 90.624-47.786667 90.624-100.181334 0-54.954667-45.397333-100.181333-100.181333-100.181333-54.954667 0-100.181333 45.397333-100.181334 100.181333 0 52.565333 40.618667 95.402667 90.624 100.181334v90.624c-162.304 7.168-293.546667 138.410667-293.546666 303.104 0 69.12 23.893333 133.632 61.952 183.808l-83.626667 68.949333c-19.114667-23.893333-47.786667-40.618667-81.066667-40.618667-54.954667 0-100.181333 45.397333-100.181333 100.181334 0 54.954667 45.397333 100.181333 100.181333 100.181333 54.954667 0 100.181333-45.397333 100.181334-100.181333 0-14.336-2.389333-28.672-9.557334-43.008l85.845334-71.509334c54.954667 62.122667 136.021333 102.570667 226.645333 102.570667s171.861333-40.618667 226.645333-102.570667l85.845334 71.509334c-4.778667 11.946667-9.557333 26.282667-9.557334 43.008 0 54.954667 45.397333 100.181333 100.181334 100.181333 54.954667 0 100.181333-45.397333 100.181333-100.181333 7.509333-52.394667-37.888-97.621333-95.061333-97.621334zM513.194667 354.986667c54.784 0 98.986667 57.685333 98.986666 128.853333s-44.373333 128.853333-98.986666 128.853333c-54.784 0-98.986667-57.685333-98.986667-128.853333-0.170667-71.168 44.202667-128.853333 98.986667-128.853333z m-196.266667 409.6s10.752-85.333333 35.157333-101.376 94.72-26.794667 94.72-26.794667 45.568 48.128 64.853334 48.128 64.853333-48.128 64.853333-48.128 70.314667 10.752 94.72 26.794667c28.672 18.944 35.84 101.376 35.84 101.376H316.928z" fill="#090908" p-id="10857"></path>
    </svg>
  `
}

const HelpIcon = {
  template: `
  <svg t="1752218857169" class="icon" viewBox="0 0 1041 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13740" xmlns:xlink="http://www.w3.org/1999/xlink" width="203.3203125" height="200">
    <path d="M784.210865 956.852459H291.795564c-61.551913 0-117.508197-50.360656-117.508197-111.912568V346.928962c0-61.551913 50.360656-117.508197 111.912569-117.508197H521.216329c27.978142 0 55.956284 22.382514 55.956284 50.360656v5.595628c0 27.978142-22.382514 55.956284-50.360655 55.956284H291.795564c-5.595628 0-5.595628 0-5.595628 5.595629v492.4153c0 5.595628 0 5.595628 5.595628 5.595629h492.415301c5.595628 0 5.595628 0 5.595628-5.595629V609.923497c0-33.57377 22.382514-55.956284 55.956284-55.956284s55.956284 22.382514 55.956284 55.956284v229.420765c0 61.551913-50.360656 117.508197-117.508196 117.508197 5.595628 0 0 0 0 0" fill="#666666" p-id="13741"></path><path d="M616.342012 598.73224c-22.382514 22.382514-55.956284 22.382514-78.338798 0-22.382514-22.382514-22.382514-55.956284 0-78.338797L856.954034 201.442623c22.382514-22.382514 55.956284-22.382514 78.338798 0 22.382514 22.382514 22.382514 55.956284 0 78.338798L616.342012 598.73224z" fill="#666666" p-id="13742"></path>
  </svg>
  `
}

// 组件属性
const props = defineProps({
  // 当前选中的模块
  currentModule: {
    type: String,
    default: 'knowledge'
  }
})

// 组件事件
const emit = defineEmits(['module-change'])

// 菜单项配置
const menuItems = [
  {
    key: 'knowledge',
    label: '知识中心',
    icon: BookIcon,
    description: '智能问答和知识搜索'
  },
  {
    key: 'business',
    label: '业务域',
    icon: BusinessIcon,
    description: '业务相关的专业问答'
  },
  {
    key: 'function',
    label: '职能域',
    icon: FunctionIcon,
    description: '职能相关的功能服务'
  },
  {
    key: 'help',
    label: '帮助与反馈',
    icon: HelpIcon,
    description: '使用帮助和意见反馈'
  }
]

/**
 * 处理菜单点击事件
 * @param {string} moduleKey - 点击的模块键值
 */
const handleMenuClick = (moduleKey) => {
  if (moduleKey !== props.currentModule) {
    console.log('点击导航菜单:', moduleKey)
    emit('module-change', moduleKey)
  }
}
</script>

<style scoped>
/* 侧边栏容器 */
.sidebar {
  height: 100vh;
  width: 100%;
  background: var(--sidebar-bg);
  display: flex;
  flex-direction: column;
  padding: 24px 0;
  margin: 0;
  border: none;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--sidebar-overlay);
  pointer-events: none;
}

/* 导航菜单 */
.nav-menu {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 0 12px;
  position: relative;
  z-index: 1;
}

/* 导航项 */
.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 18px 12px;
  margin: 4px 8px;
  border-radius: 16px;
  border: none;
  outline: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--sidebar-nav-color);
  position: relative;
  overflow: hidden;
  min-height: 80px;
  text-align: center;
}

/* 导航项悬停效果 - 改变颜色 */
.nav-item:hover {
  color: rgba(255, 255, 255, 0.9);
}

/* 激活状态的导航项 - 只有图标有背景 */
.nav-item.active {
  color: #ffffff;
  font-weight: 700;
  position: relative;
}



/* 导航图标 */
.nav-icon {
  width: 36px;
  height: 36px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-icon svg {
  width: 26px;
  height: 26px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* 悬停时图标的悬浮效果 - 无背景 */
.nav-item:hover .nav-icon {
  transform: translateY(-1px) scale(1.05);
}

.nav-item:hover .nav-icon svg {
  transform: scale(1.1);
  filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.3)) brightness(1.1);
}

/* 选中状态的图标 - 适度变大，完全无背景 */
.nav-item.active .nav-icon {
  transform: translateY(-2px) scale(1.1);
  background: none !important;
  background-color: transparent !important;
  box-shadow: none !important;
  border: none !important;
}

.nav-item.active .nav-icon svg {
  transform: scale(1.2);
  filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.5)) brightness(1.3) contrast(1.2) drop-shadow(0 0 10px rgba(255, 255, 255, 0.2));
}

/* 导航文字 */
.nav-text {
  font-size: 13px;
  font-weight: 500;
  text-align: center;
  line-height: 1.3;
  white-space: nowrap;
  letter-spacing: 0.3px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 悬停时文字的效果 */
.nav-item:hover .nav-text {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
}

/* 选中状态的文字 - 明显区分 */
.nav-item.active .nav-text {
  font-weight: 700;
  font-size: 14px;
  color: #ffffff;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.5), 0 0 8px rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  letter-spacing: 0.5px;
}

/* 响应式设计 */

/* 平板端适配 */
@media (max-width: 1024px) {
  .sidebar {
    padding: var(--spacing-md) 0;
  }

  .nav-menu {
    padding: 0 var(--spacing-xs);
    gap: var(--spacing-xs);
  }

  .nav-item {
    padding: var(--spacing-md) var(--spacing-sm);
    margin: var(--spacing-xs);
    min-height: 70px;
  }

  .nav-icon {
    width: 32px;
    height: 32px;
    margin-bottom: var(--spacing-sm);
  }

  .nav-icon svg {
    width: 22px;
    height: 22px;
  }

  .nav-text {
    font-size: var(--font-size-sm);
  }

  .nav-item.active .nav-text {
    font-size: var(--font-size-base);
  }
}

/* 移动端适配 - 垂直居中布局 */
@media (max-width: 768px) {
  .sidebar {
    padding: var(--spacing-sm) 0;
    display: flex;
    flex-direction: column;
    justify-content: center; /* 垂直居中 */
  }

  .nav-menu {
    flex-direction: column;
    justify-content: center; /* 垂直居中 */
    padding: 0 var(--spacing-xs);
    gap: var(--spacing-xs);
    flex: none; /* 不占用额外空间 */
  }

  .nav-item {
    padding: var(--spacing-sm) var(--spacing-xs);
    margin: var(--spacing-xs) 0;
    min-height: auto;
    border-radius: var(--border-radius-md);
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .nav-icon {
    width: 24px;
    height: 24px;
    margin-bottom: var(--spacing-xs);
  }

  .nav-icon svg {
    width: 18px;
    height: 18px;
  }

  .nav-text {
    font-size: var(--font-size-xs);
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.2;
    text-align: center;
    white-space: nowrap;
  }

  .nav-item.active .nav-text {
    font-size: var(--font-size-sm);
    font-weight: 600;
  }
}

/* 小屏手机适配 */
@media (max-width: 480px) {
  .nav-item {
    padding: var(--spacing-xs);
    margin: 2px 0;
  }

  .nav-icon {
    width: 20px;
    height: 20px;
    margin-bottom: 2px;
  }

  .nav-icon svg {
    width: 16px;
    height: 16px;
  }

  .nav-text {
    font-size: 10px;
    line-height: 1.1;
  }

  .nav-item.active .nav-text {
    font-size: 11px;
  }
}
</style>
