/**
 * Redis连接测试工具
 * 用于测试和验证Redis连接时间点功能
 */

import { getRedisAdapter } from '../services/redisHttpAdapter.js'
import { getRedisConfig } from '../config/redis.js'
import { getUserService } from '../services/userService.js'

/**
 * Redis连接测试器类
 */
class RedisConnectionTester {
  constructor() {
    this.redis = getRedisAdapter()
    this.config = getRedisConfig()
    this.userService = getUserService()
  }

  /**
   * 测试基本Redis连接
   */
  async testBasicConnection() {
    console.log('🔍 测试基本Redis连接...')
    
    try {
      const result = await this.redis.ping()
      if (result) {
        console.log('✅ Redis基本连接测试成功')
        return { success: true, message: 'Redis连接正常' }
      } else {
        console.log('❌ Redis基本连接测试失败')
        return { success: false, message: 'Redis ping失败' }
      }
    } catch (error) {
      console.error('❌ Redis基本连接测试出错:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 测试用户登录后的Redis操作
   */
  async testPostLoginRedisOperations() {
    console.log('🔍 测试用户登录后的Redis操作...')
    
    try {
      // 获取当前用户
      const currentUser = await this.userService.getCurrentUser()
      if (!currentUser) {
        return { success: false, message: '用户未登录' }
      }

      // 测试用户登录记录写入
      const loginKey = `user:login:${currentUser.userKey}`
      const loginData = {
        username: currentUser.username,
        employeeId: currentUser.employeeId,
        loginTime: new Date().toISOString(),
        sessionId: Date.now().toString(),
        testFlag: true
      }

      // 写入测试数据
      await this.redis.set(loginKey, JSON.stringify(loginData), 60) // 1分钟过期
      console.log('✅ 用户登录记录写入成功')

      // 读取验证
      const retrievedData = await this.redis.get(loginKey)
      if (retrievedData) {
        const parsedData = JSON.parse(retrievedData)
        if (parsedData.testFlag) {
          console.log('✅ 用户登录记录读取验证成功')
          return { 
            success: true, 
            message: '用户登录后Redis操作测试成功',
            data: parsedData
          }
        }
      }

      return { success: false, message: '数据读取验证失败' }
    } catch (error) {
      console.error('❌ 用户登录后Redis操作测试失败:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 测试Redis连接时间点功能
   */
  async testConnectionTiming() {
    console.log('🔍 测试Redis连接时间点功能...')
    
    const results = {
      basicConnection: null,
      postLoginOperations: null,
      timing: {
        startTime: new Date().toISOString(),
        endTime: null,
        duration: null
      }
    }

    const startTime = Date.now()

    try {
      // 1. 测试基本连接
      results.basicConnection = await this.testBasicConnection()

      // 2. 测试登录后操作
      results.postLoginOperations = await this.testPostLoginRedisOperations()

      // 3. 记录时间
      const endTime = Date.now()
      results.timing.endTime = new Date().toISOString()
      results.timing.duration = endTime - startTime

      console.log('🔍 Redis连接时间点测试完成:', results)
      return results
    } catch (error) {
      console.error('❌ Redis连接时间点测试失败:', error)
      results.timing.endTime = new Date().toISOString()
      results.timing.duration = Date.now() - startTime
      results.error = error.message
      return results
    }
  }

  /**
   * 模拟用户登录流程的Redis连接测试
   */
  async simulateLoginFlow() {
    console.log('🎭 模拟用户登录流程的Redis连接测试...')
    
    const steps = []
    
    try {
      // 步骤1: 模拟登录前状态
      steps.push({
        step: 1,
        name: '登录前状态检查',
        timestamp: new Date().toISOString(),
        result: await this.testBasicConnection()
      })

      // 步骤2: 模拟登录完成
      steps.push({
        step: 2,
        name: '模拟登录完成',
        timestamp: new Date().toISOString(),
        result: { success: true, message: '模拟登录成功' }
      })

      // 步骤3: 模拟登录后Redis连接
      steps.push({
        step: 3,
        name: '登录后Redis连接',
        timestamp: new Date().toISOString(),
        result: await this.testPostLoginRedisOperations()
      })

      console.log('🎭 用户登录流程模拟完成:', steps)
      return { success: true, steps }
    } catch (error) {
      console.error('❌ 用户登录流程模拟失败:', error)
      return { success: false, error: error.message, steps }
    }
  }

  /**
   * 清理测试数据
   */
  async cleanupTestData() {
    console.log('🧹 清理Redis测试数据...')
    
    try {
      const currentUser = await this.userService.getCurrentUser()
      if (currentUser) {
        const loginKey = `user:login:${currentUser.userKey}`
        await this.redis.del(loginKey)
        console.log('✅ 测试数据清理完成')
      }
    } catch (error) {
      console.error('❌ 清理测试数据失败:', error)
    }
  }
}

// 创建单例实例
let redisConnectionTester = null

/**
 * 获取Redis连接测试器实例
 */
export const getRedisConnectionTester = () => {
  if (!redisConnectionTester) {
    redisConnectionTester = new RedisConnectionTester()
  }
  return redisConnectionTester
}

/**
 * 快速测试Redis连接时间点功能
 */
export const quickTestRedisConnectionTiming = async () => {
  const tester = getRedisConnectionTester()
  return await tester.testConnectionTiming()
}

/**
 * 快速模拟登录流程测试
 */
export const quickSimulateLoginFlow = async () => {
  const tester = getRedisConnectionTester()
  return await tester.simulateLoginFlow()
}

// 暴露到全局，方便调试
if (typeof window !== 'undefined') {
  window.testRedisConnectionTiming = quickTestRedisConnectionTiming
  window.simulateLoginFlow = quickSimulateLoginFlow
  window.getRedisConnectionTester = getRedisConnectionTester
}

export default RedisConnectionTester
