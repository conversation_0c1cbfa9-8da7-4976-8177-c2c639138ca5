# 反馈日志记录功能使用指南

## 概述

反馈日志记录功能用于将用户提交的意见反馈自动记录到.log文件中，包含用户身份信息（用户名、工号）和时间戳等详细信息。

## 功能特性

### 🔐 用户身份记录
- 自动获取当前登录用户的用户名和工号
- 支持匿名用户反馈记录
- 记录用户的登录时间和最后活跃时间

### 📝 反馈内容记录
- 支持多种反馈类型：问题反馈、功能建议、体验改进、其他
- 完整记录反馈内容和联系方式
- 记录提交时的URL和用户代理信息

### 📊 日志管理
- 自动日志轮转，防止文件过大
- 支持日志文件下载
- 提供详细的统计信息
- 支持批量清理功能

### 🌐 双重存储
- 本地存储：使用localStorage作为主要存储
- 服务器存储：可选的服务器端日志记录

## 快速开始

### 1. 基本使用

```javascript
import { getFeedbackLogService } from './src/services/feedbackLogService.js'

// 获取服务实例
const feedbackLogService = getFeedbackLogService()

// 记录反馈
const success = await feedbackLogService.logFeedback({
  type: 'suggestion',
  content: '建议增加快捷键功能',
  contact: '<EMAIL>'
})

if (success) {
  console.log('反馈记录成功')
}
```

### 2. 在Vue组件中使用

```javascript
// 在HelpFeedback.vue中
import { getFeedbackLogService } from '../../services/feedbackLogService.js'

const submitFeedback = async () => {
  const feedbackLogService = getFeedbackLogService()
  
  const success = await feedbackLogService.logFeedback({
    type: feedbackForm.value.type,
    content: feedbackForm.value.content,
    contact: feedbackForm.value.contact
  })
  
  if (success) {
    // 反馈记录成功
  }
}
```

## 日志格式

每条反馈日志包含以下信息：

```
[2024-01-15 14:30:25] FEEDBACK_LOG
ID: feedback_1705298425123_abc123def
User: zhang_san (EMP001)
Type: suggestion
Content: 建议增加快捷键功能，提高操作效率
Contact: <EMAIL>
URL: http://localhost:3000/help-feedback
UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
---
```

## API 参考

### FeedbackLogService

#### 方法

##### `logFeedback(feedbackData)`
记录反馈日志

**参数:**
- `feedbackData` (Object): 反馈数据
  - `type` (string): 反馈类型 ('bug', 'suggestion', 'improvement', 'other')
  - `content` (string): 反馈内容
  - `contact` (string, 可选): 联系方式

**返回:** `Promise<boolean>` - 记录是否成功

##### `getLogStats()`
获取日志统计信息

**返回:** `Promise<Object>` - 统计信息对象

##### `downloadLogFile(filename?)`
下载日志文件

**参数:**
- `filename` (string, 可选): 自定义文件名

##### `clearAllLogs()`
清空所有日志

**返回:** `Promise<boolean>` - 清空是否成功

##### `enableServerLogging(serverUrl?)`
启用服务器日志记录

**参数:**
- `serverUrl` (string, 可选): 服务器URL

##### `disableServerLogging()`
禁用服务器日志记录

## 服务器端部署

### 1. 启动日志服务器

```bash
# 安装依赖
npm install express cors

# 启动日志服务器
node log-server.js
```

服务器将在端口4002上运行，提供以下端点：
- `GET /api/logs/health` - 健康检查
- `POST /api/logs` - 接收日志数据
- `GET /api/logs/stats` - 获取服务器端统计

### 2. 启用服务器日志记录

```javascript
const feedbackLogService = getFeedbackLogService()

// 启用服务器日志记录
feedbackLogService.enableServerLogging('http://localhost:4002/api/logs')

// 检查服务器连接
const isConnected = await feedbackLogService.checkServerConnection()
if (isConnected) {
  console.log('服务器连接正常')
}
```

## 配置选项

### 日志配置

```javascript
const feedbackLogService = getFeedbackLogService()

// 获取当前配置
const config = feedbackLogService.getConfig()
console.log(config)
// {
//   logFileName: 'feedback.log',
//   maxLogSize: 10485760, // 10MB
//   maxLogFiles: 5,
//   useServerLogging: false,
//   serverUrl: null
// }
```

### 服务器配置

在 `log-server.js` 中可以配置：

```javascript
const LOG_CONFIG = {
  logDir: './logs',           // 日志目录
  feedbackLogFile: 'feedback.log', // 日志文件名
  maxLogSize: 10 * 1024 * 1024,    // 最大文件大小 (10MB)
  maxLogFiles: 10             // 最大文件数量
}
```

## 测试

### 1. 浏览器测试

打开 `test-feedback-log.html` 文件进行交互式测试：

```bash
# 启动本地服务器
python -m http.server 8000
# 或
npx serve .

# 访问测试页面
http://localhost:8000/test-feedback-log.html
```

### 2. 编程测试

```javascript
import { runAllTests, cleanupTestData } from './src/tests/feedbackLogTest.js'

// 运行所有测试
await runAllTests()

// 清理测试数据
await cleanupTestData()
```

## 故障排除

### 常见问题

1. **日志记录失败**
   - 检查用户是否已登录
   - 确认浏览器支持localStorage
   - 查看控制台错误信息

2. **服务器连接失败**
   - 确认日志服务器已启动
   - 检查服务器URL配置
   - 验证网络连接和CORS设置

3. **日志文件过大**
   - 系统会自动进行日志轮转
   - 可以手动清理旧日志文件
   - 调整maxLogSize配置

### 调试模式

启用详细日志输出：

```javascript
// 在浏览器控制台中
localStorage.setItem('debug_feedback_log', 'true')

// 重新加载页面后会显示详细调试信息
```

## 安全考虑

1. **数据隐私**: 反馈内容可能包含敏感信息，确保适当的访问控制
2. **存储限制**: localStorage有大小限制，定期清理或使用服务器存储
3. **用户身份**: 确保用户身份信息的准确性和安全性

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 支持基本的反馈日志记录
- 实现日志轮转和统计功能
- 添加服务器端存储支持
