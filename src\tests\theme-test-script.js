/**
 * 主题切换和AI消息颜色测试脚本
 * 在浏览器控制台中运行此脚本来测试主题切换功能
 */

// 测试主题切换功能
function testThemeToggle() {
    console.log('🎨 开始测试主题切换功能...');
    
    const root = document.documentElement;
    const currentTheme = root.classList.contains('dark-theme') ? 'dark' : 'light';
    
    console.log(`当前主题: ${currentTheme}`);
    
    // 切换主题
    if (currentTheme === 'light') {
        root.classList.add('dark-theme');
        root.setAttribute('data-theme', 'moon');
        console.log('✅ 已切换到深色主题 (Moon模式)');
    } else {
        root.classList.remove('dark-theme');
        root.setAttribute('data-theme', 'sun');
        console.log('✅ 已切换到浅色主题 (Sun模式)');
    }
    
    // 检查CSS变量是否正确应用
    const computedStyle = getComputedStyle(root);
    const textPrimary = computedStyle.getPropertyValue('--text-primary').trim();
    const bgPrimary = computedStyle.getPropertyValue('--bg-primary').trim();
    
    console.log(`主要文本颜色: ${textPrimary}`);
    console.log(`主要背景颜色: ${bgPrimary}`);
    
    return {
        theme: root.classList.contains('dark-theme') ? 'dark' : 'light',
        textColor: textPrimary,
        backgroundColor: bgPrimary
    };
}

// 检查AI消息元素的颜色
function checkAIMessageColors() {
    console.log('🔍 检查AI消息元素颜色...');
    
    const aiMessages = document.querySelectorAll('.ai-message-container');
    const markdownRenderers = document.querySelectorAll('.markdown-renderer');
    
    console.log(`找到 ${aiMessages.length} 个AI消息容器`);
    console.log(`找到 ${markdownRenderers.length} 个Markdown渲染器`);
    
    // 检查Markdown渲染器的颜色
    markdownRenderers.forEach((renderer, index) => {
        const computedStyle = getComputedStyle(renderer);
        const color = computedStyle.color;
        console.log(`Markdown渲染器 ${index + 1} 颜色: ${color}`);
    });
    
    // 检查AI消息气泡的颜色
    const messageBubbles = document.querySelectorAll('.message-bubble');
    messageBubbles.forEach((bubble, index) => {
        const computedStyle = getComputedStyle(bubble);
        const color = computedStyle.color;
        const backgroundColor = computedStyle.backgroundColor;
        console.log(`消息气泡 ${index + 1} - 文本颜色: ${color}, 背景颜色: ${backgroundColor}`);
    });
}

// 模拟AI消息内容测试
function simulateAIMessage() {
    console.log('🤖 模拟AI消息测试...');
    
    // 创建测试消息容器
    const testContainer = document.createElement('div');
    testContainer.className = 'ai-message-test-container';
    testContainer.style.cssText = `
        position: fixed;
        top: 20px;
        left: 20px;
        width: 400px;
        max-height: 300px;
        background: var(--bg-primary);
        border: 2px solid var(--border-primary);
        border-radius: 12px;
        padding: 16px;
        z-index: 9999;
        box-shadow: var(--shadow-lg);
        overflow-y: auto;
    `;
    
    testContainer.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
            <h3 style="color: var(--text-primary); margin: 0;">AI消息颜色测试</h3>
            <button onclick="this.parentElement.parentElement.remove()" 
                    style="background: var(--error-border); color: white; border: none; border-radius: 4px; padding: 4px 8px; cursor: pointer;">
                关闭
            </button>
        </div>
        <div class="markdown-renderer" style="color: var(--text-primary);">
            <h4 style="color: var(--text-primary); border-bottom: 1px solid var(--border-primary); padding-bottom: 4px;">测试标题</h4>
            <p style="color: var(--text-primary); margin: 8px 0;">这是一段测试文本，用于验证AI回复内容在不同主题下的显示效果。</p>
            <code style="background: var(--bg-tertiary); color: var(--text-primary); padding: 2px 4px; border-radius: 3px;">
                const test = "代码测试";
            </code>
            <blockquote style="border-left: 4px solid var(--accent-primary); background: var(--bg-secondary); color: var(--text-secondary); padding: 12px; margin: 12px 0; font-style: italic;">
                这是一个引用块测试
            </blockquote>
        </div>
    `;
    
    document.body.appendChild(testContainer);
    console.log('✅ 已创建测试AI消息容器');
    
    return testContainer;
}

// 完整的主题和颜色测试
function runFullTest() {
    console.log('🚀 开始完整的主题和颜色测试...');
    console.log('='.repeat(50));
    
    // 1. 测试浅色主题
    console.log('1️⃣ 测试浅色主题...');
    document.documentElement.classList.remove('dark-theme');
    document.documentElement.setAttribute('data-theme', 'sun');
    
    setTimeout(() => {
        checkAIMessageColors();
        const lightTestContainer = simulateAIMessage();
        lightTestContainer.style.left = '20px';
        
        // 2. 测试深色主题
        setTimeout(() => {
            console.log('\n2️⃣ 测试深色主题...');
            document.documentElement.classList.add('dark-theme');
            document.documentElement.setAttribute('data-theme', 'moon');
            
            setTimeout(() => {
                checkAIMessageColors();
                const darkTestContainer = simulateAIMessage();
                darkTestContainer.style.left = '440px';
                
                console.log('\n✅ 测试完成！');
                console.log('请检查页面上的两个测试容器，对比浅色和深色主题下的显示效果。');
                console.log('='.repeat(50));
            }, 500);
        }, 2000);
    }, 500);
}

// 导出测试函数到全局作用域
window.testThemeToggle = testThemeToggle;
window.checkAIMessageColors = checkAIMessageColors;
window.simulateAIMessage = simulateAIMessage;
window.runFullTest = runFullTest;

// 自动运行测试（如果在浏览器环境中）
if (typeof window !== 'undefined') {
    console.log('🎯 主题测试脚本已加载！');
    console.log('可用的测试函数:');
    console.log('- testThemeToggle(): 切换主题');
    console.log('- checkAIMessageColors(): 检查AI消息颜色');
    console.log('- simulateAIMessage(): 创建测试AI消息');
    console.log('- runFullTest(): 运行完整测试');
    console.log('\n💡 在控制台中输入函数名来运行测试，例如: runFullTest()');
}
