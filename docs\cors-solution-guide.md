# CORS跨域问题解决指南

## 问题背景

在前后端分离的开发环境中，当前端应用（如 `http://localhost:3000`）尝试访问不同域的后端API（如 `http://***********:3006`）时，浏览器会因为同源策略阻止这种跨域请求，导致CORS（Cross-Origin Resource Sharing）错误。

## 错误信息

```
Access to fetch at 'http://***********:3006/api/v1/chat/completions' from origin 'http://localhost:3000' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

## 解决方案

### 方案一：Vite开发代理（推荐）

#### 1. 配置Vite代理

在 `vite.config.js` 中添加代理配置：

```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  server: {
    host: '0.0.0.0',
    port: 3000,
    proxy: {
      // AI API代理
      '/api/v1': {
        target: 'http://***********:3006',
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('AI API代理错误:', err)
          })
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('AI API代理请求:', req.method, req.url)
          })
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('AI API代理响应:', proxyRes.statusCode, req.url)
          })
        }
      }
    }
  }
})
```

#### 2. 修改API配置

将AI服务的baseURL从完整URL改为代理路径：

```javascript
// 修改前
baseURL: 'http://***********:3006/api/v1'

// 修改后
baseURL: '/api/v1'
```

#### 3. 环境变量配置

创建 `.env.development` 文件：
```bash
# 开发环境配置
VITE_AI_BASE_URL=/api/v1
VITE_AI_API_KEY=your-api-key
VITE_AI_MODEL=Qwen3-235B-A22B
VITE_AI_ENABLE_MOCK=true
```

创建 `.env.production` 文件：
```bash
# 生产环境配置
VITE_AI_BASE_URL=http://***********:3006/api/v1
VITE_AI_API_KEY=your-api-key
VITE_AI_MODEL=Qwen3-235B-A22B
VITE_AI_ENABLE_MOCK=false
```

#### 4. 配置文件支持环境变量

修改 `src/config/ai.js`：
```javascript
// 从环境变量获取配置
const getEnvConfig = () => ({
  baseURL: import.meta.env.VITE_AI_BASE_URL || '/api/v1',
  apiKey: import.meta.env.VITE_AI_API_KEY || 'default-key',
  model: import.meta.env.VITE_AI_MODEL || 'default-model',
  enableMockMode: import.meta.env.VITE_AI_ENABLE_MOCK === 'true'
})

const envConfig = getEnvConfig()

export const knowledgeAIConfig = {
  baseURL: envConfig.baseURL,
  apiKey: envConfig.apiKey,
  model: envConfig.model,
  enableMockMode: envConfig.enableMockMode,
  // 其他配置...
}
```

### 方案二：后端CORS配置

如果需要在后端解决CORS问题，可以在API服务器中添加CORS头：

#### Express.js示例
```javascript
const express = require('express')
const cors = require('cors')
const app = express()

// 配置CORS
app.use(cors({
  origin: ['http://localhost:3000', 'http://127.0.0.1:3000'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}))

// 或者手动设置CORS头
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', 'http://localhost:3000')
  res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
  
  if (req.method === 'OPTIONS') {
    res.sendStatus(200)
  } else {
    next()
  }
})
```

### 方案三：Nginx反向代理

在生产环境中，可以使用Nginx作为反向代理：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /path/to/your/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/v1/ {
        proxy_pass http://***********:3006/api/v1/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS头
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
        
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }
}
```

## 测试验证

### 1. 启动开发服务器
```bash
npm run dev
```

### 2. 检查代理是否工作
在浏览器开发者工具的Network标签中，查看API请求：
- 请求URL应该是 `http://localhost:3000/api/v1/chat/completions`
- 而不是 `http://***********:3006/api/v1/chat/completions`

### 3. 查看控制台日志
Vite代理会在控制台输出代理请求的日志信息。

## 常见问题

### 1. 代理不生效
- 确保重启了开发服务器
- 检查vite.config.js语法是否正确
- 确认API路径配置正确

### 2. 生产环境CORS问题
- 生产环境需要后端配置CORS或使用Nginx代理
- 确保环境变量配置正确

### 3. 预检请求失败
- 确保后端支持OPTIONS请求
- 检查请求头配置是否正确

## 最佳实践

1. **开发环境**：使用Vite代理，简单高效
2. **生产环境**：使用Nginx反向代理或后端CORS配置
3. **环境变量**：使用环境变量管理不同环境的配置
4. **错误处理**：实现降级机制，如模拟模式
5. **日志记录**：记录代理请求日志便于调试

## 总结

通过Vite代理配置，我们可以在开发环境中完美解决CORS跨域问题，同时通过环境变量配置实现开发和生产环境的无缝切换。这种方案具有以下优势：

- ✅ 无需修改后端代码
- ✅ 开发体验良好
- ✅ 支持环境变量配置
- ✅ 支持错误处理和降级
- ✅ 易于维护和扩展
