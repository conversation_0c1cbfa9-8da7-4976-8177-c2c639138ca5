# SSO单点登录使用示例

## 快速开始

### 1. 启动服务

```bash
# 方法一：使用启动脚本（推荐）
start-sso-dev.bat    # Windows
./start-sso-dev.sh   # Linux/Mac

# 方法二：手动启动
npm run dev:full

# 方法三：分别启动
npm run sso-proxy    # 启动SSO代理服务器
npm run dev          # 启动前端应用
```

### 2. 验证服务状态

打开浏览器访问测试页面：
```
file:///path/to/your/project/sso-test.html
```

或者手动检查：
- 前端应用：http://localhost:3000
- SSO代理健康检查：http://localhost:4003/health

### 3. SSO登录测试

#### 使用测试URL
```
http://localhost:3000?tokenId=test-token-123
```

#### 使用真实SSO Token
```
http://localhost:3000?tokenId=YOUR_REAL_SSO_TOKEN
```

## 集成到现有系统

### Java后端集成示例

如果您有Java后端系统，可以这样生成SSO登录链接：

```java
@GetMapping("/generateSSOLink")
public String generateSSOLink(@RequestParam String tokenId) {
    String frontendUrl = "http://localhost:3000";
    return frontendUrl + "?tokenId=" + tokenId;
}
```

### 重定向到SSO登录

```java
@GetMapping("/ssoLogin")
public void ssoLogin(@RequestParam String tokenId, HttpServletResponse response) 
    throws IOException {
    String ssoUrl = "http://localhost:3000?tokenId=" + tokenId;
    response.sendRedirect(ssoUrl);
}
```

## 前端集成

### 检测SSO登录状态

```javascript
// 在Vue组件中检测用户登录状态
import { getUserService } from '@/services/userService.js'

const userService = getUserService()

// 检查当前用户
const currentUser = await userService.getCurrentUser()
if (currentUser) {
    console.log('用户已登录:', currentUser)
    // 用户信息包含：username, employeeId, loginType, loginTime等
}
```

### 监听登录事件

```vue
<template>
  <UserLogin 
    :force-login="true" 
    @login-success="handleLoginSuccess"
    @logout="handleLogout"
  />
</template>

<script setup>
const handleLoginSuccess = (userData) => {
  console.log('用户登录成功:', userData)
  // 处理登录成功后的逻辑
  if (userData.loginType === 'sso') {
    console.log('这是SSO登录用户')
  }
}

const handleLogout = () => {
  console.log('用户已退出登录')
  // 处理退出登录后的逻辑
}
</script>
```

## 自定义配置

### 修改SSO验证接口

编辑 `sso-proxy-server.js`：

```javascript
// SSO验证配置
const ssoConfig = {
  url: 'https://your-oa-system.com/sso/validate',
  accessKey: 'your-access-key'
}
```

### 修改端口配置

1. 修改 `sso-proxy-server.js` 中的端口：
```javascript
const PORT = 4003  // 改为您需要的端口
```

2. 修改 `vite.config.js` 中的代理配置：
```javascript
'/api/sso': {
  target: 'http://localhost:4003',  // 对应上面的端口
  // ...
}
```

### 自定义用户信息映射

编辑 `src/services/userService.js` 中的 `ssoLogin` 方法：

```javascript
// 构建用户数据（根据您的SSO响应格式调整）
const userData = {
  username: data.username || data.name || uid,
  employeeId: uid,
  department: data.department,  // 添加部门信息
  email: data.email,           // 添加邮箱信息
  loginType: 'sso',
  ssoTokenId: tokenId
}
```

## 错误处理

### 常见错误及解决方案

1. **端口占用错误**
```
Error: listen EADDRINUSE: address already in use 0.0.0.0:4003
```
解决：修改端口号或停止占用端口的进程

2. **CORS错误**
```
Access to fetch at 'https://oa.bodor.com:8443/...' from origin 'http://localhost:3000' has been blocked by CORS policy
```
解决：确保SSO代理服务器正在运行

3. **SSO验证失败**
```
SSO token验证失败，授权无效
```
解决：检查token是否有效，或联系系统管理员

### 调试模式

启用详细日志：

```javascript
// 在sso-proxy-server.js中添加
console.log('SSO请求详情:', {
  tokenId,
  url: ssoConfig.url,
  timestamp: new Date().toISOString()
})
```

## 生产环境部署

### 1. 构建前端应用
```bash
npm run build
```

### 2. 部署SSO代理服务器
```bash
# 使用PM2管理进程
npm install -g pm2
pm2 start sso-proxy-server.js --name "sso-proxy"
```

### 3. 配置反向代理（Nginx示例）
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /path/to/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # SSO代理
    location /api/sso/ {
        proxy_pass http://localhost:4003;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 安全注意事项

1. **HTTPS部署**：生产环境必须使用HTTPS
2. **Token安全**：tokenId仅用于一次性验证，验证后立即清除
3. **访问控制**：限制SSO代理服务器的访问来源
4. **日志记录**：记录SSO登录日志用于审计

## 技术支持

如果遇到问题，请检查：
1. SSO代理服务器是否正常运行
2. 网络是否可以访问OA系统
3. SSO token是否有效
4. 浏览器控制台是否有错误信息

更多详细信息请参考：
- [SSO集成指南](sso-integration-guide.md)
- [故障排除指南](troubleshooting.md)
