<template>
  <div class="responsive-test">
    <div class="test-header">
      <h2>响应式布局测试</h2>
      <p>当前设备信息和断点测试</p>
    </div>

    <div class="device-info">
      <h3>设备信息</h3>
      <div class="info-grid">
        <div class="info-item">
          <label>窗口宽度:</label>
          <span>{{ windowWidth }}px</span>
        </div>
        <div class="info-item">
          <label>窗口高度:</label>
          <span>{{ windowHeight }}px</span>
        </div>
        <div class="info-item">
          <label>设备类型:</label>
          <span>{{ deviceType }}</span>
        </div>
        <div class="info-item">
          <label>屏幕方向:</label>
          <span>{{ orientation }}</span>
        </div>
      </div>
    </div>

    <div class="breakpoint-test">
      <h3>断点测试</h3>
      <div class="breakpoint-grid">
        <div class="breakpoint-item" :class="{ active: isXs }">
          <span class="label">XS (&lt;320px)</span>
          <span class="status">{{ isXs ? '✓' : '✗' }}</span>
        </div>
        <div class="breakpoint-item" :class="{ active: isSm }">
          <span class="label">SM (320-480px)</span>
          <span class="status">{{ isSm ? '✓' : '✗' }}</span>
        </div>
        <div class="breakpoint-item" :class="{ active: isMd }">
          <span class="label">MD (480-768px)</span>
          <span class="status">{{ isMd ? '✓' : '✗' }}</span>
        </div>
        <div class="breakpoint-item" :class="{ active: isLg }">
          <span class="label">LG (768-1024px)</span>
          <span class="status">{{ isLg ? '✓' : '✗' }}</span>
        </div>
        <div class="breakpoint-item" :class="{ active: isXl }">
          <span class="label">XL (1024-1200px)</span>
          <span class="status">{{ isXl ? '✓' : '✗' }}</span>
        </div>
        <div class="breakpoint-item" :class="{ active: is2Xl }">
          <span class="label">2XL (&gt;1200px)</span>
          <span class="status">{{ is2Xl ? '✓' : '✗' }}</span>
        </div>
      </div>
    </div>

    <div class="legacy-test">
      <h3>兼容性测试</h3>
      <div class="legacy-grid">
        <div class="legacy-item" :class="{ active: isMobile }">
          <span class="label">移动端 (&lt;768px)</span>
          <span class="status">{{ isMobile ? '✓' : '✗' }}</span>
        </div>
        <div class="legacy-item" :class="{ active: isTablet }">
          <span class="label">平板端 (768-1024px)</span>
          <span class="status">{{ isTablet ? '✓' : '✗' }}</span>
        </div>
        <div class="legacy-item" :class="{ active: isDesktop }">
          <span class="label">桌面端 (&gt;1024px)</span>
          <span class="status">{{ isDesktop ? '✓' : '✗' }}</span>
        </div>
      </div>
    </div>

    <div class="touch-test" v-if="isMobile">
      <h3>触摸手势测试</h3>
      <div 
        class="touch-area"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
      >
        <p>在此区域进行触摸手势测试</p>
        <div v-if="gestureResult.type" class="gesture-result">
          <p><strong>手势类型:</strong> {{ gestureResult.type }}</p>
          <p v-if="gestureResult.direction"><strong>方向:</strong> {{ gestureResult.direction }}</p>
          <p><strong>距离:</strong> {{ Math.round(gestureResult.distance) }}px</p>
          <p><strong>持续时间:</strong> {{ gestureResult.duration }}ms</p>
        </div>
      </div>
    </div>

    <div class="css-variables-test">
      <h3>CSS变量测试</h3>
      <div class="variables-grid">
        <div class="variable-item">
          <span class="var-name">--font-size-base</span>
          <span class="var-value" :style="{ fontSize: 'var(--font-size-base)' }">示例文本</span>
        </div>
        <div class="variable-item">
          <span class="var-name">--spacing-md</span>
          <div class="var-value spacing-demo" :style="{ padding: 'var(--spacing-md)' }">间距示例</div>
        </div>
        <div class="variable-item">
          <span class="var-name">--border-radius-md</span>
          <div class="var-value radius-demo" :style="{ borderRadius: 'var(--border-radius-md)' }">圆角示例</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useResponsive } from '../../composables/useResponsive.js'
import { useTouchGestures } from '../../composables/useTouchGestures.js'

// 响应式检测
const {
  windowWidth,
  windowHeight,
  deviceType,
  orientation,
  isXs,
  isSm,
  isMd,
  isLg,
  isXl,
  is2Xl,
  isMobile,
  isTablet,
  isDesktop
} = useResponsive()

// 触摸手势
const touchGestures = useTouchGestures({
  threshold: 30,
  timeThreshold: 500
})

const { gestureResult } = touchGestures

// 触摸事件处理
const handleTouchStart = (event) => {
  touchGestures.handleTouchStart(event)
}

const handleTouchMove = (event) => {
  touchGestures.handleTouchMove(event)
}

const handleTouchEnd = (event) => {
  touchGestures.handleTouchEnd(event)
}
</script>

<style scoped>
.responsive-test {
  padding: var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.test-header h2 {
  font-size: var(--font-size-2xl);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.test-header p {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
}

.device-info,
.breakpoint-test,
.legacy-test,
.touch-test,
.css-variables-test {
  background: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-primary);
}

.device-info h3,
.breakpoint-test h3,
.legacy-test h3,
.touch-test h3,
.css-variables-test h3 {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.info-grid,
.breakpoint-grid,
.legacy-grid,
.variables-grid {
  display: grid;
  gap: var(--spacing-md);
}

.info-grid {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.breakpoint-grid,
.legacy-grid {
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
}

.variables-grid {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.info-item,
.breakpoint-item,
.legacy-item,
.variable-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  background: var(--bg-primary);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-primary);
}

.breakpoint-item.active,
.legacy-item.active {
  background: var(--accent-secondary);
  border-color: var(--accent-primary);
}

.status {
  font-weight: bold;
}

.breakpoint-item.active .status,
.legacy-item.active .status {
  color: var(--accent-primary);
}

.touch-area {
  background: var(--bg-primary);
  border: 2px dashed var(--border-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  user-select: none;
  touch-action: none;
}

.gesture-result {
  margin-top: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--accent-secondary);
  border-radius: var(--border-radius-md);
  text-align: left;
}

.gesture-result p {
  margin: var(--spacing-xs) 0;
  font-size: var(--font-size-sm);
}

.var-name {
  font-family: monospace;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.spacing-demo {
  background: var(--accent-secondary);
  border-radius: var(--border-radius-sm);
}

.radius-demo {
  background: var(--accent-secondary);
  padding: var(--spacing-sm);
}

/* 响应式适配 */
@media (max-width: 768px) {
  .responsive-test {
    padding: var(--spacing-md);
  }

  .info-grid,
  .breakpoint-grid,
  .legacy-grid,
  .variables-grid {
    grid-template-columns: 1fr;
  }

  .touch-area {
    min-height: 150px;
    padding: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .responsive-test {
    padding: var(--spacing-sm);
  }

  .device-info,
  .breakpoint-test,
  .legacy-test,
  .touch-test,
  .css-variables-test {
    padding: var(--spacing-md);
  }
}
</style>
