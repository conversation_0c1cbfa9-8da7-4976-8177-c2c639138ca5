<template>
  <div class="watermark-test">
    <div class="test-header">
      <h1>动态水印功能测试</h1>
      <div class="test-controls">
        <button @click="testLogin" class="btn btn-primary">模拟登录</button>
        <button @click="testLogout" class="btn btn-secondary">模拟退出</button>
        <button @click="refreshAllWatermarks" class="btn btn-success">刷新水印</button>
        <button @click="runTests" class="btn btn-info">运行测试</button>
      </div>
    </div>

    <div class="test-info">
      <div class="user-info">
        <h3>当前用户信息</h3>
        <p v-if="currentUser">
          用户名: {{ currentUser.username }}<br>
          工号: {{ currentUser.employeeId }}<br>
          登录时间: {{ currentUser.loginTime }}
        </p>
        <p v-else>未登录</p>
      </div>

      <div class="watermark-info">
        <h3>水印文本预览</h3>
        <div class="watermark-preview">
          <div class="preview-item">
            <strong>知识中心:</strong> {{ watermarkTexts.knowledge || '加载中...' }}
          </div>
          <div class="preview-item">
            <strong>业务域:</strong> {{ watermarkTexts.business || '加载中...' }}
          </div>
          <div class="preview-item">
            <strong>职能域:</strong> {{ watermarkTexts.function || '加载中...' }}
          </div>
        </div>
      </div>
    </div>

    <div class="test-modules">
      <div class="module-section">
        <h3>知识中心水印测试</h3>
        <SimpleWatermark module="knowledge" ref="knowledgeWatermark">
          <div class="test-content knowledge-content">
            <h4>知识中心内容区域</h4>
            <p>这里是知识中心的内容，应该显示动态水印：用户名-工号-时间</p>
            <p>水印应该包含当前登录用户的信息和实时时间。</p>
          </div>
        </SimpleWatermark>
      </div>

      <div class="module-section">
        <h3>业务域水印测试</h3>
        <SimpleWatermark module="business" ref="businessWatermark">
          <div class="test-content business-content">
            <h4>业务域内容区域</h4>
            <p>这里是业务域的内容，应该显示动态水印：用户名-工号-时间</p>
            <p>水印颜色和样式与知识中心不同。</p>
          </div>
        </SimpleWatermark>
      </div>

      <div class="module-section">
        <h3>职能域水印测试</h3>
        <SimpleWatermark module="function" ref="functionWatermark">
          <div class="test-content function-content">
            <h4>职能域内容区域</h4>
            <p>这里是职能域的内容，应该显示动态水印：用户名-工号-时间</p>
            <p>职能域的水印没有动画效果。</p>
          </div>
        </SimpleWatermark>
      </div>
    </div>

    <div class="test-log">
      <h3>测试日志</h3>
      <div class="log-content" ref="logContent">
        <div v-for="(log, index) in testLogs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-level" :class="log.level">{{ log.level }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import SimpleWatermark from '../common/SimpleWatermark.vue'
import { getUserService } from '../../services/userService.js'
import { 
  generateDynamicWatermarkText, 
  getDynamicWatermarkConfig,
  refreshAllDynamicWatermarks 
} from '../../config/watermark.js'

// 响应式数据
const currentUser = ref(null)
const watermarkTexts = ref({
  knowledge: '',
  business: '',
  function: ''
})
const testLogs = ref([])

// 组件引用
const knowledgeWatermark = ref(null)
const businessWatermark = ref(null)
const functionWatermark = ref(null)
const logContent = ref(null)

/**
 * 添加测试日志
 */
const addLog = (level, message) => {
  const now = new Date()
  const time = now.toLocaleTimeString('zh-CN')
  testLogs.value.unshift({ time, level, message })
  
  // 限制日志数量
  if (testLogs.value.length > 50) {
    testLogs.value = testLogs.value.slice(0, 50)
  }
  
  // 滚动到顶部
  nextTick(() => {
    if (logContent.value) {
      logContent.value.scrollTop = 0
    }
  })
}

/**
 * 更新当前用户信息
 */
const updateCurrentUser = async () => {
  try {
    const userService = getUserService()
    currentUser.value = await userService.getCurrentUser()
    addLog('INFO', `用户信息更新: ${currentUser.value ? currentUser.value.username : '未登录'}`)
  } catch (error) {
    addLog('ERROR', `获取用户信息失败: ${error.message}`)
  }
}

/**
 * 更新水印文本预览
 */
const updateWatermarkTexts = async () => {
  try {
    const modules = ['knowledge', 'business', 'function']
    const prefixes = ['知识中心', '业务域', '职能域']
    
    for (let i = 0; i < modules.length; i++) {
      const text = await generateDynamicWatermarkText(prefixes[i])
      watermarkTexts.value[modules[i]] = text
    }
    
    addLog('INFO', '水印文本预览已更新')
  } catch (error) {
    addLog('ERROR', `更新水印文本失败: ${error.message}`)
  }
}

/**
 * 刷新所有水印组件
 */
const refreshWatermarkComponents = async () => {
  try {
    const components = [knowledgeWatermark.value, businessWatermark.value, functionWatermark.value]
    const promises = components.map(comp => comp?.refreshWatermark?.())
    await Promise.all(promises.filter(p => p))
    addLog('SUCCESS', '所有水印组件已刷新')
  } catch (error) {
    addLog('ERROR', `刷新水印组件失败: ${error.message}`)
  }
}

/**
 * 模拟用户登录
 */
const testLogin = async () => {
  try {
    const userService = getUserService()
    const userData = {
      username: '测试用户',
      employeeId: 'TEST001'
    }
    
    await userService.login(userData)
    addLog('SUCCESS', `用户登录成功: ${userData.username}`)
    
    // 更新用户信息和水印
    await updateCurrentUser()
    await updateWatermarkTexts()
    await refreshWatermarkComponents()
  } catch (error) {
    addLog('ERROR', `用户登录失败: ${error.message}`)
  }
}

/**
 * 模拟用户退出
 */
const testLogout = async () => {
  try {
    const userService = getUserService()
    await userService.logout()
    addLog('SUCCESS', '用户退出成功')
    
    // 更新用户信息和水印
    await updateCurrentUser()
    await updateWatermarkTexts()
    await refreshWatermarkComponents()
  } catch (error) {
    addLog('ERROR', `用户退出失败: ${error.message}`)
  }
}

/**
 * 刷新所有水印
 */
const refreshAllWatermarks = async () => {
  try {
    addLog('INFO', '开始刷新所有水印...')
    
    // 刷新配置
    await refreshAllDynamicWatermarks()
    
    // 更新预览
    await updateWatermarkTexts()
    
    // 刷新组件
    await refreshWatermarkComponents()
    
    addLog('SUCCESS', '所有水印刷新完成')
  } catch (error) {
    addLog('ERROR', `刷新水印失败: ${error.message}`)
  }
}

/**
 * 运行自动化测试
 */
const runTests = async () => {
  addLog('INFO', '开始运行自动化测试...')
  
  try {
    // 测试1：未登录状态
    addLog('INFO', '测试1: 未登录状态水印')
    await testLogout()
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 测试2：登录状态
    addLog('INFO', '测试2: 登录状态水印')
    await testLogin()
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 测试3：刷新水印
    addLog('INFO', '测试3: 刷新水印功能')
    await refreshAllWatermarks()
    
    addLog('SUCCESS', '所有自动化测试完成')
  } catch (error) {
    addLog('ERROR', `自动化测试失败: ${error.message}`)
  }
}

// 组件挂载时初始化
onMounted(async () => {
  addLog('INFO', '水印测试页面已加载')
  await updateCurrentUser()
  await updateWatermarkTexts()
})
</script>

<style scoped>
.watermark-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  margin-bottom: 30px;
  text-align: center;
}

.test-header h1 {
  color: #2d3748;
  margin-bottom: 20px;
}

.test-controls {
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-primary { background: #3b82f6; color: white; }
.btn-secondary { background: #6b7280; color: white; }
.btn-success { background: #10b981; color: white; }
.btn-info { background: #06b6d4; color: white; }

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.test-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.user-info, .watermark-info {
  padding: 20px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.user-info h3, .watermark-info h3 {
  margin-top: 0;
  color: #374151;
}

.watermark-preview {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-item {
  padding: 8px;
  background: white;
  border-radius: 4px;
  border: 1px solid #d1d5db;
  font-family: monospace;
  font-size: 14px;
}

.test-modules {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.module-section {
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.module-section h3 {
  margin: 0;
  padding: 15px 20px;
  background: #f1f5f9;
  color: #374151;
  border-bottom: 1px solid #e2e8f0;
}

.test-content {
  height: 200px;
  padding: 20px;
  position: relative;
}

.knowledge-content { background: #eff6ff; }
.business-content { background: #f0fdf4; }
.function-content { background: #faf5ff; }

.test-log {
  margin-top: 30px;
}

.test-log h3 {
  color: #374151;
  margin-bottom: 15px;
}

.log-content {
  height: 300px;
  overflow-y: auto;
  background: #1f2937;
  border-radius: 8px;
  padding: 15px;
  font-family: monospace;
  font-size: 13px;
}

.log-item {
  display: flex;
  gap: 10px;
  margin-bottom: 5px;
  align-items: center;
}

.log-time {
  color: #9ca3af;
  min-width: 80px;
}

.log-level {
  min-width: 60px;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: bold;
  text-align: center;
}

.log-level.INFO { background: #3b82f6; color: white; }
.log-level.SUCCESS { background: #10b981; color: white; }
.log-level.ERROR { background: #ef4444; color: white; }

.log-message {
  color: #f3f4f6;
  flex: 1;
}

@media (max-width: 768px) {
  .test-info {
    grid-template-columns: 1fr;
  }
  
  .test-modules {
    grid-template-columns: 1fr;
  }
  
  .test-controls {
    flex-direction: column;
    align-items: center;
  }
}
</style>
