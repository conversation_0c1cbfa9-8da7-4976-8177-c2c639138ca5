/**
 * 会话数据迁移工具
 * 提供从LocalStorage到Redis的数据迁移功能
 */

import sessionStorageService from '../services/sessionStorageService.js'
import { getRedisSessionStorageService } from '../services/redisSessionStorageService.js'

/**
 * 会话迁移工具类
 */
class SessionMigrationTool {
  constructor() {
    this.localStorageService = sessionStorageService
    this.redisStorageService = null
    this.migrationStatus = {
      inProgress: false,
      completed: false,
      error: null,
      startTime: null,
      endTime: null,
      migratedSessions: 0,
      totalSessions: 0,
      details: []
    }
  }

  /**
   * 初始化Redis存储服务
   */
  async initializeRedisService() {
    if (!this.redisStorageService) {
      this.redisStorageService = await getRedisSessionStorageService()
    }
    return this.redisStorageService
  }

  /**
   * 检查迁移前置条件
   */
  async checkMigrationPrerequisites() {
    const checks = {
      localStorageAvailable: false,
      localStorageHasData: false,
      redisAvailable: false,
      redisConnected: false,
      canProceed: false,
      details: [],
      warnings: []
    }

    try {
      // 检查LocalStorage可用性
      try {
        checks.localStorageAvailable = this.localStorageService.checkStorageSupport()
        if (checks.localStorageAvailable) {
          checks.details.push('✅ LocalStorage可用')
        } else {
          checks.details.push('❌ LocalStorage不可用')
        }
      } catch (error) {
        checks.localStorageAvailable = false
        checks.details.push(`❌ LocalStorage检查失败: ${error.message}`)
      }

      if (checks.localStorageAvailable) {
        // 检查LocalStorage是否有数据
        try {
          const localData = this.localStorageService.loadAllSessions()
          checks.localStorageHasData = !!(localData && localData.moduleSessions &&
            Object.values(localData.moduleSessions).some(sessions => sessions.length > 0))

          if (checks.localStorageHasData) {
            const sessionCount = Object.values(localData.moduleSessions)
              .reduce((total, sessions) => total + sessions.length, 0)
            checks.details.push(`✅ LocalStorage有数据 (${sessionCount}个会话)`)
          } else {
            checks.details.push('⚠️ LocalStorage无会话数据')
            checks.warnings.push('没有找到需要迁移的会话数据')
          }
        } catch (error) {
          checks.localStorageHasData = false
          checks.details.push(`❌ LocalStorage数据检查失败: ${error.message}`)
        }
      }

      // 检查Redis可用性
      try {
        await this.initializeRedisService()
        const redisStatus = this.redisStorageService.getConnectionStatus()
        checks.redisAvailable = redisStatus.supported
        checks.redisConnected = redisStatus.connected

        if (checks.redisAvailable) {
          checks.details.push('✅ Redis服务可用')
        } else {
          checks.details.push('❌ Redis服务不可用')
        }

        if (checks.redisConnected) {
          checks.details.push('✅ Redis连接正常')
        } else {
          checks.details.push('❌ Redis连接失败')
        }
      } catch (error) {
        checks.redisAvailable = false
        checks.redisConnected = false
        checks.details.push(`❌ Redis检查失败: ${error.message}`)
      }

      // 判断是否可以进行迁移
      // 放宽条件：只要Redis可用就可以迁移，即使没有LocalStorage数据
      checks.canProceed = checks.redisAvailable && checks.redisConnected

      if (!checks.canProceed) {
        if (!checks.redisAvailable) {
          checks.details.push('❌ 迁移失败：Redis服务不可用')
        } else if (!checks.redisConnected) {
          checks.details.push('❌ 迁移失败：Redis连接失败')
        }
      } else {
        if (!checks.localStorageHasData) {
          checks.details.push('ℹ️ 可以迁移，但没有数据需要迁移')
        } else {
          checks.details.push('✅ 满足迁移条件')
        }
      }

      return checks
    } catch (error) {
      console.error('检查迁移前置条件失败:', error)
      checks.error = error.message
      checks.details.push(`❌ 检查过程出错: ${error.message}`)
      return checks
    }
  }

  /**
   * 执行数据迁移
   */
  async performMigration(options = {}) {
    const {
      createBackup = true,
      clearLocalAfterMigration = false,
      validateAfterMigration = true
    } = options

    // 重置迁移状态
    this.resetMigrationStatus()
    this.migrationStatus.inProgress = true
    this.migrationStatus.startTime = new Date()

    try {
      console.log('开始会话数据迁移...')
      this.addMigrationDetail('开始迁移过程', 'info')

      // 检查前置条件
      const prerequisites = await this.checkMigrationPrerequisites()
      if (!prerequisites.canProceed) {
        const errorDetails = prerequisites.details.join('\n')
        throw new Error(`迁移前置条件不满足:\n${errorDetails}`)
      }

      this.addMigrationDetail('前置条件检查通过', 'success')

      // 加载LocalStorage数据
      const localData = this.localStorageService.loadAllSessions()

      // 处理没有数据的情况
      if (!localData || !localData.moduleSessions) {
        this.addMigrationDetail('LocalStorage中没有会话数据，创建空的Redis存储结构', 'info')

        // 创建空的数据结构
        const emptyData = {
          moduleSessions: {
            knowledge: [],
            business: [],
            function: []
          },
          moduleCurrentSession: {
            knowledge: null,
            business: null,
            function: null
          }
        }

        // 初始化Redis存储
        const initResult = await this.redisStorageService.saveAllSessions(
          emptyData.moduleSessions,
          emptyData.moduleCurrentSession
        )

        if (initResult) {
          this.migrationStatus.completed = true
          this.migrationStatus.endTime = new Date()
          this.addMigrationDetail('Redis存储结构初始化完成', 'success')

          return {
            success: true,
            migratedSessions: 0,
            totalSessions: 0,
            duration: this.migrationStatus.endTime - this.migrationStatus.startTime
          }
        } else {
          throw new Error('初始化Redis存储结构失败')
        }
      }

      // 统计会话数量
      this.migrationStatus.totalSessions = Object.values(localData.moduleSessions)
        .reduce((total, sessions) => total + sessions.length, 0)

      this.addMigrationDetail(`发现${this.migrationStatus.totalSessions}个会话需要迁移`, 'info')

      // 创建备份（如果需要）
      if (createBackup) {
        const backupKey = await this.redisStorageService.createBackup('pre_migration_backup')
        if (backupKey) {
          this.addMigrationDetail(`已创建迁移前备份: ${backupKey}`, 'success')
        } else {
          this.addMigrationDetail('创建备份失败，但继续迁移', 'warning')
        }
      }

      // 执行迁移
      const migrationResult = await this.redisStorageService.saveAllSessions(
        localData.moduleSessions, 
        localData.moduleCurrentSession
      )

      if (!migrationResult) {
        throw new Error('保存数据到Redis失败')
      }

      this.migrationStatus.migratedSessions = this.migrationStatus.totalSessions
      this.addMigrationDetail('数据已成功保存到Redis', 'success')

      // 验证迁移结果（如果需要）
      if (validateAfterMigration) {
        const validationResult = await this.validateMigration(localData)
        if (validationResult.success) {
          this.addMigrationDetail('迁移数据验证通过', 'success')
        } else {
          this.addMigrationDetail(`迁移数据验证失败: ${validationResult.error}`, 'error')
          throw new Error(`数据验证失败: ${validationResult.error}`)
        }
      }

      // 清除LocalStorage数据（如果需要）
      if (clearLocalAfterMigration) {
        const clearResult = this.localStorageService.clearAllData()
        if (clearResult) {
          this.addMigrationDetail('已清除LocalStorage中的旧数据', 'success')
        } else {
          this.addMigrationDetail('清除LocalStorage数据失败', 'warning')
        }
      }

      // 迁移完成
      this.migrationStatus.completed = true
      this.migrationStatus.endTime = new Date()
      this.addMigrationDetail('数据迁移完成', 'success')

      console.log('会话数据迁移成功完成')
      return {
        success: true,
        migratedSessions: this.migrationStatus.migratedSessions,
        totalSessions: this.migrationStatus.totalSessions,
        duration: this.migrationStatus.endTime - this.migrationStatus.startTime
      }

    } catch (error) {
      this.migrationStatus.error = error.message
      this.migrationStatus.endTime = new Date()
      this.addMigrationDetail(`迁移失败: ${error.message}`, 'error')
      
      console.error('会话数据迁移失败:', error)
      return {
        success: false,
        error: error.message,
        migratedSessions: this.migrationStatus.migratedSessions,
        totalSessions: this.migrationStatus.totalSessions
      }
    } finally {
      this.migrationStatus.inProgress = false
    }
  }

  /**
   * 验证迁移结果
   */
  async validateMigration(originalData) {
    try {
      // 从Redis加载数据
      const redisData = await this.redisStorageService.loadAllSessions()
      
      if (!redisData) {
        return { success: false, error: '无法从Redis加载数据' }
      }

      // 比较会话数量
      const originalSessionCount = Object.values(originalData.moduleSessions)
        .reduce((total, sessions) => total + sessions.length, 0)
      const redisSessionCount = Object.values(redisData.moduleSessions)
        .reduce((total, sessions) => total + sessions.length, 0)

      if (originalSessionCount !== redisSessionCount) {
        return { 
          success: false, 
          error: `会话数量不匹配: 原始${originalSessionCount}, Redis${redisSessionCount}` 
        }
      }

      // 验证每个模块的会话
      for (const [moduleKey, originalSessions] of Object.entries(originalData.moduleSessions)) {
        const redisSessions = redisData.moduleSessions[moduleKey] || []
        
        if (originalSessions.length !== redisSessions.length) {
          return { 
            success: false, 
            error: `模块${moduleKey}会话数量不匹配: 原始${originalSessions.length}, Redis${redisSessions.length}` 
          }
        }

        // 验证会话ID
        const originalIds = new Set(originalSessions.map(s => s.id))
        const redisIds = new Set(redisSessions.map(s => s.id))
        
        for (const id of originalIds) {
          if (!redisIds.has(id)) {
            return { 
              success: false, 
              error: `模块${moduleKey}中缺少会话ID: ${id}` 
            }
          }
        }
      }

      return { success: true }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  /**
   * 获取迁移状态
   */
  getMigrationStatus() {
    return { ...this.migrationStatus }
  }

  /**
   * 重置迁移状态
   */
  resetMigrationStatus() {
    this.migrationStatus = {
      inProgress: false,
      completed: false,
      error: null,
      startTime: null,
      endTime: null,
      migratedSessions: 0,
      totalSessions: 0,
      details: []
    }
  }

  /**
   * 添加迁移详情
   */
  addMigrationDetail(message, type = 'info') {
    this.migrationStatus.details.push({
      timestamp: new Date(),
      message,
      type
    })
    console.log(`[迁移${type.toUpperCase()}] ${message}`)
  }

  /**
   * 获取迁移报告
   */
  getMigrationReport() {
    const status = this.getMigrationStatus()
    const duration = status.endTime && status.startTime ? 
      status.endTime - status.startTime : 0

    return {
      success: status.completed && !status.error,
      duration,
      migratedSessions: status.migratedSessions,
      totalSessions: status.totalSessions,
      migrationRate: status.totalSessions > 0 ? 
        (status.migratedSessions / status.totalSessions * 100).toFixed(2) + '%' : '0%',
      error: status.error,
      details: status.details,
      startTime: status.startTime,
      endTime: status.endTime
    }
  }

  /**
   * 导出迁移报告为JSON
   */
  exportMigrationReport() {
    const report = this.getMigrationReport()
    const blob = new Blob([JSON.stringify(report, null, 2)], { 
      type: 'application/json' 
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `session-migration-report-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }
}

// 创建单例实例
let migrationTool = null

/**
 * 获取会话迁移工具实例
 */
export const getSessionMigrationTool = () => {
  if (!migrationTool) {
    migrationTool = new SessionMigrationTool()
  }
  return migrationTool
}

export default SessionMigrationTool
