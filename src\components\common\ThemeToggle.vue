<template>
  <!-- 主题切换组件 -->
  <div class="theme-toggle">
    <button
      class="theme-button"
      @click="toggleTheme"
      :title="isDark ? '切换到Sun模式' : '切换到Moon模式'"
      :aria-label="isDark ? '切换到Sun模式' : '切换到Moon模式'"
    >
      <transition name="theme-icon" mode="out-in">
        <SunIcon v-if="isDark" key="sun" />
        <MoonIcon v-else key="moon" />
      </transition>
    </button>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { forceApplyTheme } from '../../utils/themeForcer.js'

// 图标组件
const SunIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 7c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5-2.24-5-5-5zM2 13h2c.55 0 1-.45 1-1s-.45-1-1-1H2c-.55 0-1 .45-1 1s.45 1 1 1zm18 0h2c.55 0 1-.45 1-1s-.45-1-1-1h-2c-.55 0-1 .45-1 1s.45 1 1 1zM11 2v2c0 .55.45 1 1 1s1-.45 1-1V2c0-.55-.45-1-1-1s-1 .45-1 1zm0 18v2c0 .55.45 1 1 1s1-.45 1-1v-2c0-.55-.45-1-1-1s-1 .45-1 1zM5.99 4.58c-.39-.39-1.03-.39-1.41 0-.39.39-.39 1.03 0 1.41l1.06 1.06c.39.39 1.03.39 1.41 0s.39-1.03 0-1.41L5.99 4.58zm12.37 12.37c-.39-.39-1.03-.39-1.41 0-.39.39-.39 1.03 0 1.41l1.06 1.06c.39.39 1.03.39 1.41 0 .39-.39.39-1.03 0-1.41l-1.06-1.06zm1.06-10.96c.39-.39.39-1.03 0-1.41-.39-.39-1.03-.39-1.41 0l-1.06 1.06c-.39.39-.39 1.03 0 1.41s1.03.39 1.41 0l1.06-1.06zM7.05 18.36c.39-.39.39-1.03 0-1.41-.39-.39-1.03-.39-1.41 0l-1.06 1.06c-.39.39-.39 1.03 0 1.41s1.03.39 1.41 0l1.06-1.06z"/>
    </svg>
  `
}

const MoonIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M12.34 2.02C6.59 1.82 2 6.42 2 12c0 5.52 4.48 10 10 10 3.71 0 6.93-2.02 8.66-5.02-7.51-.25-12.09-8.43-8.32-14.96z"/>
    </svg>
  `
}

// 响应式数据
const isDark = ref(false)

/**
 * 切换主题
 */
const toggleTheme = () => {
  isDark.value = !isDark.value

  // 使用强制主题应用器确保兼容性
  forceApplyTheme(isDark.value)

  // 保存主题偏好
  saveThemePreference(isDark.value)

  console.log('切换主题:', isDark.value ? 'Moon模式' : 'Sun模式')
}

/**
 * 强制更新AI消息样式 - 解决浏览器兼容性问题
 * @param {boolean} dark - 是否为深色主题
 */
const forceUpdateAIMessages = (dark) => {
  // 查找所有AI消息相关元素并强制更新样式
  const aiElements = [
    '.markdown-renderer',
    '.ai-content-wrapper',
    '.message-bubble',
    '.ai-message-container'
  ]

  aiElements.forEach(selector => {
    const elements = document.querySelectorAll(selector)
    elements.forEach(element => {
      if (dark) {
        element.style.color = '#f9fafb'
        if (selector === '.message-bubble') {
          element.style.backgroundColor = '#1f2937'
          element.style.borderColor = '#374151'
        }
      } else {
        element.style.color = '#1f2937'
        if (selector === '.message-bubble') {
          element.style.backgroundColor = '#f8fafc'
          element.style.borderColor = '#e2e8f0'
        }
      }
    })
  })

  // 强制更新Markdown内容元素
  const markdownElements = document.querySelectorAll('.markdown-renderer p, .markdown-renderer h1, .markdown-renderer h2, .markdown-renderer h3, .markdown-renderer h4, .markdown-renderer h5, .markdown-renderer h6, .markdown-renderer li, .markdown-renderer strong, .markdown-renderer em')
  markdownElements.forEach(element => {
    element.style.color = dark ? '#f9fafb' : '#1f2937'
  })
}

/**
 * 应用主题
 * @param {boolean} dark - 是否为深色主题
 */
const applyTheme = (dark) => {
  const root = document.documentElement

  if (dark) {
    root.setAttribute('data-theme', 'moon')
    root.classList.add('dark-theme')
    root.classList.remove('light-theme')
  } else {
    root.setAttribute('data-theme', 'sun')
    root.classList.add('light-theme')
    root.classList.remove('dark-theme')
  }

  // 强制刷新CSS变量 - 解决某些浏览器的主题切换延迟问题
  setTimeout(() => {
    forceRefreshTheme(dark)
  }, 50)
}

/**
 * 强制刷新主题 - 解决浏览器兼容性问题
 * @param {boolean} dark - 是否为深色主题
 */
const forceRefreshTheme = (dark) => {
  const root = document.documentElement

  // 临时移除所有主题类，然后重新添加
  root.classList.remove('dark-theme', 'light-theme')

  // 使用 requestAnimationFrame 确保DOM更新
  requestAnimationFrame(() => {
    if (dark) {
      root.classList.add('dark-theme')
      root.setAttribute('data-theme', 'moon')
    } else {
      root.classList.add('light-theme')
      root.setAttribute('data-theme', 'sun')
    }

    // 触发重绘
    root.style.display = 'none'
    root.offsetHeight // 触发reflow
    root.style.display = ''
  })
}

/**
 * 保存主题偏好
 * @param {boolean} dark - 是否为深色主题
 */
const saveThemePreference = (dark) => {
  localStorage.setItem('theme-preference', dark ? 'moon' : 'sun')
}

/**
 * 加载主题偏好
 */
const loadThemePreference = () => {
  const saved = localStorage.getItem('theme-preference')

  if (saved) {
    isDark.value = saved === 'moon'
  } else {
    // 检测系统主题偏好
    isDark.value = window.matchMedia('(prefers-color-scheme: dark)').matches
  }

  applyTheme(isDark.value)
}

// 监听系统主题变化
const watchSystemTheme = () => {
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  
  mediaQuery.addEventListener('change', (e) => {
    // 只有在用户没有手动设置主题时才跟随系统
    if (!localStorage.getItem('theme-preference')) {
      isDark.value = e.matches
      applyTheme(isDark.value)
    }
  })
}

// 组件挂载时初始化
onMounted(() => {
  loadThemePreference()
  watchSystemTheme()
})
</script>

<style scoped>
/* 主题切换按钮 */
.theme-toggle {
  position: relative;
}

.theme-button {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background-color: var(--theme-toggle-bg, #f3f4f6);
  color: var(--theme-toggle-color, #374151);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  outline: none !important;
}

.theme-button:focus {
  outline: none !important;
  border: none !important;
}

.theme-button:active {
  outline: none !important;
  border: none !important;
}

.theme-button:hover {
  background-color: var(--theme-toggle-hover-bg, #e5e7eb);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.theme-button:active {
  transform: translateY(0);
}

.theme-button svg {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}

/* 图标切换动画 */
.theme-icon-enter-active,
.theme-icon-leave-active {
  transition: all 0.3s ease;
}

.theme-icon-enter-from {
  opacity: 0;
  transform: rotate(-90deg) scale(0.8);
}

.theme-icon-leave-to {
  opacity: 0;
  transform: rotate(90deg) scale(0.8);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .theme-button {
    width: 36px;
    height: 36px;
  }

  .theme-button svg {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 768px) {
  .theme-button {
    width: 34px;
    height: 34px;
  }

  .theme-button svg {
    width: 17px;
    height: 17px;
  }
}

@media (max-width: 480px) {
  .theme-button {
    width: 32px;
    height: 32px;
  }

  .theme-button svg {
    width: 16px;
    height: 16px;
  }
}

/* 移动端工具栏样式 */
.mobile-toolbar-item .theme-button {
  background: transparent !important;
  box-shadow: none !important;
  width: 32px;
  height: 32px;
  color: var(--text-primary) !important;
}

.mobile-toolbar-item .theme-button:hover {
  background: rgba(0, 0, 0, 0.1) !important;
  transform: none;
  box-shadow: none !important;
}

.mobile-toolbar-item .theme-button svg {
  width: 18px;
  height: 18px;
}
</style>

<!-- 全局主题样式 -->
<style>
/* 浅色主题变量 */
:root.light-theme {
  /* 主题切换按钮 */
  --theme-toggle-bg: #f3f4f6;
  --theme-toggle-color: #374151;
  --theme-toggle-hover-bg: #e5e7eb;

  /* 背景颜色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-quaternary: #f5f7fa;

  /* 文本颜色 */
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --text-white: #ffffff;
  --text-white-soft: rgba(255, 255, 255, 0.9);
  --text-white-muted: rgba(255, 255, 255, 0.7);

  /* 边框颜色 */
  --border-primary: #e5e7eb;
  --border-secondary: #f3f4f6;
  --border-light: #e0e6ed;

  /* 强调色 */
  --accent-primary: #3b82f6;
  --accent-secondary: #eff6ff;

  /* 置顶会话颜色 */
  --pinned-bg: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  --pinned-border: #f59e0b;
  --pinned-hover-bg: linear-gradient(135deg, #fde68a 0%, #fcd34d 100%);
  --pinned-hover-border: #d97706;
  --pinned-indicator: #f59e0b;

  /* 侧边栏颜色 */
  --sidebar-bg: linear-gradient(180deg, #87ceeb 0%, #6bb6d6 100%);
  --sidebar-overlay: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(0, 0, 0, 0.05) 100%);
  --sidebar-nav-color: rgba(255, 255, 255, 0.7);
  --sidebar-nav-hover: rgba(255, 255, 255, 0.1);
  --sidebar-nav-active: rgba(255, 255, 255, 0.1);

  /* 阴影 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 6px 12px rgba(0, 0, 0, 0.15);
  --shadow-xl: 0 12px 20px rgba(0, 0, 0, 0.25);

  /* 状态颜色 - 浅色主题 */
  --error-bg: #fef2f2;
  --error-border: #ef4444;
  --info-bg: #f0f9ff;
  --info-border: #0ea5e9;
  --success-bg: #f0fdf4;
  --success-border: #22c55e;
  --warning-bg: #fffbeb;
  --warning-border: #f59e0b;
}

/* 深色主题变量 */
:root.dark-theme {
  /* 主题切换按钮 */
  --theme-toggle-bg: #374151;
  --theme-toggle-color: #f9fafb;
  --theme-toggle-hover-bg: #4b5563;

  /* 背景颜色 */
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --bg-tertiary: #374151;
  --bg-quaternary: #1f2937;

  /* 文本颜色 */
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-tertiary: #9ca3af;
  --text-white: #f9fafb;
  --text-white-soft: rgba(249, 250, 251, 0.9);
  --text-white-muted: rgba(249, 250, 251, 0.7);

  /* 边框颜色 */
  --border-primary: #374151;
  --border-secondary: #4b5563;
  --border-light: #4b5563;

  /* 强调色 */
  --accent-primary: #60a5fa;
  --accent-secondary: #1e3a8a;

  /* 置顶会话颜色 - 深色主题下使用更柔和的颜色 */
  --pinned-bg: linear-gradient(135deg, rgba(120, 113, 108, 0.3) 0%, rgba(87, 83, 78, 0.4) 100%);
  --pinned-border: rgba(161, 161, 170, 0.4);
  --pinned-hover-bg: linear-gradient(135deg, rgba(120, 113, 108, 0.4) 0%, rgba(87, 83, 78, 0.5) 100%);
  --pinned-hover-border: rgba(161, 161, 170, 0.6);
  --pinned-indicator: #a1a1aa;

  /* 侧边栏颜色 - 深色主题下使用深色渐变 */
  --sidebar-bg: linear-gradient(180deg, #374151 0%, #1f2937 100%);
  --sidebar-overlay: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, transparent 50%, rgba(0, 0, 0, 0.1) 100%);
  --sidebar-nav-color: rgba(249, 250, 251, 0.7);
  --sidebar-nav-hover: rgba(255, 255, 255, 0.1);
  --sidebar-nav-active: rgba(255, 255, 255, 0.25);

  /* 阴影 - 深色主题下使用更深的阴影 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 6px 12px rgba(0, 0, 0, 0.4);
  --shadow-xl: 0 12px 20px rgba(0, 0, 0, 0.5);

  /* 状态颜色 - 深色主题 */
  --error-bg: rgba(239, 68, 68, 0.1);
  --error-border: rgba(239, 68, 68, 0.3);
  --info-bg: rgba(14, 165, 233, 0.1);
  --info-border: rgba(14, 165, 233, 0.3);
  --success-bg: rgba(34, 197, 94, 0.1);
  --success-border: rgba(34, 197, 94, 0.3);
  --warning-bg: rgba(245, 158, 11, 0.1);
  --warning-border: rgba(245, 158, 11, 0.3);
}

/* 主题过渡动画 */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* 强制主题样式 - 使用data-theme属性确保兼容性 */
[data-theme="sun"] {
  /* 强制浅色主题变量 */
  --bg-primary: #ffffff !important;
  --bg-secondary: #f8fafc !important;
  --bg-tertiary: #f1f5f9 !important;
  --text-primary: #1f2937 !important;
  --text-secondary: #6b7280 !important;
  --text-tertiary: #9ca3af !important;
  --border-primary: #e5e7eb !important;
  --border-secondary: #f3f4f6 !important;
  --accent-primary: #3b82f6 !important;
  --accent-secondary: #eff6ff !important;
  --error-bg: #fef2f2 !important;
  --error-border: #ef4444 !important;
  --info-bg: #f0f9ff !important;
  --info-border: #0ea5e9 !important;
  --success-bg: #f0fdf4 !important;
  --success-border: #22c55e !important;
  --warning-bg: #fffbeb !important;
  --warning-border: #f59e0b !important;
}

[data-theme="moon"] {
  /* 强制深色主题变量 */
  --bg-primary: #111827 !important;
  --bg-secondary: #1f2937 !important;
  --bg-tertiary: #374151 !important;
  --text-primary: #f9fafb !important;
  --text-secondary: #d1d5db !important;
  --text-tertiary: #9ca3af !important;
  --border-primary: #374151 !important;
  --border-secondary: #4b5563 !important;
  --accent-primary: #60a5fa !important;
  --accent-secondary: #1e3a8a !important;
  --error-bg: rgba(239, 68, 68, 0.1) !important;
  --error-border: rgba(239, 68, 68, 0.3) !important;
  --info-bg: rgba(14, 165, 233, 0.1) !important;
  --info-border: rgba(14, 165, 233, 0.3) !important;
  --success-bg: rgba(34, 197, 94, 0.1) !important;
  --success-border: rgba(34, 197, 94, 0.3) !important;
  --warning-bg: rgba(245, 158, 11, 0.1) !important;
  --warning-border: rgba(245, 158, 11, 0.3) !important;
}

/* AI消息强制样式 - 确保在所有浏览器中正确显示 */
[data-theme="sun"] .markdown-renderer,
[data-theme="sun"] .ai-content-wrapper,
[data-theme="sun"] .message-bubble {
  color: #1f2937 !important;
  background-color: #f8fafc !important;
}

[data-theme="moon"] .markdown-renderer,
[data-theme="moon"] .ai-content-wrapper,
[data-theme="moon"] .message-bubble {
  color: #f9fafb !important;
  background-color: #1f2937 !important;
}

/* 强制AI消息内容样式 */
[data-theme="sun"] .markdown-renderer p,
[data-theme="sun"] .markdown-renderer h1,
[data-theme="sun"] .markdown-renderer h2,
[data-theme="sun"] .markdown-renderer h3,
[data-theme="sun"] .markdown-renderer h4,
[data-theme="sun"] .markdown-renderer h5,
[data-theme="sun"] .markdown-renderer h6,
[data-theme="sun"] .markdown-renderer li,
[data-theme="sun"] .markdown-renderer strong,
[data-theme="sun"] .markdown-renderer em {
  color: #1f2937 !important;
}

[data-theme="moon"] .markdown-renderer p,
[data-theme="moon"] .markdown-renderer h1,
[data-theme="moon"] .markdown-renderer h2,
[data-theme="moon"] .markdown-renderer h3,
[data-theme="moon"] .markdown-renderer h4,
[data-theme="moon"] .markdown-renderer h5,
[data-theme="moon"] .markdown-renderer h6,
[data-theme="moon"] .markdown-renderer li,
[data-theme="moon"] .markdown-renderer strong,
[data-theme="moon"] .markdown-renderer em {
  color: #f9fafb !important;
}

/* 帮助与反馈页面强制样式 */
[data-theme="sun"] .help-feedback .guide-title,
[data-theme="sun"] .help-feedback .guide-description,
[data-theme="sun"] .help-feedback .step-text,
[data-theme="sun"] .help-feedback .question-text,
[data-theme="sun"] .help-feedback .form-title,
[data-theme="sun"] .help-feedback .form-description,
[data-theme="sun"] .help-feedback .form-label,
[data-theme="sun"] .help-feedback .radio-label {
  color: #1f2937 !important;
}

[data-theme="moon"] .help-feedback .guide-title,
[data-theme="moon"] .help-feedback .guide-description,
[data-theme="moon"] .help-feedback .step-text,
[data-theme="moon"] .help-feedback .question-text,
[data-theme="moon"] .help-feedback .form-title,
[data-theme="moon"] .help-feedback .form-description,
[data-theme="moon"] .help-feedback .form-label,
[data-theme="moon"] .help-feedback .radio-label {
  color: #f9fafb !important;
}

[data-theme="sun"] .help-feedback .guide-item,
[data-theme="sun"] .help-feedback .feedback-form,
[data-theme="sun"] .help-feedback .faq-question,
[data-theme="sun"] .help-feedback .faq-answer {
  background-color: #f8fafc !important;
  border-color: #e5e7eb !important;
}

[data-theme="moon"] .help-feedback .guide-item,
[data-theme="moon"] .help-feedback .feedback-form,
[data-theme="moon"] .help-feedback .faq-question,
[data-theme="moon"] .help-feedback .faq-answer {
  background-color: #1f2937 !important;
  border-color: #374151 !important;
}

[data-theme="sun"] .help-feedback .form-input,
[data-theme="sun"] .help-feedback .form-textarea {
  background-color: #ffffff !important;
  color: #1f2937 !important;
  border-color: #d1d5db !important;
}

[data-theme="moon"] .help-feedback .form-input,
[data-theme="moon"] .help-feedback .form-textarea {
  background-color: #374151 !important;
  color: #f9fafb !important;
  border-color: #4b5563 !important;
}
</style>
