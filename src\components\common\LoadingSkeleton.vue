<template>
  <!-- 加载骨架屏组件 -->
  <div class="loading-skeleton">
    <!-- 消息骨架屏 -->
    <div v-if="type === 'message'" class="message-skeleton">
      <div class="skeleton-avatar"></div>
      <div class="skeleton-content">
        <div class="skeleton-line skeleton-line-long"></div>
        <div class="skeleton-line skeleton-line-medium"></div>
        <div class="skeleton-line skeleton-line-short"></div>
      </div>
    </div>
    
    <!-- 会话列表骨架屏 -->
    <div v-else-if="type === 'session'" class="session-skeleton">
      <div v-for="i in count" :key="i" class="session-item-skeleton">
        <div class="skeleton-title"></div>
        <div class="skeleton-meta">
          <div class="skeleton-tag"></div>
          <div class="skeleton-time"></div>
        </div>
        <div class="skeleton-preview"></div>
      </div>
    </div>
    
    <!-- 通用骨架屏 -->
    <div v-else class="generic-skeleton">
      <div v-for="i in count" :key="i" class="skeleton-line" 
           :class="`skeleton-line-${['long', 'medium', 'short'][i % 3]}`">
      </div>
    </div>
  </div>
</template>

<script setup>
// defineProps 在 Vue 3 中是编译器宏，不需要导入

// 组件属性
const props = defineProps({
  // 骨架屏类型：message, session, generic
  type: {
    type: String,
    default: 'generic'
  },
  // 显示数量
  count: {
    type: Number,
    default: 3
  }
})
</script>

<style scoped>
/* 骨架屏基础样式 */
.loading-skeleton {
  padding: 16px;
}

/* 骨架屏动画 */
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-line,
.skeleton-avatar,
.skeleton-title,
.skeleton-meta > div,
.skeleton-preview,
.skeleton-tag,
.skeleton-time {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

/* 消息骨架屏 */
.message-skeleton {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.skeleton-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  flex-shrink: 0;
}

.skeleton-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-line {
  height: 16px;
  border-radius: 8px;
}

.skeleton-line-long {
  width: 80%;
}

.skeleton-line-medium {
  width: 60%;
}

.skeleton-line-short {
  width: 40%;
}

/* 会话列表骨架屏 */
.session-skeleton {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.session-item-skeleton {
  padding: 12px;
  border: 1px solid #f0f2f5;
  border-radius: 8px;
  background-color: #fafbfc;
}

.skeleton-title {
  height: 18px;
  width: 70%;
  margin-bottom: 8px;
}

.skeleton-meta {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.skeleton-tag {
  height: 14px;
  width: 60px;
}

.skeleton-time {
  height: 14px;
  width: 80px;
}

.skeleton-preview {
  height: 14px;
  width: 90%;
}

/* 通用骨架屏 */
.generic-skeleton {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.generic-skeleton .skeleton-line {
  height: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-skeleton {
    padding: 12px;
  }
  
  .message-skeleton {
    gap: 8px;
    margin-bottom: 12px;
  }
  
  .skeleton-avatar {
    width: 28px;
    height: 28px;
  }
  
  .skeleton-content {
    gap: 6px;
  }
  
  .skeleton-line {
    height: 14px;
  }
}
</style>
