<template>
  <!-- 消息重试组件 -->
  <div class="message-retry" v-if="showRetry">
    <div class="retry-content">
      <div class="retry-icon">
        <WarningIcon />
      </div>
      <div class="retry-text">
        <span class="retry-title">发送失败</span>
        <span class="retry-description">{{ errorMessage }}</span>
      </div>
      <div class="retry-actions">
        <button 
          class="retry-button"
          @click="handleRetry"
          :disabled="isRetrying"
        >
          <RefreshIcon v-if="!isRetrying" />
          <LoadingIcon v-else />
          {{ isRetrying ? '重试中...' : '重试' }}
        </button>
        <button 
          class="cancel-button"
          @click="handleCancel"
        >
          取消
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue'

// 图标组件
const WarningIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
    </svg>
  `
}

const RefreshIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor">
      <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
    </svg>
  `
}

const LoadingIcon = {
  template: `
    <svg viewBox="0 0 24 24" fill="currentColor" class="loading-spin">
      <path d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z"/>
    </svg>
  `
}

// 组件属性
const props = defineProps({
  // 是否显示重试组件
  showRetry: {
    type: Boolean,
    default: false
  },
  // 错误消息
  errorMessage: {
    type: String,
    default: '网络连接异常，请检查网络后重试'
  },
  // 是否正在重试
  isRetrying: {
    type: Boolean,
    default: false
  }
})

// 组件事件
const emit = defineEmits(['retry', 'cancel'])

/**
 * 处理重试
 */
const handleRetry = () => {
  console.log('用户点击重试')
  emit('retry')
}

/**
 * 处理取消
 */
const handleCancel = () => {
  console.log('用户取消重试')
  emit('cancel')
}
</script>

<style scoped>
/* 消息重试容器 */
.message-retry {
  margin: 8px 0;
  padding: 12px;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  border-left: 4px solid #ef4444;
}

.retry-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 重试图标 */
.retry-icon {
  width: 20px;
  height: 20px;
  color: #ef4444;
  flex-shrink: 0;
}

.retry-icon svg {
  width: 100%;
  height: 100%;
}

/* 重试文本 */
.retry-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.retry-title {
  font-size: 14px;
  font-weight: 500;
  color: #dc2626;
}

.retry-description {
  font-size: 12px;
  color: #7f1d1d;
  line-height: 1.4;
}

/* 重试操作 */
.retry-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.retry-button,
.cancel-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button {
  background-color: #ef4444;
  color: white;
}

.retry-button:hover:not(:disabled) {
  background-color: #dc2626;
  transform: translateY(-1px);
}

.retry-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.retry-button svg {
  width: 14px;
  height: 14px;
}

.cancel-button {
  background-color: transparent;
  color: #7f1d1d;
  border: 1px solid #fca5a5;
}

.cancel-button:hover {
  background-color: #fee2e2;
}

/* 加载动画 */
.loading-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-retry {
    padding: 10px;
  }
  
  .retry-content {
    gap: 8px;
  }
  
  .retry-actions {
    flex-direction: column;
    gap: 6px;
  }
  
  .retry-button,
  .cancel-button {
    padding: 8px 12px;
    font-size: 13px;
  }
}
</style>
