/**
 * 键盘快捷键管理 Composable
 * 提供全局键盘快捷键支持和管理功能
 */

import { onMounted, onUnmounted, ref } from 'vue'

export function useKeyboardShortcuts() {
  // 快捷键映射表
  const shortcuts = ref(new Map())
  
  // 当前按下的修饰键
  const modifierKeys = ref({
    ctrl: false,
    alt: false,
    shift: false,
    meta: false
  })

  /**
   * 注册快捷键
   * @param {string} key - 按键组合，如 'ctrl+n', 'alt+shift+d'
   * @param {Function} callback - 回调函数
   * @param {string} description - 快捷键描述
   */
  const registerShortcut = (key, callback, description = '') => {
    const normalizedKey = normalizeKey(key)
    shortcuts.value.set(normalizedKey, {
      callback,
      description,
      key: normalizedKey
    })
    console.log(`注册快捷键: ${key} - ${description}`)
  }

  /**
   * 注销快捷键
   * @param {string} key - 按键组合
   */
  const unregisterShortcut = (key) => {
    const normalizedKey = normalizeKey(key)
    shortcuts.value.delete(normalizedKey)
    console.log(`注销快捷键: ${key}`)
  }

  /**
   * 标准化按键字符串
   * @param {string} key - 原始按键字符串
   * @returns {string} 标准化后的按键字符串
   */
  const normalizeKey = (key) => {
    return key.toLowerCase()
      .replace(/\s+/g, '')
      .split('+')
      .sort((a, b) => {
        const order = ['ctrl', 'alt', 'shift', 'meta']
        const aIndex = order.indexOf(a)
        const bIndex = order.indexOf(b)
        if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex
        if (aIndex !== -1) return -1
        if (bIndex !== -1) return 1
        return a.localeCompare(b)
      })
      .join('+')
  }

  /**
   * 处理键盘按下事件
   * @param {KeyboardEvent} event - 键盘事件
   */
  const handleKeyDown = (event) => {
    // 更新修饰键状态
    modifierKeys.value.ctrl = event.ctrlKey
    modifierKeys.value.alt = event.altKey
    modifierKeys.value.shift = event.shiftKey
    modifierKeys.value.meta = event.metaKey

    // 构建当前按键组合
    const keyCombo = buildKeyCombo(event)
    
    // 查找匹配的快捷键
    const shortcut = shortcuts.value.get(keyCombo)
    
    if (shortcut) {
      // 阻止默认行为和事件冒泡
      event.preventDefault()
      event.stopPropagation()
      
      // 执行回调
      try {
        shortcut.callback(event)
        console.log(`执行快捷键: ${keyCombo}`)
      } catch (error) {
        console.error(`快捷键执行错误: ${keyCombo}`, error)
      }
    }
  }

  /**
   * 处理键盘释放事件
   * @param {KeyboardEvent} event - 键盘事件
   */
  const handleKeyUp = (event) => {
    // 更新修饰键状态
    modifierKeys.value.ctrl = event.ctrlKey
    modifierKeys.value.alt = event.altKey
    modifierKeys.value.shift = event.shiftKey
    modifierKeys.value.meta = event.metaKey
  }

  /**
   * 构建按键组合字符串
   * @param {KeyboardEvent} event - 键盘事件
   * @returns {string} 按键组合字符串
   */
  const buildKeyCombo = (event) => {
    const parts = []
    
    if (event.ctrlKey) parts.push('ctrl')
    if (event.altKey) parts.push('alt')
    if (event.shiftKey) parts.push('shift')
    if (event.metaKey) parts.push('meta')
    
    // 添加主键
    const key = event.key.toLowerCase()
    
    // 忽略修饰键本身
    if (!['control', 'alt', 'shift', 'meta'].includes(key)) {
      parts.push(key)
    }
    
    return parts.join('+')
  }

  /**
   * 获取所有已注册的快捷键
   * @returns {Array} 快捷键列表
   */
  const getShortcuts = () => {
    return Array.from(shortcuts.value.values())
  }

  /**
   * 检查是否有快捷键冲突
   * @param {string} key - 要检查的按键组合
   * @returns {boolean} 是否有冲突
   */
  const hasConflict = (key) => {
    const normalizedKey = normalizeKey(key)
    return shortcuts.value.has(normalizedKey)
  }

  /**
   * 初始化默认快捷键
   */
  const initDefaultShortcuts = () => {
    // 这些快捷键将在具体组件中注册
    console.log('键盘快捷键系统已初始化')
  }

  // 组件挂载时添加事件监听器
  onMounted(() => {
    document.addEventListener('keydown', handleKeyDown, true)
    document.addEventListener('keyup', handleKeyUp, true)
    initDefaultShortcuts()
  })

  // 组件卸载时移除事件监听器
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeyDown, true)
    document.removeEventListener('keyup', handleKeyUp, true)
    shortcuts.value.clear()
  })

  return {
    // 方法
    registerShortcut,
    unregisterShortcut,
    getShortcuts,
    hasConflict,
    
    // 状态
    modifierKeys: modifierKeys.value,
    
    // 常用快捷键常量
    SHORTCUTS: {
      NEW_SESSION: 'ctrl+n',
      SEARCH: 'ctrl+f',
      TOGGLE_SIDEBAR: 'ctrl+b',
      FOCUS_INPUT: 'ctrl+l',
      SEND_MESSAGE: 'ctrl+enter',
      CLEAR_CHAT: 'ctrl+shift+k',
      TOGGLE_THEME: 'ctrl+shift+t',
      HELP: 'f1',
      ESCAPE: 'escape'
    }
  }
}

/**
 * 全局快捷键提示组件数据
 */
export const shortcutHints = [
  { key: 'Ctrl + N', description: '新建会话' },
  { key: 'Ctrl + F', description: '搜索消息' },
  { key: 'Ctrl + B', description: '切换侧边栏' },
  { key: 'Ctrl + L', description: '聚焦输入框' },
  { key: 'Ctrl + Enter', description: '发送消息' },
  { key: 'Ctrl + Shift + K', description: '清空对话' },
  { key: 'Ctrl + Shift + T', description: '切换主题' },
  { key: 'F1', description: '显示帮助' },
  { key: 'Esc', description: '取消/关闭' }
]
