{"name": "bod<PERSON>i", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --port 3000", "build": "vite build", "preview": "vite preview --port 5000", "sso-proxy": "node sso-proxy-server.js", "dev:full": "concurrently \"npm run sso-proxy\" \"npm run dev\""}, "dependencies": {"axios": "^1.10.0", "cors": "^2.8.5", "express": "^4.21.2", "highlight.js": "^11.11.1", "marked": "^16.0.0", "node-fetch": "^3.3.2", "redis": "^5.5.6", "vue": "^3.5.17"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "concurrently": "^8.2.2", "vite": "^4.5.0", "vite-plugin-vue-devtools": "^7.7.7"}}