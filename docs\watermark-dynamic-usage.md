# 动态水印功能使用说明

## 概述

动态水印功能已更新，现在支持根据登录用户信息和当前时间动态生成水印内容。水印格式为：**用户名-工号-时间**

## 功能特性

- ✅ 动态生成水印文本：`用户名-工号-时间`
- ✅ 支持知识中心、业务域、职能域三个模块
- ✅ 用户未登录时显示默认水印
- ✅ 实时更新时间信息
- ✅ 自动获取用户登录信息
- ✅ 错误处理和降级机制

## 水印格式示例

### 用户已登录
```
知识中心 - 张三-EMP001-2024/01/15 14:30
业务域 - 李四-EMP002-2024/01/15 14:30
职能域 - 王五-EMP003-2024/01/15 14:30
```

### 用户未登录
```
知识中心 - 未登录用户
业务域 - 未登录用户
职能域 - 未登录用户
```

## API 使用方法

### 1. 生成动态水印文本

```javascript
import { generateDynamicWatermarkText } from '../config/watermark.js'

// 生成知识中心水印文本
const knowledgeWatermark = await generateDynamicWatermarkText('知识中心')
console.log(knowledgeWatermark) // "知识中心 - 张三-EMP001-2024/01/15 14:30"

// 生成业务域水印文本
const businessWatermark = await generateDynamicWatermarkText('业务域')
console.log(businessWatermark) // "业务域 - 张三-EMP001-2024/01/15 14:30"
```

### 2. 获取动态水印配置

```javascript
import { getDynamicWatermarkConfig } from '../config/watermark.js'

// 获取包含动态文本的完整水印配置
const config = await getDynamicWatermarkConfig('knowledge')
console.log(config.text) // "知识中心 - 张三-EMP001-2024/01/15 14:30"
```

### 3. 获取静态水印配置

```javascript
import { getWatermarkConfig } from '../config/watermark.js'

// 获取使用默认文本的水印配置（同步）
const config = getWatermarkConfig('knowledge')
console.log(config.text) // "知识中心 - 机密文档"
```

### 4. 刷新所有动态水印

```javascript
import { refreshAllDynamicWatermarks } from '../config/watermark.js'

// 用户登录状态变化时，刷新所有模块的水印
const results = await refreshAllDynamicWatermarks()
console.log(results)
```

## 配置说明

每个模块的水印配置现在包含以下新属性：

```javascript
{
  enabled: true,
  text: '知识中心 - 机密文档',        // 默认文本（降级使用）
  dynamicText: true,                  // 启用动态文本
  prefix: '知识中心',                 // 动态文本前缀
  fontSize: 16,
  opacity: 0.2,
  color: '#3b82f6',
  // ... 其他样式配置
}
```

## 集成指南

### 1. 在组件中使用动态水印

```javascript
// 在 Vue 组件中
import { getDynamicWatermarkConfig } from '@/config/watermark.js'

export default {
  data() {
    return {
      watermarkConfig: null
    }
  },
  
  async mounted() {
    // 获取动态水印配置
    this.watermarkConfig = await getDynamicWatermarkConfig('knowledge')
    this.applyWatermark()
  },
  
  methods: {
    applyWatermark() {
      if (this.watermarkConfig) {
        // 应用水印到页面
        console.log('应用水印:', this.watermarkConfig.text)
      }
    }
  }
}
```

### 2. 监听用户登录状态变化

```javascript
import { getUserService } from '@/services/userService.js'
import { refreshAllDynamicWatermarks } from '@/config/watermark.js'

// 用户登录后刷新水印
async function onUserLogin() {
  try {
    await refreshAllDynamicWatermarks()
    console.log('水印已更新')
  } catch (error) {
    console.error('更新水印失败:', error)
  }
}

// 用户退出后刷新水印
async function onUserLogout() {
  try {
    await refreshAllDynamicWatermarks()
    console.log('水印已重置为未登录状态')
  } catch (error) {
    console.error('重置水印失败:', error)
  }
}
```

### 3. 定时刷新水印（更新时间）

```javascript
// 每分钟刷新一次水印时间
setInterval(async () => {
  try {
    await refreshAllDynamicWatermarks()
  } catch (error) {
    console.error('定时刷新水印失败:', error)
  }
}, 60000) // 60秒
```

## 错误处理

动态水印功能包含完善的错误处理机制：

1. **用户未登录**：显示 "未登录用户" 水印
2. **获取用户信息失败**：使用默认水印文本
3. **时间格式化失败**：使用系统默认时间格式
4. **网络错误**：降级到静态水印配置

## 性能考虑

- 动态水印文本生成是异步操作
- 建议在用户登录状态变化时才刷新水印
- 可以根据需要调整时间刷新频率
- 静态配置可用作快速降级方案

## 示例代码

完整的使用示例请参考：`src/examples/watermarkExample.js`

运行示例：
```javascript
import { runAllExamples } from '@/examples/watermarkExample.js'
await runAllExamples()
```

## 注意事项

1. 确保用户服务 (`userService.js`) 正常工作
2. 动态水印依赖用户登录状态
3. 时间格式使用中文本地化格式
4. 建议在生产环境中添加更多错误处理逻辑
