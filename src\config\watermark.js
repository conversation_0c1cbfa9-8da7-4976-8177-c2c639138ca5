/**
 * 水印配置文件
 * 用于配置各个模块的水印显示
 */

import { getUserService } from '../services/userService.js'

/**
 * 生成动态水印文本
 * 格式：用户名-工号-时间
 * @param {string} prefix - 水印前缀（如：知识中心、业务域、职能域）
 * @returns {string} 动态生成的水印文本
 */
export async function generateDynamicWatermarkText(prefix = '') {
  try {
    const userService = getUserService()
    const currentUser = await userService.getCurrentUser()

    if (!currentUser) {
      // 如果用户未登录，返回默认水印
      return prefix ? `${prefix} - 未登录用户` : '未登录用户'
    }

    // 获取当前日期时间
    const now = new Date()
    const dateStr = now.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
    const timeStr = now.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
    const dateTimeStr = `${dateStr} ${timeStr}`

    // 生成水印文本：用户名-工号-时间
    const userWatermark = `${currentUser.username}-${currentUser.employeeId}-${dateTimeStr}`

    return prefix ? `${prefix} - ${userWatermark}` : userWatermark
  } catch (error) {
    console.error('生成动态水印文本失败:', error)
    return prefix ? `${prefix} - 水印生成失败` : '水印生成失败'
  }
}

// 水印基础配置
export const watermarkConfig = {
  // 知识中心水印配置
  knowledge: {
    enabled: true,
    text: '知识中心 - 机密文档', // 默认文本，实际使用时会被动态生成的文本替换
    dynamicText: true, // 标识使用动态文本
    prefix: '知识中心', // 动态文本前缀
    fontSize: 16,
    opacity: 0.2,
    color: '#3b82f6',
    rotate: -15,
    gapX: 300,
    gapY: 220,
    mode: 'repeat',
    animated: true,
    animationSpeed: 'normal'
  },

  // 业务域水印配置
  business: {
    enabled: true,
    text: '业务域 - 商业机密', // 默认文本，实际使用时会被动态生成的文本替换
    dynamicText: true, // 标识使用动态文本
    prefix: '业务域', // 动态文本前缀
    fontSize: 16,
    opacity: 0.2,
    color: '#10b981',
    rotate: -15,
    gapX: 300,
    gapY: 220,
    mode: 'repeat',
    animated: true,
    animationSpeed: 'slow'
  },

  // 职能域水印配置
  function: {
    enabled: true,
    text: '职能域 - 内部专用', // 默认文本，实际使用时会被动态生成的文本替换
    dynamicText: true, // 标识使用动态文本
    prefix: '职能域', // 动态文本前缀
    fontSize: 16,
    opacity: 0.2,
    color: '#8b5cf6',
    rotate: -15,
    gapX: 300,
    gapY: 220,
    mode: 'repeat',
    animated: false,
    animationSpeed: 'normal'
  }
}

// 水印全局设置
export const globalWatermarkSettings = {
  // 是否启用水印功能
  enabled: true,
  
  // 水印层级
  zIndex: 1,
  
  // 是否可选择（防止用户选中水印文字）
  userSelect: 'none',
  
  // 是否响应鼠标事件
  pointerEvents: 'none',
  
  // 字体设置
  fontFamily: 'Arial, sans-serif',
  fontWeight: '500',
  
  // 防篡改设置
  antiTamper: {
    // 是否启用防篡改
    enabled: true,
    
    // 检查间隔（毫秒）
    checkInterval: 5000,
    
    // 重新生成水印的延迟（毫秒）
    regenerateDelay: 3000
  }
}

/**
 * 获取指定模块的水印配置（同步版本，使用默认文本）
 * @param {string} module - 模块名称 (knowledge, business, function)
 * @returns {Object} 水印配置对象
 */
export function getWatermarkConfig(module) {
  const config = watermarkConfig[module]
  if (!config) {
    console.warn(`未找到模块 ${module} 的水印配置`)
    return null
  }

  return {
    ...config,
    ...globalWatermarkSettings
  }
}

/**
 * 获取指定模块的动态水印配置（异步版本，包含动态文本）
 * @param {string} module - 模块名称 (knowledge, business, function)
 * @returns {Promise<Object>} 水印配置对象
 */
export async function getDynamicWatermarkConfig(module) {
  const config = watermarkConfig[module]
  if (!config) {
    console.warn(`未找到模块 ${module} 的水印配置`)
    return null
  }

  // 如果配置了动态文本，则生成动态水印文本
  let finalConfig = { ...config }
  if (config.dynamicText && config.prefix) {
    try {
      const dynamicText = await generateDynamicWatermarkText(config.prefix)
      finalConfig.text = dynamicText
    } catch (error) {
      console.error(`生成 ${module} 模块动态水印失败:`, error)
      // 如果生成失败，使用默认文本
    }
  }

  return {
    ...finalConfig,
    ...globalWatermarkSettings
  }
}

/**
 * 更新指定模块的水印配置
 * @param {string} module - 模块名称
 * @param {Object} newConfig - 新的配置
 */
export function updateWatermarkConfig(module, newConfig) {
  if (watermarkConfig[module]) {
    watermarkConfig[module] = {
      ...watermarkConfig[module],
      ...newConfig
    }
    console.log(`${module} 模块水印配置已更新:`, watermarkConfig[module])
  } else {
    console.warn(`无法更新模块 ${module} 的水印配置：模块不存在`)
  }
}

/**
 * 重置指定模块的水印配置为默认值
 * @param {string} module - 模块名称
 */
export function resetWatermarkConfig(module) {
  const defaultConfigs = {
    knowledge: {
      enabled: true,
      text: '知识中心 - 机密文档',
      dynamicText: true,
      prefix: '知识中心',
      fontSize: 16,
      opacity: 0.08,
      color: '#3b82f6',
      rotate: -15,
      gapX: 200,
      gapY: 120,
      mode: 'repeat',
      animated: true,
      animationSpeed: 'normal'
    },
    business: {
      enabled: true,
      text: '业务域 - 商业机密',
      dynamicText: true,
      prefix: '业务域',
      fontSize: 18,
      opacity: 0.06,
      color: '#10b981',
      rotate: -20,
      gapX: 180,
      gapY: 140,
      mode: 'repeat',
      animated: true,
      animationSpeed: 'slow'
    },
    function: {
      enabled: true,
      text: '职能域 - 内部专用',
      dynamicText: true,
      prefix: '职能域',
      fontSize: 14,
      opacity: 0.05,
      color: '#8b5cf6',
      rotate: -25,
      gapX: 160,
      gapY: 100,
      mode: 'repeat',
      animated: false,
      animationSpeed: 'normal'
    }
  }

  if (defaultConfigs[module]) {
    watermarkConfig[module] = { ...defaultConfigs[module] }
    console.log(`${module} 模块水印配置已重置为默认值`)
  }
}

/**
 * 获取所有模块的水印配置
 * @returns {Object} 所有模块的水印配置
 */
export function getAllWatermarkConfigs() {
  return { ...watermarkConfig }
}

/**
 * 检查水印是否启用
 * @param {string} module - 模块名称
 * @returns {boolean} 是否启用水印
 */
export function isWatermarkEnabled(module) {
  return globalWatermarkSettings.enabled && 
         watermarkConfig[module]?.enabled === true
}

// 导出默认配置（用于重置）
export const defaultWatermarkConfig = {
  knowledge: { ...watermarkConfig.knowledge },
  business: { ...watermarkConfig.business },
  function: { ...watermarkConfig.function }
}

/**
 * 刷新所有模块的动态水印文本
 * 用于用户登录状态变化时更新水印
 * @returns {Promise<void>}
 */
export async function refreshAllDynamicWatermarks() {
  try {
    const modules = ['knowledge', 'business', 'function']
    const refreshPromises = modules.map(async (module) => {
      const config = watermarkConfig[module]
      if (config && config.dynamicText && config.prefix) {
        try {
          const dynamicText = await generateDynamicWatermarkText(config.prefix)
          console.log(`${module} 模块水印文本已更新:`, dynamicText)
          return { module, text: dynamicText }
        } catch (error) {
          console.error(`刷新 ${module} 模块水印失败:`, error)
          return { module, text: config.text, error }
        }
      }
      return { module, text: config.text }
    })

    const results = await Promise.all(refreshPromises)
    console.log('所有模块水印文本刷新完成:', results)
    return results
  } catch (error) {
    console.error('刷新动态水印失败:', error)
    throw error
  }
}
