/**
 * Redis连接管理器
 * 负责Redis连接状态监控、重连机制和降级策略
 */

import { getRedisAdapter } from './redisHttpAdapter.js'
import { getRedisConfig } from '../config/redis.js'

/**
 * Redis连接管理器类
 */
class RedisConnectionManager {
  constructor() {
    this.redis = getRedisAdapter()
    this.config = getRedisConfig()
    
    // 连接状态
    this.isConnected = false
    this.isConnecting = false
    this.lastConnectionAttempt = null
    this.connectionAttempts = 0
    this.maxConnectionAttempts = 5
    
    // 重连配置
    this.reconnectInterval = 5000 // 5秒
    this.reconnectBackoffMultiplier = 1.5
    this.maxReconnectInterval = 60000 // 60秒
    
    // 健康检查配置
    this.healthCheckInterval = 30000 // 30秒
    this.healthCheckTimer = null
    
    // 事件监听器
    this.eventListeners = {
      connected: [],
      disconnected: [],
      reconnecting: [],
      error: []
    }
    
    // 启动连接管理
    this.initialize()
  }

  /**
   * 初始化连接管理器
   */
  async initialize() {
    console.log('Redis连接管理器初始化...')
    
    // 尝试初始连接
    await this.connect()
    
    // 启动健康检查
    this.startHealthCheck()
    
    // 监听页面可见性变化
    this.setupVisibilityChangeListener()
  }

  /**
   * 连接Redis
   */
  async connect() {
    if (this.isConnecting) {
      console.log('Redis连接正在进行中，跳过重复连接')
      return this.isConnected
    }

    this.isConnecting = true
    this.lastConnectionAttempt = new Date()

    try {
      console.log(`尝试连接Redis (第${this.connectionAttempts + 1}次)...`)
      
      const connected = await this.redis.ping()
      
      if (connected) {
        this.isConnected = true
        this.connectionAttempts = 0
        this.isConnecting = false
        
        console.log('Redis连接成功')
        this.emit('connected')
        
        return true
      } else {
        throw new Error('Redis ping失败')
      }
    } catch (error) {
      this.isConnected = false
      this.isConnecting = false
      this.connectionAttempts++
      
      console.error(`Redis连接失败 (第${this.connectionAttempts}次):`, error.message)
      this.emit('error', error)
      
      // 如果未达到最大重试次数，安排重连
      if (this.connectionAttempts < this.maxConnectionAttempts) {
        this.scheduleReconnect()
      } else {
        console.error('Redis连接重试次数已达上限，停止重连')
        this.emit('disconnected')
      }
      
      return false
    }
  }

  /**
   * 安排重连
   */
  scheduleReconnect() {
    const delay = Math.min(
      this.reconnectInterval * Math.pow(this.reconnectBackoffMultiplier, this.connectionAttempts - 1),
      this.maxReconnectInterval
    )
    
    console.log(`将在${delay}ms后尝试重连Redis`)
    this.emit('reconnecting', { delay, attempt: this.connectionAttempts })
    
    setTimeout(() => {
      this.connect()
    }, delay)
  }

  /**
   * 手动重连
   */
  async reconnect() {
    console.log('手动触发Redis重连...')
    this.connectionAttempts = 0
    return await this.connect()
  }

  /**
   * 断开连接
   */
  async disconnect() {
    console.log('断开Redis连接...')
    
    this.stopHealthCheck()
    
    if (this.redis) {
      await this.redis.disconnect()
    }
    
    this.isConnected = false
    this.emit('disconnected')
  }

  /**
   * 启动健康检查
   */
  startHealthCheck() {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer)
    }
    
    this.healthCheckTimer = setInterval(async () => {
      await this.performHealthCheck()
    }, this.healthCheckInterval)
    
    console.log(`Redis健康检查已启动，间隔${this.healthCheckInterval}ms`)
  }

  /**
   * 停止健康检查
   */
  stopHealthCheck() {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer)
      this.healthCheckTimer = null
      console.log('Redis健康检查已停止')
    }
  }

  /**
   * 执行健康检查
   */
  async performHealthCheck() {
    try {
      const connected = await this.redis.ping()
      
      if (connected && !this.isConnected) {
        // 连接恢复
        this.isConnected = true
        this.connectionAttempts = 0
        console.log('Redis连接已恢复')
        this.emit('connected')
      } else if (!connected && this.isConnected) {
        // 连接丢失
        this.isConnected = false
        console.warn('Redis连接丢失，尝试重连...')
        this.emit('disconnected')
        this.connect()
      }
    } catch (error) {
      if (this.isConnected) {
        this.isConnected = false
        console.warn('Redis健康检查失败，连接可能已断开:', error.message)
        this.emit('disconnected')
        this.connect()
      }
    }
  }

  /**
   * 设置页面可见性变化监听器
   */
  setupVisibilityChangeListener() {
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible') {
          // 页面变为可见时，检查连接状态
          console.log('页面变为可见，检查Redis连接状态')
          this.performHealthCheck()
        }
      })
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus() {
    return {
      connected: this.isConnected,
      connecting: this.isConnecting,
      lastConnectionAttempt: this.lastConnectionAttempt,
      connectionAttempts: this.connectionAttempts,
      maxConnectionAttempts: this.maxConnectionAttempts,
      nextReconnectIn: this.getNextReconnectDelay()
    }
  }

  /**
   * 获取下次重连延迟时间
   */
  getNextReconnectDelay() {
    if (this.isConnected || this.connectionAttempts === 0) {
      return 0
    }
    
    return Math.min(
      this.reconnectInterval * Math.pow(this.reconnectBackoffMultiplier, this.connectionAttempts - 1),
      this.maxReconnectInterval
    )
  }

  /**
   * 添加事件监听器
   */
  on(event, listener) {
    if (this.eventListeners[event]) {
      this.eventListeners[event].push(listener)
    }
  }

  /**
   * 移除事件监听器
   */
  off(event, listener) {
    if (this.eventListeners[event]) {
      const index = this.eventListeners[event].indexOf(listener)
      if (index > -1) {
        this.eventListeners[event].splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   */
  emit(event, data = null) {
    if (this.eventListeners[event]) {
      this.eventListeners[event].forEach(listener => {
        try {
          listener(data)
        } catch (error) {
          console.error(`事件监听器执行失败 (${event}):`, error)
        }
      })
    }
  }

  /**
   * 获取连接统计信息
   */
  getConnectionStats() {
    return {
      totalConnectionAttempts: this.connectionAttempts,
      lastConnectionAttempt: this.lastConnectionAttempt,
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      healthCheckInterval: this.healthCheckInterval,
      reconnectInterval: this.reconnectInterval,
      maxReconnectInterval: this.maxReconnectInterval
    }
  }

  /**
   * 重置连接统计
   */
  resetConnectionStats() {
    this.connectionAttempts = 0
    this.lastConnectionAttempt = null
    console.log('Redis连接统计已重置')
  }

  /**
   * 销毁连接管理器
   */
  async destroy() {
    console.log('销毁Redis连接管理器...')
    
    this.stopHealthCheck()
    await this.disconnect()
    
    // 清除所有事件监听器
    Object.keys(this.eventListeners).forEach(event => {
      this.eventListeners[event] = []
    })
  }
}

// 创建单例实例
let connectionManager = null

/**
 * 获取Redis连接管理器实例
 */
export const getRedisConnectionManager = () => {
  if (!connectionManager) {
    connectionManager = new RedisConnectionManager()
  }
  return connectionManager
}

export default RedisConnectionManager
