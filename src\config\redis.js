/**
 * Redis配置管理
 * 负责Redis连接参数配置、连接池管理和连接状态监控
 */

// Redis连接配置
const REDIS_CONFIG = {
  // Redis服务器配置
  host: '*************',
  port: 6379,
  password: 'X4gN7',
  
  // 连接选项
  connectTimeout: 10000,      // 连接超时时间（毫秒）
  lazyConnect: true,          // 延迟连接
  retryDelayOnFailover: 100,  // 故障转移重试延迟
  maxRetriesPerRequest: 3,    // 每个请求最大重试次数
  
  // 连接池配置
  family: 4,                  // IP版本 (4 或 6)
  keepAlive: true,           // 保持连接活跃
  
  // 数据库选择
  db: 0,                     // 使用的数据库编号
  
  // 键前缀配置
  keyPrefix: 'bodorai:',     // 所有键的前缀
  
  // 会话相关配置
  sessionKeyPrefix: 'session:', // 会话键前缀
  userKeyPrefix: 'user:',       // 用户键前缀
  
  // 过期时间配置（秒）
  sessionTTL: 7 * 24 * 60 * 60,    // 会话数据过期时间：7天
  backupTTL: 30 * 24 * 60 * 60,    // 备份数据过期时间：30天
  
  // 发布订阅频道
  channels: {
    sessionUpdate: 'session:update',    // 会话更新通知
    sessionDelete: 'session:delete',    // 会话删除通知
    userOnline: 'user:online',          // 用户在线状态
  }
}

// 环境变量覆盖配置
if (typeof process !== 'undefined' && process.env) {
  // 从环境变量读取配置（如果存在）
  REDIS_CONFIG.host = process.env.REDIS_HOST || REDIS_CONFIG.host
  REDIS_CONFIG.port = parseInt(process.env.REDIS_PORT) || REDIS_CONFIG.port
  REDIS_CONFIG.password = process.env.REDIS_PASSWORD || REDIS_CONFIG.password
  REDIS_CONFIG.db = parseInt(process.env.REDIS_DB) || REDIS_CONFIG.db
}

/**
 * 获取Redis连接配置
 * @returns {Object} Redis连接配置对象
 */
export const getRedisConfig = () => {
  return { ...REDIS_CONFIG }
}

/**
 * 获取完整的Redis连接URL
 * @returns {string} Redis连接URL
 */
export const getRedisUrl = () => {
  const config = getRedisConfig()
  const auth = config.password ? `:${config.password}@` : ''
  return `redis://${auth}${config.host}:${config.port}/${config.db}`
}

/**
 * 生成Redis键名
 * @param {string} type - 键类型 (session, user, backup等)
 * @param {string} identifier - 标识符
 * @returns {string} 完整的Redis键名
 */
export const generateRedisKey = (type, identifier) => {
  const config = getRedisConfig()
  const typePrefix = config[`${type}KeyPrefix`] || `${type}:`
  return `${config.keyPrefix}${typePrefix}${identifier}`
}

/**
 * 解析Redis键名
 * @param {string} key - Redis键名
 * @returns {Object} 解析结果 {type, identifier}
 */
export const parseRedisKey = (key) => {
  const config = getRedisConfig()
  
  if (!key.startsWith(config.keyPrefix)) {
    return null
  }
  
  const withoutPrefix = key.substring(config.keyPrefix.length)
  
  // 尝试匹配不同类型的键
  for (const [type, prefix] of Object.entries({
    session: config.sessionKeyPrefix,
    user: config.userKeyPrefix
  })) {
    if (withoutPrefix.startsWith(prefix)) {
      return {
        type,
        identifier: withoutPrefix.substring(prefix.length)
      }
    }
  }
  
  return {
    type: 'unknown',
    identifier: withoutPrefix
  }
}

/**
 * 获取发布订阅频道名
 * @param {string} channelType - 频道类型
 * @returns {string} 完整的频道名
 */
export const getChannelName = (channelType) => {
  const config = getRedisConfig()
  return config.channels[channelType] || channelType
}

/**
 * 验证Redis配置
 * @returns {Object} 验证结果 {valid, errors}
 */
export const validateRedisConfig = () => {
  const config = getRedisConfig()
  const errors = []
  
  // 检查必需的配置项
  if (!config.host) {
    errors.push('Redis主机地址不能为空')
  }
  
  if (!config.port || config.port < 1 || config.port > 65535) {
    errors.push('Redis端口必须在1-65535范围内')
  }
  
  if (config.db < 0 || config.db > 15) {
    errors.push('Redis数据库编号必须在0-15范围内')
  }
  
  if (config.connectTimeout < 1000) {
    errors.push('连接超时时间不能少于1000毫秒')
  }
  
  if (config.sessionTTL < 3600) {
    errors.push('会话过期时间不能少于1小时')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 获取Redis连接选项（用于创建连接）
 * @returns {Object} Redis连接选项
 */
export const getRedisConnectionOptions = () => {
  const config = getRedisConfig()
  
  return {
    host: config.host,
    port: config.port,
    password: config.password,
    db: config.db,
    connectTimeout: config.connectTimeout,
    lazyConnect: config.lazyConnect,
    retryDelayOnFailover: config.retryDelayOnFailover,
    maxRetriesPerRequest: config.maxRetriesPerRequest,
    family: config.family,
    keepAlive: config.keepAlive
  }
}

// 导出配置常量
export { REDIS_CONFIG }

// 默认导出配置获取函数
export default getRedisConfig
