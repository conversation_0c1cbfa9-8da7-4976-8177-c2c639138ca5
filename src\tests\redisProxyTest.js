/**
 * Redis代理功能测试
 * 用于验证Redis代理配置是否正常工作
 */

import { getServerConfig, getRedisProxyUrl, getAvailableRedisProxyUrl, checkRedisProxyAvailability } from '../config/serverConfig.js'
import { getRedisAdapter } from '../services/redisHttpAdapter.js'
import { getRedisDiagnostics } from '../utils/redisDiagnostics.js'

/**
 * 测试Redis代理配置
 */
export async function testRedisProxyConfig() {
  console.log('🧪 开始测试Redis代理配置...')
  
  try {
    // 1. 测试服务器配置
    console.log('\n1️⃣ 测试服务器配置:')
    const config = getServerConfig()
    console.log('Redis代理配置:', config.redisProxy)
    
    // 2. 测试URL生成
    console.log('\n2️⃣ 测试URL生成:')
    const proxyUrl = getRedisProxyUrl(true)  // 使用代理
    const directUrl = getRedisProxyUrl(false) // 直连
    console.log('代理URL:', proxyUrl)
    console.log('直连URL:', directUrl)

    // 3. 测试可用性检测
    console.log('\n3️⃣ 测试可用性检测:')
    const availableUrl = await getAvailableRedisProxyUrl() // 强制使用代理
    console.log('可用URL:', availableUrl)
    
    // 4. 测试Redis适配器
    console.log('\n4️⃣ 测试Redis适配器:')
    const redisAdapter = getRedisAdapter()
    await redisAdapter.updateRedisUrl()
    console.log('Redis适配器URL:', redisAdapter.baseURL)
    
    // 5. 测试连接
    console.log('\n5️⃣ 测试Redis连接:')
    const pingResult = await redisAdapter.ping()
    console.log('Redis PING结果:', pingResult)
    
    console.log('\n✅ Redis代理配置测试完成')
    return true
    
  } catch (error) {
    console.error('❌ Redis代理配置测试失败:', error)
    return false
  }
}

/**
 * 测试代理连接
 */
export async function testProxyConnection() {
  console.log('🔗 测试Redis代理连接...')
  
  try {
    const response = await fetch('/redis/ping', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    if (response.ok) {
      const data = await response.text()
      if (data === 'PONG') {
        console.log('✅ Redis代理连接成功:', data)
        return true
      } else {
        console.error('❌ Redis代理响应异常:', data)
        return false
      }
    } else {
      console.error('❌ Redis代理连接失败:', response.status, response.statusText)
      return false
    }
  } catch (error) {
    console.error('❌ Redis代理连接异常:', error)
    return false
  }
}

/**
 * 测试直连代理服务器
 */
export async function testDirectProxyConnection() {
  console.log('🔗 测试直连Redis代理服务器...')
  
  try {
    const config = getServerConfig()
    const { protocol, host, port } = config.redisProxy
    const url = `${protocol}//${host}:${port}/redis/ping`
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    if (response.ok) {
      const data = await response.text()
      if (data === 'PONG') {
        console.log('✅ 直连Redis代理服务器成功:', data)
        return true
      } else {
        console.error('❌ 直连Redis代理服务器响应异常:', data)
        return false
      }
    } else {
      console.error('❌ 直连Redis代理服务器失败:', response.status, response.statusText)
      return false
    }
  } catch (error) {
    console.error('❌ 直连Redis代理服务器异常:', error)
    return false
  }
}

/**
 * 测试Redis诊断功能
 */
export async function testRedisDiagnostics() {
  console.log('🔍 测试Redis诊断功能...')
  
  try {
    const diagnostics = getRedisDiagnostics()
    
    // 运行完整诊断
    await diagnostics.runFullDiagnostics()
    
    // 获取诊断结果
    const results = diagnostics.getDiagnosticResults()
    
    console.log('📊 诊断结果:')
    results.forEach(result => {
      console.log(`${result.success ? '✅' : '❌'} ${result.test}`)
      if (result.details.length > 0) {
        result.details.forEach(detail => console.log(`  ${detail}`))
      }
      if (result.errors.length > 0) {
        result.errors.forEach(error => console.log(`  ${error}`))
      }
    })
    
    const allPassed = results.every(result => result.success)
    console.log(`\n总体诊断结果: ${allPassed ? '✅ 全部通过' : '❌ 部分失败'}`)
    
    return allPassed
  } catch (error) {
    console.error('❌ Redis诊断测试失败:', error)
    return false
  }
}

/**
 * 测试Redis基本操作
 */
export async function testRedisOperations() {
  console.log('⚙️ 测试Redis基本操作...')
  
  try {
    const redisAdapter = getRedisAdapter()
    
    // 测试设置和获取
    const testKey = 'test:proxy:' + Date.now()
    const testValue = 'Redis代理测试值'
    
    console.log('设置测试键值...')
    await redisAdapter.set(testKey, testValue, 60) // 60秒过期
    
    console.log('获取测试键值...')
    const retrievedValue = await redisAdapter.get(testKey)
    
    if (retrievedValue === testValue) {
      console.log('✅ Redis基本操作测试成功')
      
      // 清理测试数据
      await redisAdapter.del(testKey)
      console.log('🧹 测试数据已清理')
      
      return true
    } else {
      console.error('❌ Redis基本操作测试失败: 值不匹配')
      console.error('期望值:', testValue)
      console.error('实际值:', retrievedValue)
      return false
    }
  } catch (error) {
    console.error('❌ Redis基本操作测试异常:', error)
    return false
  }
}

/**
 * 运行所有Redis代理测试
 */
export async function runAllRedisProxyTests() {
  console.log('🚀 开始运行所有Redis代理测试...')
  
  const results = {
    config: false,
    proxy: false,
    direct: false,
    diagnostics: false,
    operations: false
  }
  
  // 测试配置
  results.config = await testRedisProxyConfig()
  
  // 测试代理连接
  results.proxy = await testProxyConnection()
  
  // 测试直连
  results.direct = await testDirectProxyConnection()
  
  // 测试诊断
  results.diagnostics = await testRedisDiagnostics()
  
  // 测试基本操作
  if (results.proxy || results.direct) {
    results.operations = await testRedisOperations()
  }
  
  console.log('\n📊 测试结果汇总:')
  console.log('配置测试:', results.config ? '✅ 通过' : '❌ 失败')
  console.log('代理连接:', results.proxy ? '✅ 通过' : '❌ 失败')
  console.log('直连代理:', results.direct ? '✅ 通过' : '❌ 失败')
  console.log('诊断功能:', results.diagnostics ? '✅ 通过' : '❌ 失败')
  console.log('基本操作:', results.operations ? '✅ 通过' : '❌ 失败')
  
  const allPassed = Object.values(results).every(result => result)
  console.log('\n总体结果:', allPassed ? '✅ 全部通过' : '❌ 部分失败')
  
  return results
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中，将测试函数挂载到全局对象
  window.redisProxyTests = {
    testRedisProxyConfig,
    testProxyConnection,
    testDirectProxyConnection,
    testRedisDiagnostics,
    testRedisOperations,
    runAllRedisProxyTests
  }
  
  console.log('📝 Redis代理测试函数已加载，可以在控制台中调用:')
  console.log('- window.redisProxyTests.runAllRedisProxyTests() // 运行所有测试')
  console.log('- window.redisProxyTests.testProxyConnection() // 测试代理连接')
  console.log('- window.redisProxyTests.testDirectProxyConnection() // 测试直连')
  console.log('- window.redisProxyTests.testRedisDiagnostics() // 测试诊断功能')
}
