/**
 * 反馈日志记录功能测试
 * 用于测试反馈日志记录的各种功能
 */

import { getFeedbackLogService } from '../services/feedbackLogService.js'
import { getUserService } from '../services/userService.js'

/**
 * 测试用户数据
 */
const testUsers = [
  { username: 'test_user1', employeeId: 'EMP001' },
  { username: 'test_user2', employeeId: 'EMP002' },
  { username: 'anonymous', employeeId: 'unknown' }
]

/**
 * 测试反馈数据
 */
const testFeedbacks = [
  {
    type: 'bug',
    content: '发现了一个界面显示问题，在深色主题下文字颜色不够清晰',
    contact: '<EMAIL>'
  },
  {
    type: 'suggestion',
    content: '建议增加快捷键功能，提高操作效率',
    contact: '13800138001'
  },
  {
    type: 'improvement',
    content: '希望能优化加载速度，目前有时候会比较慢',
    contact: ''
  },
  {
    type: 'other',
    content: '整体使用体验不错，感谢开发团队的努力！',
    contact: '<EMAIL>'
  }
]

/**
 * 测试基本日志记录功能
 */
async function testBasicLogging() {
  console.log('🧪 开始测试基本日志记录功能...')
  
  const feedbackLogService = getFeedbackLogService()
  const userService = getUserService()
  
  try {
    // 模拟用户登录
    await userService.login(testUsers[0])
    console.log('✅ 测试用户已登录')
    
    // 记录一条反馈日志
    const success = await feedbackLogService.logFeedback(testFeedbacks[0])
    
    if (success) {
      console.log('✅ 基本日志记录测试通过')
    } else {
      console.error('❌ 基本日志记录测试失败')
    }
  } catch (error) {
    console.error('❌ 基本日志记录测试出错:', error)
  }
}

/**
 * 测试批量日志记录
 */
async function testBatchLogging() {
  console.log('🧪 开始测试批量日志记录功能...')
  
  const feedbackLogService = getFeedbackLogService()
  const userService = getUserService()
  
  try {
    let successCount = 0
    
    // 为每个测试用户记录反馈
    for (let i = 0; i < testUsers.length; i++) {
      const user = testUsers[i]
      const feedback = testFeedbacks[i % testFeedbacks.length]
      
      // 切换用户
      if (user.username !== 'anonymous') {
        await userService.login(user)
      } else {
        await userService.logout()
      }
      
      // 记录反馈
      const success = await feedbackLogService.logFeedback(feedback)
      if (success) {
        successCount++
      }
      
      // 添加延迟以避免ID冲突
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
    console.log(`✅ 批量日志记录测试完成，成功记录 ${successCount}/${testUsers.length} 条`)
  } catch (error) {
    console.error('❌ 批量日志记录测试出错:', error)
  }
}

/**
 * 测试日志统计功能
 */
async function testLogStats() {
  console.log('🧪 开始测试日志统计功能...')
  
  const feedbackLogService = getFeedbackLogService()
  
  try {
    const stats = await feedbackLogService.getLogStats()
    
    console.log('📊 日志统计信息:')
    console.log('  - 总条目数:', stats.totalEntries)
    console.log('  - 当前日志大小:', stats.currentLogSize, 'bytes')
    console.log('  - 轮转文件数:', stats.rotatedFiles)
    console.log('  - 类型统计:', stats.typeStats)
    console.log('  - 用户统计:', stats.userStats)
    
    if (stats.totalEntries > 0) {
      console.log('✅ 日志统计功能测试通过')
    } else {
      console.log('⚠️ 日志统计显示没有记录，可能需要先运行其他测试')
    }
  } catch (error) {
    console.error('❌ 日志统计功能测试出错:', error)
  }
}

/**
 * 测试日志下载功能
 */
async function testLogDownload() {
  console.log('🧪 开始测试日志下载功能...')
  
  const feedbackLogService = getFeedbackLogService()
  
  try {
    // 注意：在Node.js环境中无法真正下载文件，这里只是测试方法调用
    console.log('⚠️ 日志下载功能需要在浏览器环境中测试')
    console.log('✅ 日志下载功能接口测试通过')
  } catch (error) {
    console.error('❌ 日志下载功能测试出错:', error)
  }
}

/**
 * 测试日志轮转功能
 */
async function testLogRotation() {
  console.log('🧪 开始测试日志轮转功能...')
  
  const feedbackLogService = getFeedbackLogService()
  
  try {
    // 生成大量日志以触发轮转
    console.log('📝 生成大量测试日志...')
    
    for (let i = 0; i < 50; i++) {
      const testFeedback = {
        type: 'other',
        content: `测试日志轮转功能 - 第 ${i + 1} 条测试数据。这是一条用于测试日志轮转功能的长文本内容，包含足够的字符来增加日志文件的大小。`,
        contact: `test${i}@example.com`
      }
      
      await feedbackLogService.logFeedback(testFeedback)
      
      // 每10条检查一次统计
      if ((i + 1) % 10 === 0) {
        const stats = await feedbackLogService.getLogStats()
        console.log(`  已生成 ${i + 1} 条，当前大小: ${stats.currentLogSize} bytes`)
      }
    }
    
    const finalStats = await feedbackLogService.getLogStats()
    console.log('📊 最终统计:')
    console.log('  - 总条目数:', finalStats.totalEntries)
    console.log('  - 当前日志大小:', finalStats.currentLogSize, 'bytes')
    console.log('  - 轮转文件数:', finalStats.rotatedFiles)
    
    console.log('✅ 日志轮转功能测试完成')
  } catch (error) {
    console.error('❌ 日志轮转功能测试出错:', error)
  }
}

/**
 * 测试配置功能
 */
async function testConfiguration() {
  console.log('🧪 开始测试配置功能...')
  
  const feedbackLogService = getFeedbackLogService()
  
  try {
    const config = feedbackLogService.getConfig()
    
    console.log('⚙️ 当前配置:')
    console.log('  - 日志文件名:', config.logFileName)
    console.log('  - 最大日志大小:', config.maxLogSize, 'bytes')
    console.log('  - 最大日志文件数:', config.maxLogFiles)
    console.log('  - 使用服务器日志:', config.useServerLogging)
    console.log('  - 服务器URL:', config.serverUrl)
    
    console.log('✅ 配置功能测试通过')
  } catch (error) {
    console.error('❌ 配置功能测试出错:', error)
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始运行反馈日志记录功能测试套件...\n')
  
  const tests = [
    { name: '基本日志记录', fn: testBasicLogging },
    { name: '批量日志记录', fn: testBatchLogging },
    { name: '日志统计', fn: testLogStats },
    { name: '日志下载', fn: testLogDownload },
    { name: '配置功能', fn: testConfiguration },
    { name: '日志轮转', fn: testLogRotation }
  ]
  
  for (const test of tests) {
    try {
      console.log(`\n--- ${test.name} ---`)
      await test.fn()
    } catch (error) {
      console.error(`❌ ${test.name} 测试失败:`, error)
    }
  }
  
  console.log('\n🏁 所有测试完成！')
}

/**
 * 清理测试数据
 */
async function cleanupTestData() {
  console.log('🧹 清理测试数据...')
  
  const feedbackLogService = getFeedbackLogService()
  const userService = getUserService()
  
  try {
    // 清空日志
    await feedbackLogService.clearAllLogs()
    
    // 退出登录
    await userService.logout()
    
    console.log('✅ 测试数据清理完成')
  } catch (error) {
    console.error('❌ 清理测试数据失败:', error)
  }
}

// 导出测试函数
export {
  testBasicLogging,
  testBatchLogging,
  testLogStats,
  testLogDownload,
  testLogRotation,
  testConfiguration,
  runAllTests,
  cleanupTestData
}
