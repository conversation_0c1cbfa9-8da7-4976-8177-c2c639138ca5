<template>
  <!-- 简化水印组件 - 仅显示水印，配置在代码中 -->
  <div class="watermark-container" ref="watermarkContainer">
    <!-- 插槽内容 -->
    <slot></slot>
    
    <!-- 水印层 -->
    <div 
      v-if="config && config.enabled"
      class="watermark-layer"
      ref="watermarkLayer"
    ></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, defineProps } from 'vue'
import { getWatermarkConfig, getDynamicWatermarkConfig, isWatermarkEnabled } from '../../config/watermark.js'
import { getUserService } from '../../services/userService.js'

// 组件属性
const props = defineProps({
  // 模块名称
  module: {
    type: String,
    required: true,
    validator: (value) => ['knowledge', 'business', 'function'].includes(value)
  }
})

// 响应式数据
const watermarkContainer = ref(null)
const watermarkLayer = ref(null)
const config = ref(null)

// 防篡改相关
let tamperCheckInterval = null
let mutationObserver = null

/**
 * 初始化水印配置（使用动态水印）
 */
const initWatermarkConfig = async () => {
  try {
    // 优先使用动态水印配置
    config.value = await getDynamicWatermarkConfig(props.module)
    if (!config.value) {
      console.warn(`无法获取模块 ${props.module} 的动态水印配置，使用静态配置`)
      // 降级到静态配置
      config.value = getWatermarkConfig(props.module)
    }
    console.log(`${props.module} 模块动态水印配置加载:`, config.value)
  } catch (error) {
    console.error(`获取动态水印配置失败:`, error)
    // 降级到静态配置
    config.value = getWatermarkConfig(props.module)
    console.log(`${props.module} 模块使用静态水印配置:`, config.value)
  }
}

/**
 * 生成水印
 */
const generateWatermark = () => {
  if (!watermarkLayer.value || !config.value || !config.value.enabled) {
    return
  }
  
  const container = watermarkContainer.value
  if (!container) return
  
  // 清除现有水印
  watermarkLayer.value.innerHTML = ''
  
  // 根据模式生成水印
  switch (config.value.mode) {
    case 'repeat':
      generateRepeatWatermark()
      break
    case 'center':
      generateCenterWatermark()
      break
    case 'corner':
      generateCornerWatermark()
      break
    default:
      generateRepeatWatermark()
  }
}

/**
 * 生成平铺水印
 */
const generateRepeatWatermark = () => {
  const container = watermarkContainer.value
  const layer = watermarkLayer.value
  
  const containerRect = container.getBoundingClientRect()
  const cols = Math.ceil(containerRect.width / config.value.gapX) + 1
  const rows = Math.ceil(containerRect.height / config.value.gapY) + 1
  
  for (let i = 0; i < rows; i++) {
    for (let j = 0; j < cols; j++) {
      const watermarkItem = createWatermarkItem()
      watermarkItem.style.cssText += `
        position: absolute;
        left: ${j * config.value.gapX}px;
        top: ${i * config.value.gapY}px;
      `
      layer.appendChild(watermarkItem)
    }
  }
}

/**
 * 生成居中水印
 */
const generateCenterWatermark = () => {
  const layer = watermarkLayer.value
  const watermarkItem = createWatermarkItem()
  
  watermarkItem.style.cssText += `
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(${config.value.rotate}deg);
    font-size: ${config.value.fontSize * 2}px;
    font-weight: 600;
  `
  layer.appendChild(watermarkItem)
}

/**
 * 生成角落水印
 */
const generateCornerWatermark = () => {
  const layer = watermarkLayer.value
  const positions = [
    { top: '20px', left: '20px' },
    { top: '20px', right: '20px' },
    { bottom: '20px', left: '20px' },
    { bottom: '20px', right: '20px' }
  ]
  
  positions.forEach(pos => {
    const watermarkItem = createWatermarkItem()
    
    let positionStyle = 'position: absolute;'
    Object.entries(pos).forEach(([key, value]) => {
      positionStyle += `${key}: ${value};`
    })
    
    watermarkItem.style.cssText += positionStyle
    layer.appendChild(watermarkItem)
  })
}

/**
 * 创建水印元素
 */
const createWatermarkItem = () => {
  const watermarkItem = document.createElement('div')
  watermarkItem.className = 'watermark-item'
  watermarkItem.textContent = config.value.text
  
  // 基础样式
  let itemStyle = `
    font-size: ${config.value.fontSize}px;
    color: ${config.value.color};
    opacity: ${config.value.opacity};
    transform: rotate(${config.value.rotate}deg);
    pointer-events: none;
    user-select: none;
    white-space: nowrap;
    font-weight: ${config.value.fontWeight || '500'};
    font-family: ${config.value.fontFamily || 'Arial, sans-serif'};
    z-index: ${config.value.zIndex || 1};
  `
  
  // 添加动画
  if (config.value.animated) {
    const animationDurations = {
      slow: '30s',
      normal: '20s',
      fast: '10s'
    }
    const duration = animationDurations[config.value.animationSpeed] || '20s'
    itemStyle += `animation: watermarkFloat ${duration} ease-in-out infinite;`
  }
  
  watermarkItem.style.cssText = itemStyle
  
  // 添加防篡改属性
  watermarkItem.setAttribute('data-watermark', 'true')
  watermarkItem.setAttribute('data-module', props.module)
  
  return watermarkItem
}

/**
 * 启动防篡改检查
 */
const startTamperProtection = () => {
  if (!config.value?.antiTamper?.enabled) return
  
  // 定时检查水印是否被移除
  tamperCheckInterval = setInterval(() => {
    if (!watermarkLayer.value || watermarkLayer.value.children.length === 0) {
      console.warn(`${props.module} 模块水印被篡改，正在重新生成...`)
      setTimeout(generateWatermark, config.value.antiTamper.regenerateDelay || 100)
    }
  }, config.value.antiTamper.checkInterval || 3000)
  
  // 监听DOM变化
  if (window.MutationObserver && watermarkLayer.value) {
    mutationObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.removedNodes.length > 0) {
          // 检查是否有水印元素被移除
          const removedWatermarks = Array.from(mutation.removedNodes).some(node => 
            node.nodeType === Node.ELEMENT_NODE && 
            node.getAttribute('data-watermark') === 'true'
          )
          
          if (removedWatermarks) {
            console.warn(`${props.module} 模块水印被篡改，正在重新生成...`)
            setTimeout(generateWatermark, config.value.antiTamper.regenerateDelay || 100)
          }
        }
      })
    })
    
    mutationObserver.observe(watermarkLayer.value, {
      childList: true,
      subtree: true
    })
  }
}

/**
 * 停止防篡改检查
 */
const stopTamperProtection = () => {
  if (tamperCheckInterval) {
    clearInterval(tamperCheckInterval)
    tamperCheckInterval = null
  }
  
  if (mutationObserver) {
    mutationObserver.disconnect()
    mutationObserver = null
  }
}

/**
 * 刷新水印（重新获取动态配置并重新生成）
 */
const refreshWatermark = async () => {
  try {
    await initWatermarkConfig()
    if (isWatermarkEnabled(props.module)) {
      generateWatermark()
    }
  } catch (error) {
    console.error('刷新水印失败:', error)
  }
}

// 暴露方法给父组件
defineExpose({
  refreshWatermark
})

// 监听容器大小变化
let resizeObserver = null

onMounted(async () => {
  // 初始化配置（异步）
  await initWatermarkConfig()

  // 生成水印
  if (isWatermarkEnabled(props.module)) {
    generateWatermark()
    startTamperProtection()
  }

  // 监听容器大小变化
  if (watermarkContainer.value && window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      if (isWatermarkEnabled(props.module)) {
        generateWatermark()
      }
    })
    resizeObserver.observe(watermarkContainer.value)
  }
})

onUnmounted(() => {
  stopTamperProtection()
  
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
})

// 监听模块变化，重新生成水印
watch(() => props.module, async () => {
  await initWatermarkConfig()
  if (isWatermarkEnabled(props.module)) {
    generateWatermark()
  }
})
</script>

<style scoped>
/* 水印容器 */
.watermark-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 水印层 */
.watermark-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  user-select: none;
  z-index: 1;
  overflow: hidden;
}

/* 水印动画 */
@keyframes watermarkFloat {
  0%, 100% {
    transform: translateY(0px) rotate(var(--rotate, -15deg));
  }
  50% {
    transform: translateY(-10px) rotate(var(--rotate, -15deg));
  }
}

/* 水印项目 */
:deep(.watermark-item) {
  --rotate: -15deg;
}
</style>
