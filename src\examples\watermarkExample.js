/**
 * 水印使用示例
 * 演示如何使用动态水印功能
 */

import { 
  generateDynamicWatermarkText, 
  getDynamicWatermarkConfig, 
  getWatermarkConfig,
  refreshAllDynamicWatermarks 
} from '../config/watermark.js'
import { getUserService } from '../services/userService.js'

/**
 * 示例：获取知识中心的动态水印配置
 */
export async function getKnowledgeWatermarkExample() {
  try {
    console.log('=== 获取知识中心动态水印配置示例 ===')
    
    // 获取动态水印配置（包含用户信息和时间）
    const dynamicConfig = await getDynamicWatermarkConfig('knowledge')
    console.log('动态水印配置:', dynamicConfig)
    
    // 获取静态水印配置（使用默认文本）
    const staticConfig = getWatermarkConfig('knowledge')
    console.log('静态水印配置:', staticConfig)
    
    return { dynamicConfig, staticConfig }
  } catch (error) {
    console.error('获取水印配置失败:', error)
    throw error
  }
}

/**
 * 示例：生成各模块的动态水印文本
 */
export async function generateAllWatermarkTextsExample() {
  try {
    console.log('=== 生成所有模块动态水印文本示例 ===')
    
    const modules = [
      { name: 'knowledge', prefix: '知识中心' },
      { name: 'business', prefix: '业务域' },
      { name: 'function', prefix: '职能域' }
    ]
    
    const results = []
    for (const module of modules) {
      const text = await generateDynamicWatermarkText(module.prefix)
      results.push({ module: module.name, text })
      console.log(`${module.name} 水印文本:`, text)
    }
    
    return results
  } catch (error) {
    console.error('生成水印文本失败:', error)
    throw error
  }
}

/**
 * 示例：模拟用户登录后刷新水印
 */
export async function simulateUserLoginAndRefreshWatermark() {
  try {
    console.log('=== 模拟用户登录并刷新水印示例 ===')
    
    const userService = getUserService()
    
    // 模拟用户登录
    const userData = {
      username: '张三',
      employeeId: 'EMP001'
    }
    
    console.log('模拟用户登录:', userData)
    await userService.login(userData)
    
    // 刷新所有动态水印
    console.log('刷新所有动态水印...')
    const refreshResults = await refreshAllDynamicWatermarks()
    
    console.log('水印刷新结果:', refreshResults)
    return refreshResults
  } catch (error) {
    console.error('模拟登录和刷新水印失败:', error)
    throw error
  }
}

/**
 * 示例：在用户未登录时获取水印
 */
export async function getWatermarkWhenNotLoggedIn() {
  try {
    console.log('=== 用户未登录时的水印示例 ===')
    
    const userService = getUserService()
    
    // 确保用户已退出登录
    await userService.logout()
    
    // 生成水印文本
    const knowledgeText = await generateDynamicWatermarkText('知识中心')
    const businessText = await generateDynamicWatermarkText('业务域')
    const functionText = await generateDynamicWatermarkText('职能域')
    
    console.log('知识中心水印（未登录）:', knowledgeText)
    console.log('业务域水印（未登录）:', businessText)
    console.log('职能域水印（未登录）:', functionText)
    
    return {
      knowledge: knowledgeText,
      business: businessText,
      function: functionText
    }
  } catch (error) {
    console.error('获取未登录水印失败:', error)
    throw error
  }
}

/**
 * 运行所有示例
 */
export async function runAllExamples() {
  try {
    console.log('开始运行水印示例...\n')
    
    // 示例1：用户未登录时的水印
    await getWatermarkWhenNotLoggedIn()
    console.log('\n' + '='.repeat(50) + '\n')
    
    // 示例2：模拟用户登录并刷新水印
    await simulateUserLoginAndRefreshWatermark()
    console.log('\n' + '='.repeat(50) + '\n')
    
    // 示例3：获取知识中心水印配置
    await getKnowledgeWatermarkExample()
    console.log('\n' + '='.repeat(50) + '\n')
    
    // 示例4：生成所有模块水印文本
    await generateAllWatermarkTextsExample()
    
    console.log('\n所有水印示例运行完成！')
  } catch (error) {
    console.error('运行示例失败:', error)
  }
}

// 如果直接运行此文件，则执行所有示例
if (typeof window !== 'undefined' && window.location) {
  // 在浏览器环境中，可以通过控制台调用
  window.watermarkExamples = {
    runAllExamples,
    getKnowledgeWatermarkExample,
    generateAllWatermarkTextsExample,
    simulateUserLoginAndRefreshWatermark,
    getWatermarkWhenNotLoggedIn
  }
  console.log('水印示例已加载，可通过 window.watermarkExamples 访问')
}
